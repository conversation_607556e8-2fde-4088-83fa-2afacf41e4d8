-- =====================================================
-- Python股票技术分析系统MySQL建表脚本
-- 对应Oracle GP_ANALYSE_PKG包的所有相关表结构
-- =====================================================

-- 1. 股票基本信息表 (对应Oracle GP_INFO_T)
CREATE TABLE IF NOT EXISTS stock_list (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    stock_code VARCHAR(10) NOT NULL COMMENT '股票代码',
    stock_name VARCHAR(100) COMMENT '股票名称', 
    exchange VARCHAR(10) NOT NULL COMMENT '交易所(sh/sz/hk/us)',
    sector VARCHAR(50) COMMENT '板块',
    list_date DATE COMMENT '上市日期',
    industry_l1 VARCHAR(100) COMMENT '申万一级行业',
    industry_l2 VARCHAR(100) COMMENT '申万二级行业',
    industry_l3 VARCHAR(100) COMMENT '申万三级行业',
    concept TEXT COMMENT '所涉概念',
    controller_type VARCHAR(50) COMMENT '控制人性质',
    market_cap DECIMAL(15,4) COMMENT '市值',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_stock_exchange (stock_code, exchange),
    KEY idx_stock_code (stock_code),
    KEY idx_exchange (exchange),
    KEY idx_sector (sector)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='股票基本信息';

-- 2. 股票日线数据表 (对应Oracle GP_JY_D)
CREATE TABLE IF NOT EXISTS stock_daily_data (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    stock_code VARCHAR(10) NOT NULL COMMENT '股票代码',
    trade_date DATE NOT NULL COMMENT '交易日期',
    open_price DECIMAL(10,4) COMMENT '开盘价',
    high_price DECIMAL(10,4) COMMENT '最高价', 
    close_price DECIMAL(10,4) COMMENT '收盘价',
    low_price DECIMAL(10,4) COMMENT '最低价',
    volume BIGINT COMMENT '交易量',
    amount DECIMAL(15,4) COMMENT '交易额',
    adj_factor DECIMAL(10,6) COMMENT '复权因子',
    change_amount DECIMAL(10,4) COMMENT '涨跌额',
    change_rate DECIMAL(8,4) COMMENT '涨跌幅(%)',
    amplitude DECIMAL(8,4) COMMENT '振幅(%)',
    turnover_rate DECIMAL(8,4) COMMENT '换手率(%)',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_stock_date (stock_code, trade_date),
    KEY idx_stock_code (stock_code),
    KEY idx_trade_date (trade_date),
    KEY idx_stock_date_range (stock_code, trade_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='股票日线数据';

-- 3. 股票周线数据表 (对应Oracle GP_JY_W)
CREATE TABLE IF NOT EXISTS stock_weekly_data (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    stock_code VARCHAR(10) NOT NULL COMMENT '股票代码 (对应Oracle gp_num)',
    trade_date DATE NOT NULL COMMENT '交易日期 (对应Oracle jy_date)',
    week_num VARCHAR(20) COMMENT '周编号 (对应Oracle week_num)',
    open_price DECIMAL(10,4) COMMENT '开盘价 (对应Oracle open_price)',
    high_price DECIMAL(10,4) COMMENT '最高价 (对应Oracle high_price)',
    close_price DECIMAL(10,4) COMMENT '收盘价 (对应Oracle close_price)', 
    low_price DECIMAL(10,4) COMMENT '最低价 (对应Oracle low_price)',
    jy_quantity BIGINT COMMENT '交易量 (对应Oracle jy_quantity)',
    jy_amount DECIMAL(15,4) COMMENT '交易额 (对应Oracle jy_amount)',
    change_amount DECIMAL(10,4) COMMENT '涨跌额',
    change_rate DECIMAL(8,4) COMMENT '涨跌幅(%)',
    amplitude DECIMAL(8,4) COMMENT '振幅(%)',
    turnover_rate DECIMAL(8,4) COMMENT '换手率(%)',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_stock_date (stock_code, trade_date),
    KEY idx_stock_code (stock_code),
    KEY idx_week_num (week_num),
    KEY idx_trade_date (trade_date),
    KEY idx_stock_week (stock_code, week_num)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='股票周线数据 (对应Oracle GP_JY_W)';

-- 4. 股票平均交易量数据表 (对应Oracle GP_AVG_WTRADE)
CREATE TABLE IF NOT EXISTS stock_avg_trade (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    stock_code VARCHAR(10) NOT NULL COMMENT '股票代码 (对应Oracle gp_num)',
    trade_date DATE NOT NULL COMMENT '交易日期 (对应Oracle gp_jy_date)',
    avg_days INT NOT NULL COMMENT '平均天数 (对应Oracle avg_days)',
    avg_qty DECIMAL(15,2) COMMENT '平均交易量 (对应Oracle avg_qty)',
    avg_amount DECIMAL(15,4) COMMENT '平均交易额 (对应Oracle avg_amount)',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_stock_date_days (stock_code, trade_date, avg_days),
    KEY idx_stock_code (stock_code),
    KEY idx_trade_date (trade_date),
    KEY idx_avg_days (avg_days)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='股票平均交易量数据 (对应Oracle GP_AVG_WTRADE)';

-- 5. 股票移动平均线数据表 (对应Oracle GP_AVG_JYW)
CREATE TABLE IF NOT EXISTS stock_moving_average (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    stock_code VARCHAR(10) NOT NULL COMMENT '股票代码 (对应Oracle gp_num)',
    week_num VARCHAR(20) NOT NULL COMMENT '周编号 (对应Oracle gp_week_num)',
    trade_date DATE NOT NULL COMMENT '交易日期 (对应Oracle gp_jy_date)',
    avg_months INT NOT NULL COMMENT '平均月数/周期数1-20 (对应Oracle avg_months)',
    avg_value DECIMAL(10,4) NOT NULL COMMENT '移动平均价格 (对应Oracle avg_value)',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间 (对应Oracle create_date)',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Oracle索引对应: GP_AVG_JYW_U1 on (GP_NUM, GP_JY_DATE, AVG_MONTHS)
    UNIQUE KEY uk_stock_date_months (stock_code, trade_date, avg_months),
    
    -- 其他优化索引
    KEY idx_stock_code (stock_code),
    KEY idx_trade_date (trade_date),
    KEY idx_avg_months (avg_months),
    KEY idx_week_num (week_num),
    KEY idx_stock_week (stock_code, week_num)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='股票移动平均线数据 (对应Oracle GP_AVG_JYW)';

-- 6. 股票波动率数据表 (对应Oracle GP_JY_D.attribute1/2字段)
CREATE TABLE IF NOT EXISTS stock_volatility (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    stock_code VARCHAR(10) NOT NULL COMMENT '股票代码',
    trade_date DATE NOT NULL COMMENT '交易日期',
    daily_tr DECIMAL(10,4) COMMENT '日真实波幅(True Range)',
    atr_5 DECIMAL(10,4) COMMENT '5日平均真实波幅',
    atr_10 DECIMAL(10,4) COMMENT '10日平均真实波幅',
    atr_20 DECIMAL(10,4) COMMENT '20日平均真实波幅',
    atr_30 DECIMAL(10,4) COMMENT '30日平均真实波幅',
    volatility_ratio DECIMAL(8,4) COMMENT '波动率',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_stock_date (stock_code, trade_date),
    KEY idx_stock_code (stock_code),
    KEY idx_trade_date (trade_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='股票波动率数据';

-- 7. 股票U型V型分析结果表 (对应Oracle GP_RESULT_T)
CREATE TABLE IF NOT EXISTS stock_analysis_result (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    batch_id BIGINT COMMENT '批次ID',
    tactics_code VARCHAR(20) DEFAULT 'UV_PATTERN' COMMENT '策略代码',
    stock_code VARCHAR(10) NOT NULL COMMENT '股票代码',
    stock_name VARCHAR(100) COMMENT '股票名称',
    
    -- U型形态数据
    u_left_date DATE COMMENT 'U型左侧高点日期',
    u_left_price DECIMAL(10,4) COMMENT 'U型左侧高点价格',
    u_right_date DATE COMMENT 'U型右侧高点日期', 
    u_right_price DECIMAL(10,4) COMMENT 'U型右侧高点价格',
    u_lowest DECIMAL(10,4) COMMENT 'U型最低点价格',
    u_lowest_date DATE COMMENT 'U型最低点日期',
    u_low1_price DECIMAL(10,4) COMMENT 'U型1/3最低价格',
    u_low1_date DATE COMMENT 'U型1/3最低日期',
    u_low2_price DECIMAL(10,4) COMMENT 'U型1/4最低价格',
    u_low2_date DATE COMMENT 'U型1/4最低日期',
    u_period_days INT COMMENT 'U型周期天数',
    u_decline_rate DECIMAL(8,4) COMMENT 'U型最大跌幅(%)',
    
    -- V型形态数据  
    v_right_date DATE COMMENT 'V型右侧高点日期',
    v_right_price DECIMAL(10,4) COMMENT 'V型右侧高点价格',
    v_lowest DECIMAL(10,4) COMMENT 'V型最低点价格',
    v_lowest_date DATE COMMENT 'V型最低点日期',
    v_period_days INT COMMENT 'V型周期天数',
    v_decline_rate DECIMAL(8,4) COMMENT 'V型最大跌幅(%)',
    
    -- 移动平均线支撑
    ma_num INT COMMENT 'MA支撑线编号(1-20周)',
    ma_support_price DECIMAL(10,4) COMMENT 'MA支撑价格',
    
    -- 成功突破数据
    success_date DATE COMMENT '成功突破日期',
    success_price DECIMAL(10,4) COMMENT '成功突破价格',
    breakthrough_close DECIMAL(10,4) COMMENT '突破日收盘价',
    is_breakthrough VARCHAR(10) DEFAULT '未突破' COMMENT '是否突破(突破/未突破)',
    latest_breakthrough_date DATE COMMENT '最近突破日期',
    
    -- 统计数据
    total_turnover_rate DECIMAL(8,4) COMMENT '累计换手率',
    avg_turnover_rate DECIMAL(8,4) COMMENT '平均换手率',
    max_gain_rate DECIMAL(8,4) COMMENT '最大涨幅(%)',
    current_price DECIMAL(10,4) COMMENT '当前价格',
    current_gain_rate DECIMAL(8,4) COMMENT '当前涨幅(%)',
    
    -- 质量评级
    pattern_quality VARCHAR(10) COMMENT '形态质量(优秀/良好/一般)',
    risk_level VARCHAR(10) COMMENT '风险等级(低/中/高)',
    
    -- 扩展字段
    trend_direction VARCHAR(10) COMMENT '趋势方向(上升/下降/横盘)',
    volume_profile VARCHAR(20) COMMENT '成交量特征',
    market_environment VARCHAR(20) COMMENT '市场环境',
    
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    KEY idx_stock_code (stock_code),
    KEY idx_batch_id (batch_id),
    KEY idx_tactics_code (tactics_code),
    KEY idx_u_dates (u_left_date, u_right_date),
    KEY idx_v_date (v_right_date),
    KEY idx_success_date (success_date),
    KEY idx_breakthrough (is_breakthrough),
    KEY idx_pattern_quality (pattern_quality),
    KEY idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='股票U型V型分析结果';

-- 8. 股票股本信息表 (对应Oracle GP_GB_INFO，用于换手率计算)
CREATE TABLE IF NOT EXISTS stock_capital_info (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    stock_code VARCHAR(10) NOT NULL COMMENT '股票代码',
    change_date DATE NOT NULL COMMENT '变更日期',
    notice_date DATE COMMENT '公告日期',
    change_reason VARCHAR(100) COMMENT '变更原因',
    total_shares BIGINT COMMENT '总股本',
    float_shares BIGINT COMMENT '流通股本',
    restricted_shares BIGINT COMMENT '限售股本',
    management_shares BIGINT COMMENT '高管股本',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY uk_stock_change_date (stock_code, change_date),
    KEY idx_stock_code (stock_code),
    KEY idx_change_date (change_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='股票股本变更信息';

-- 9. 系统配置表
CREATE TABLE IF NOT EXISTS system_config (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    config_key VARCHAR(50) NOT NULL COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    config_desc VARCHAR(200) COMMENT '配置描述',
    config_type VARCHAR(20) DEFAULT 'STRING' COMMENT '配置类型(STRING/NUMBER/JSON)',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否启用',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_config_key (config_key)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统配置表';

-- 10. 分析任务日志表
CREATE TABLE IF NOT EXISTS analysis_task_log (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    batch_id BIGINT COMMENT '批次ID',
    task_name VARCHAR(100) NOT NULL COMMENT '任务名称',
    task_type VARCHAR(50) COMMENT '任务类型',
    stock_code VARCHAR(10) COMMENT '股票代码(可选)',
    start_time DATETIME COMMENT '开始时间',
    end_time DATETIME COMMENT '结束时间',
    duration_seconds INT COMMENT '执行时长(秒)',
    status VARCHAR(20) DEFAULT 'RUNNING' COMMENT '状态(RUNNING/SUCCESS/FAILED)',
    processed_count INT DEFAULT 0 COMMENT '处理数量',
    success_count INT DEFAULT 0 COMMENT '成功数量',
    error_count INT DEFAULT 0 COMMENT '错误数量',
    error_message TEXT COMMENT '错误信息',
    result_summary JSON COMMENT '结果摘要',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    KEY idx_batch_id (batch_id),
    KEY idx_task_name (task_name),
    KEY idx_status (status),
    KEY idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分析任务执行日志';

-- =====================================================
-- 初始化系统配置数据
-- =====================================================

-- 插入U型分析参数配置
INSERT INTO system_config (config_key, config_value, config_desc, config_type) VALUES
('U_PATTERN.PRE_MONTH', '10', 'U型前段周期', 'NUMBER'),
('U_PATTERN.LEFT_DIFF_RATE', '0.03', 'U型左侧高点涨幅限制', 'NUMBER'),
('U_PATTERN.LR_DIFF_RATE', '0.03', 'U型左右高点价差', 'NUMBER'),
('U_PATTERN.LL_LOW_DIFF', '0.12', 'U型底部下探下限', 'NUMBER'),
('U_PATTERN.LL_HIGH_DIFF', '0.50', 'U型底部下探上限', 'NUMBER'),
('U_PATTERN.LOW_PERIOD', '7', 'U型间隔最低周期', 'NUMBER'),
('U_PATTERN.HIGH_PERIOD', '6000', 'U型间隔最高周期', 'NUMBER'),
('U_PATTERN.VOLUME_AMPLIFY_RATE', '1.05', 'U型交易量放大倍数', 'NUMBER');

-- 插入V型分析参数配置  
INSERT INTO system_config (config_key, config_value, config_desc, config_type) VALUES
('V_PATTERN.LOW_PERIOD', '3', 'V型间隔最低周期', 'NUMBER'),
('V_PATTERN.LOW_DIFF', '0.15', 'V型底部下探限制', 'NUMBER'),
('V_PATTERN.LR_DIFF', '0.05', 'V型左侧验证阈值', 'NUMBER'),
('V_PATTERN.VOLUME_SHRINK_THRESHOLD', '0.5', 'V型交易量收缩阈值', 'NUMBER');

-- 插入交易量分析参数配置
INSERT INTO system_config (config_key, config_value, config_desc, config_type) VALUES
('VOLUME.AVG_DAYS', '[5,10,20,30]', '平均交易量计算天数', 'JSON'),
('VOLUME.FILTER_DAYS', '90', '交易量过滤天数', 'NUMBER');

-- 插入移动平均线参数配置
INSERT INTO system_config (config_key, config_value, config_desc, config_type) VALUES
('MA.MAX_PERIODS', '20', '移动平均线最大周期', 'NUMBER'),
('MA.MIN_PERIODS', '1', '移动平均线最小周期', 'NUMBER');

-- 插入波动率计算参数配置
INSERT INTO system_config (config_key, config_value, config_desc, config_type) VALUES
('VOLATILITY.ATR_PERIODS', '[5,10,20,30]', 'ATR计算周期', 'JSON'),
('VOLATILITY.MIN_HISTORY_DAYS', '20', '最小历史数据天数', 'NUMBER');

-- =====================================================
-- 创建索引优化查询性能
-- =====================================================

-- 为分析结果表创建复合索引
CREATE INDEX idx_analysis_pattern_date ON stock_analysis_result (stock_code, u_left_date, v_right_date);
CREATE INDEX idx_analysis_success_pattern ON stock_analysis_result (is_breakthrough, pattern_quality, success_date);

-- 为日线数据表创建时间范围查询索引  
CREATE INDEX idx_daily_data_time_range ON stock_daily_data (trade_date, stock_code);

-- 为周线数据表创建周期查询索引
CREATE INDEX idx_weekly_data_period ON stock_weekly_data (stock_code, week_num, trade_date);

-- =====================================================
-- 表结构说明
-- =====================================================

/*
核心表结构对应关系：
1. stock_list          ↔ Oracle GP_INFO_T
2. stock_daily_data    ↔ Oracle GP_JY_D  
3. stock_weekly_data   ↔ Oracle GP_JY_W
4. stock_avg_trade     ↔ Oracle GP_AVG_WTRADE
5. stock_moving_average ↔ Oracle GP_AVG_JYW
6. stock_volatility    ↔ Oracle GP_JY_D.attribute1/2 (独立表设计)
7. stock_analysis_result ↔ Oracle GP_RESULT_T
8. stock_capital_info  ↔ Oracle GP_GB_INFO

设计优势：
- 使用AUTO_INCREMENT主键提高插入性能
- 添加详细的字段注释便于维护
- 优化索引设计提高查询效率  
- 使用DECIMAL类型确保金融数据精度
- 添加创建/更新时间字段便于数据追踪
- 扩展字段设计便于功能迭代
*/ 