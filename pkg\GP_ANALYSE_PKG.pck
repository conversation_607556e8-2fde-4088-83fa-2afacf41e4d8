﻿CREATE OR REPLACE PACKAGE GPUSER.GP_ANALYSE_PKG IS

  -- AUTHOR  : XUE
  -- CREATED : 2014-6-3 14:06:56
  -- PURPOSE :
  PROCEDURE GET_URIGHT(P_ROWNUM NUMBER,
                       P_GPNUM  VARCHAR2,
                       P_LPRICE NUMBER,
                       P_RNUM   OUT NUMBER,
                       P_JYDATE OUT DATE,
                       P_HPRICE OUT NUMBER);

  PROCEDURE CHECK_RESULT;

  PROCEDURE GET_VRIGHT(P_GPNUM  VARCHAR2,
                       P_LNUM   NUMBER,
                       P_LPRICE NUMBER,
                       P_VRIGHT OUT NUMBER,
                       P_VRDATE OUT DATE,
                       P_VRNUM  OUT NUMBER);

  PROCEDURE CHECK_MA;

  function get_gbsum(p_gp_num varchar2, p_jy_date date) return number;

  procedure insert_textnew;

  PROCEDURE MAIN_W;

  /*PROCEDURE CALCULATION_AVG_M;*/

  /*PROCEDURE INSERT_ROW(P_GP_NUM        VARCHAR2,
  P_JY<PERSON>_NO        VARCHAR2,
  P_JY_DATE       VARCHAR2,
  P_OPEN_PRICE    VARCHAR2,
  P_HIGH_PRICE    VARCHAR2,
  P_CLOSE_PRICE   VARCHAR2,
  P_LOW_PRICE     VARCHAR2,
  P_VOLUME_AMOUNT VARCHAR2,
  P_ADJ_CLOSE     VARCHAR2);*/

  PROCEDURE INSERT_JYD(P_JY_DATE     VARCHAR2,
                       P_OPEN_PRICE  VARCHAR2,
                       P_HIGH_PRICE  VARCHAR2,
                       P_CLOSE_PRICE VARCHAR2,
                       P_LOW_PRICE   VARCHAR2,
                       P_JY_QUANTITY VARCHAR2,
                       P_JY_AMOUNT   VARCHAR2,
                       P_FQ_FACTOR   VARCHAR2,
                       P_GP_NUM      VARCHAR2);

  PROCEDURE INSERT_JYW;

  PROCEDURE CALCULATION_AVG_JYM;

  PROCEDURE CALCU_FLUCT;

  PROCEDURE CALCUL_AVG_DQTY;

  PROCEDURE CALCUL_AVG_WQTY;

  PROCEDURE CHECK_PRICE;

  PROCEDURE MAIN;

END GP_ANALYSE_PKG;

 
/
CREATE OR REPLACE PACKAGE BODY GPUSER.GP_ANALYSE_PKG IS

  U_PREMONTH    NUMBER := 10; --U型前段周
  U_LEFT        NUMBER; --U左侧高点
  U_LEFT_DATE   DATE;
  U_RIGHT       NUMBER; --U右侧高点
  U_RIGHT_DATE  DATE;
  U_LOW         NUMBER; --U型底部低点
  U_LPERIOD     NUMBER := 7; --U型间隔最低周期
  U_HPERIOD     NUMBER := 6000; --U型间隔最高周期
  U_lowest      number;
  u_lowest_date date;

  V_LEFT       NUMBER; --V左侧高点
  V_RIGHT      NUMBER; --V右侧高点
  V_RIGHT_DATE DATE;
  V_LOW        NUMBER; --V型底部低点
  V_LPERIOD    NUMBER := 3; --V型间隔最低周期
  V_HPERIOD    NUMBER; --V型间隔最低周期

  V_LOWest      number;
  v_lowest_date date;

  U_LDIFF_RATE   NUMBER := 0.03; --U型左侧高点高于前10周不超过3%
  U_LR_DIFF_RATE NUMBER := 0.03; --U左侧高点和右侧高点相差正负3%
  U_LL_LDIFF     NUMBER := 0.12; -- 左侧高点与U型底部的最低价相差12%-50%
  U_LL_HDIFF     NUMBER := 0.5; -- 左侧高点与U型底部的最低价相差12%-50%

  V_LLOW_DIFF NUMBER := 0.15; --V型底部最低价相对左侧下探不超15%
  V_LR_DIFF   NUMBER := 0.05; --右侧高点下一周为左侧高点不超过1.05

  L_BATCH_ID NUMBER;

  FUNCTION CHECK_ULEFT(P_RNUM NUMBER, P_GPNUM VARCHAR2, P_HPRICE NUMBER)
    RETURN VARCHAR2 IS
  
    L_FLAG VARCHAR2(5) := 'N';
  BEGIN
    SELECT 'Y'
      INTO L_FLAG
      FROM DUAL
     WHERE NOT EXISTS
     (SELECT 1
              FROM GP_JY_W_TMP W
             WHERE ROW_NUM BETWEEN (P_RNUM - 10) AND (P_RNUM - 1)
               and row_num <> p_rnum
               AND HIGH_PRICE > P_HPRICE * (1 + U_LDIFF_RATE));
  
    /*  FOR REC IN (SELECT *
                  FROM (SELECT ROWNUM ROW_NUM, W.HIGH_PRICE
                          FROM GP_JY_W W
                         WHERE W.GP_NUM = P_GPNUM
                         ORDER BY W.JY_DATE)
                 WHERE ROW_NUM BETWEEN (P_RNUM - 10) AND (P_RNUM - 1)) LOOP
      IF REC.HIGH_PRICE > P_HPRICE * (1 + U_LDIFF_RATE) THEN
        L_FLAG := 'Y';
      ELSE
        L_FLAG := 'N';
        EXIT;
      END IF;
    END LOOP;*/
  
    RETURN L_FLAG;
  EXCEPTION
    WHEN NO_DATA_FOUND THEN
      L_FLAG := 'N';
      RETURN L_FLAG;
    WHEN OTHERS THEN
    
      L_FLAG := 'N';
      RETURN L_FLAG;
  END;

  PROCEDURE GET_URIGHT(P_ROWNUM NUMBER,
                       P_GPNUM  VARCHAR2,
                       P_LPRICE NUMBER,
                       P_RNUM   OUT NUMBER,
                       P_JYDATE OUT DATE,
                       P_HPRICE OUT NUMBER) IS
    I      NUMBER := 1;
    L_FLAG NUMBER := 0;
  BEGIN
  
    P_RNUM := 0;
    FOR REC IN (SELECT W.*, GREATEST(W.OPEN_PRICE, W.CLOSE_PRICE) MAX_PRICE
                  FROM GP_JY_W_TMP W
                 WHERE W.GP_NUM = P_GPNUM
                   AND ROW_NUM > P_ROWNUM) LOOP
      IF ((REC.HIGH_PRICE >= (P_LPRICE * (1 - U_LR_DIFF_RATE)) AND
         REC.HIGH_PRICE <= (P_LPRICE * (1 + U_LR_DIFF_RATE))) OR
         (REC.MAX_PRICE >= (P_LPRICE * (1 - U_LR_DIFF_RATE)) AND
         REC.MAX_PRICE <= (P_LPRICE * (1 + U_LR_DIFF_RATE)))) THEN
        IF ((REC.ROW_NUM - P_ROWNUM) >= U_LPERIOD) THEN
          --ADD BY XUE.XU 2014-10-29
          L_FLAG := 0;
          begin
            select 1
              INTO L_FLAG
              from gp_avg_wtrade dt, GP_JY_W_TMP wt
             where dt.gp_num = p_gpnum
               and dt.gp_jy_date = wt.jy_date
               and dt.gp_num = wt.gp_num
               and wt.row_num = REC.ROW_NUM
               and dt.avg_days = 5
               and (dt.avg_qty * 1.05) <= wt.jy_quantity; --1.3
          exception
            when others then
              L_FLAG := 0;
          end;
        
          IF L_FLAG = 1 THEN
            P_RNUM   := REC.ROW_NUM;
            P_JYDATE := REC.JY_DATE;
            P_HPRICE := REC.HIGH_PRICE;
          
            -- dbms_output.put_line(p_gpnum || '   ' ||  REC.JY_DATE);
          
            EXIT;
          ELSE
            P_RNUM := 0;
            GOTO ENDPOINT;
          END IF;
        
        ELSE
          P_RNUM := 0;
          GOTO ENDPOINT;
        END IF;
      ELSIF REC.HIGH_PRICE < (P_LPRICE * (1 - U_LR_DIFF_RATE)) THEN
        GOTO ENDPOINT;
      ELSE
        EXIT;
      END IF;
      <<ENDPOINT>>
      NULL;
    
    END LOOP;
  
  END;

  FUNCTION CHECK_ULOW(P_GPNUM  VARCHAR2,
                      P_LNUM   NUMBER,
                      P_LPRICE NUMBER,
                      P_RNUM   NUMBER) RETURN VARCHAR2 IS
    -- L_LOW  NUMBER;
    L_FLAG VARCHAR2(5) := 'N';
  BEGIN
    SELECT MIN(LOW_PRICE)
      INTO U_LOW
      FROM GP_JY_W_TMP W
     WHERE W.GP_NUM = P_GPNUM
       AND ROW_NUM > P_LNUM
       AND ROW_NUM < P_RNUM;
  
    IF ((U_LOW >= P_LPRICE * (1 - U_LL_HDIFF)) AND
       (U_LOW <= P_LPRICE * (1 - U_LL_LDIFF))) THEN
      L_FLAG := 'Y';
    
      U_LOWest      := null;
      u_lowest_date := null;
      SELECT LOW_PRICE, jy_date
        INTO U_LOWest, u_lowest_date
        FROM GP_JY_W_TMP W
       WHERE W.GP_NUM = P_GPNUM
         AND ROW_NUM > P_LNUM
         AND ROW_NUM < P_RNUM
         and w.low_price = U_LOW
         and rownum = 1;
    
    END IF;
  
    RETURN L_FLAG;
  END;

  PROCEDURE GET_VRIGHT(P_GPNUM  VARCHAR2,
                       P_LNUM   NUMBER,
                       P_LPRICE NUMBER,
                       P_VRIGHT OUT NUMBER,
                       P_VRDATE OUT DATE,
                       P_VRNUM  OUT NUMBER) IS
    L_PRICE NUMBER;
    I       NUMBER := 1;
  BEGIN
    P_VRNUM := 0;
    FOR REC IN (SELECT *
                  FROM GP_JY_W_TMP W
                 WHERE W.GP_NUM = P_GPNUM
                   AND ROW_NUM > P_LNUM + 2) LOOP
      IF REC.HIGH_PRICE > P_LPRICE THEN
        IF REC.ROW_NUM - P_LNUM >= 3 THEN
          P_VRIGHT := REC.HIGH_PRICE;
          P_VRDATE := REC.JY_DATE;
          P_VRNUM  := REC.ROW_NUM;
        
          EXIT;
        
        ELSE
          P_VRNUM := 0;
          GOTO VPOINT;
        END IF;
      
      END IF;
      <<VPOINT>>
      NULL;
    END LOOP;
  
  END;

  FUNCTION CHECK_VLOW(P_GPNUM  VARCHAR2,
                      P_LNUM   NUMBER,
                      P_LPRICE NUMBER,
                      P_RNUM   NUMBER) RETURN VARCHAR2 IS
    -- L_LOW  NUMBER;
    L_FLAG VARCHAR2(5) := 'N';
  
    l_qty     number := 0;
    l_avg_qty number := 0;
  BEGIN
    SELECT MIN(LOW_PRICE)
      INTO V_LOW
      FROM GP_JY_W_TMP W
     WHERE W.GP_NUM = P_GPNUM
       AND ROW_NUM > P_LNUM
       AND ROW_NUM < P_RNUM;
  
    IF (V_LOW >= P_LPRICE * (1 - V_LLOW_DIFF)) THEN
      for rec_vl in (SELECT * --LOW_PRICE, jy_date
                     -- INTO V_LOWest, v_lowest_date
                       FROM GP_JY_W_TMP W
                      WHERE W.GP_NUM = P_GPNUM
                        AND ROW_NUM > P_LNUM
                        AND ROW_NUM < P_RNUM
                        and low_price = V_LOW) loop
        select wt.jy_quantity
          into l_qty
          from GP_JY_W_TMP wt
         where wt.row_num = P_LNUM;
      
        select avg(wt.jy_quantity)
          into l_avg_qty
          from GP_JY_W_TMP wt
         where wt.row_num between P_LNUM and rec_vl.row_num;
      
        if l_qty * 0.5 > l_avg_qty then
          L_FLAG        := 'Y';
          V_LOWest      := rec_vl.LOW_PRICE;
          v_lowest_date := rec_vl.jy_date;
          goto endpoint00;
        end if;
      end loop;
      <<endpoint00>>
      null;
    
      /* L_FLAG        := 'Y';
       V_LOWest      := null;
       v_lowest_date := null;
      SELECT LOW_PRICE, jy_date
        INTO V_LOWest, v_lowest_date
        FROM GP_JY_W_TMP W
       WHERE W.GP_NUM = P_GPNUM
         AND ROW_NUM > P_LNUM
         AND ROW_NUM < P_RNUM
         and low_price = V_LOW
         and rownum = 1;*/
    
    END IF;
  
    RETURN L_FLAG;
  END;

  PROCEDURE CHECK_RESULT IS
    L_SPRICE NUMBER := 0;
    L_SDATE  DATE;
    L_LPRICE NUMBER := 0;
    L_LDATE  DATE;
  BEGIN
  
    FOR REC IN (SELECT * FROM GP_RESULT_T) LOOP
      SELECT MAX(T.HIGH_PRICE)
        INTO L_SPRICE
        FROM GP_JY_W T
       WHERE T.GP_NUM = REC.GP_NUM
         AND T.JY_DATE BETWEEN REC.V_RDATE AND REC.V_RDATE + 90;
    
      --  IF L_SPRICE >= REC.V_RHIGHT * (1 + 0.2) THEN
      --RETURN 'Y';
    
      SELECT MIN(T.JY_DATE)
        INTO L_SDATE
        FROM GP_JY_W T
       WHERE T.GP_NUM = REC.GP_NUM
         AND T.JY_DATE BETWEEN REC.V_RDATE AND REC.V_RDATE + 90
         AND T.HIGH_PRICE = L_SPRICE;
    
      SELECT MIN(T.HIGH_PRICE)
        INTO L_LPRICE
        FROM GP_JY_W T
       WHERE T.GP_NUM = REC.GP_NUM
         AND T.JY_DATE BETWEEN REC.V_RDATE AND L_SDATE;
    
      SELECT MIN(T.JY_DATE)
        INTO L_LDATE
        FROM GP_JY_W T
       WHERE T.GP_NUM = REC.GP_NUM
         AND T.JY_DATE BETWEEN REC.V_RDATE AND L_SDATE
         AND T.HIGH_PRICE = L_LPRICE
       ORDER BY T.JY_DATE;
    
      UPDATE GP_RESULT_T T
         SET T.SUCESS_DATE  = L_SDATE,
             T.SUCESS_PRICE = L_SPRICE,
             T.LOWER_DATE   = L_LDATE,
             T.LOWER_PRICE  = L_LPRICE
       WHERE T.RECORD_ID = REC.RECORD_ID;
      --   END IF;
    
    END LOOP;
  
    COMMIT;
  
  END;

  PROCEDURE CHECK_MA IS
    L_LOW1_PRICE NUMBER := 0;
    L_LOW2_PRICE NUMBER := 0;
    L_LOW1_DATE  DATE;
    L_LOW2_DATE  DATE;
  
    L_LOW_CLOSE_P NUMBER := 0;
    L_LOW_CLOSE_D DATE;
  
    L_AVG_WEEK NUMBER;
  
    L_FLAG VARCHAR2(2) := 'N';
  
  BEGIN
    FOR REC IN (SELECT * FROM GP_RESULT_T) LOOP
      SELECT MIN(W.LOW_PRICE)
        INTO L_LOW1_PRICE
        FROM GP_JY_W W
       WHERE W.GP_NUM = REC.GP_NUM
         AND W.JY_DATE BETWEEN
             (REC.U_RDATE - (REC.U_RDATE - REC.U_LDATE) / 3) AND
             REC.U_RDATE;
    
      SELECT MIN(W.JY_DATE)
        INTO L_LOW1_DATE
        FROM GP_JY_W W
       WHERE W.GP_NUM = REC.GP_NUM
         AND W.JY_DATE BETWEEN
             (REC.U_RDATE - (REC.U_RDATE - REC.U_LDATE) / 3) AND
             REC.U_RDATE
         AND W.LOW_PRICE = L_LOW1_PRICE;
    
      SELECT MIN(W.LOW_PRICE)
        INTO L_LOW2_PRICE
        FROM GP_JY_W W
       WHERE W.GP_NUM = REC.GP_NUM
         AND W.JY_DATE BETWEEN
             (REC.U_RDATE - (REC.U_RDATE - REC.U_LDATE) / 4) AND
             REC.U_RDATE;
    
      SELECT MIN(W.JY_DATE)
        INTO L_LOW2_DATE
        FROM GP_JY_W W
       WHERE W.GP_NUM = REC.GP_NUM
         AND W.JY_DATE BETWEEN
             (REC.U_RDATE - (REC.U_RDATE - REC.U_LDATE) / 4) AND
             REC.U_RDATE
         AND W.LOW_PRICE = L_LOW2_PRICE;
    
      UPDATE GP_RESULT_T T
         SET T.U_LOW1_PRICE = L_LOW1_PRICE,
             T.U_LOW1_DATE  = L_LOW1_DATE,
             T.U_LOW2_PRICE = L_LOW2_PRICE,
             T.U_LOW2_DATE  = L_LOW2_DATE
       WHERE T.RECORD_ID = REC.RECORD_ID;
      COMMIT;
    
    END LOOP;
  
    FOR REC_R IN (SELECT *
                    FROM GP_RESULT_T T
                   WHERE T.SUCESS_PRICE IS NOT NULL) LOOP
    
      begin
        select avg_months
          into L_AVG_WEEK
          from (select t.avg_months, sum(g.CLOSE_PRICE - t.AVG_VALUE)
                  from GP_AVG_JYW t, GP_JY_W g
                 where t.GP_NUM = g.GP_NUM
                   AND t.GP_NUM = REC_R.Gp_Num
                   AND t.GP_JY_DATE = g.JY_DATE
                   and t.avg_months >= 2
                   AND g.JY_DATE BETWEEN REC_R.U_RDATE AND REC_R.SUCESS_DATE
                      
                   and t.avg_months not in
                       (select distinct AVG_MONTHS
                          from (SELECT AW.AVG_MONTHS,
                                       aw.gp_jy_date,
                                       (W.CLOSE_PRICE - AW.AVG_VALUE) dif_price
                                  FROM GP_AVG_JYW AW, GP_JY_W W
                                 WHERE AW.GP_NUM = W.GP_NUM
                                   AND AW.GP_NUM = REC_R.GP_NUM
                                   AND AW.GP_JY_DATE = W.JY_DATE
                                   AND W.JY_DATE BETWEEN REC_R.U_RDATE AND
                                       REC_R.SUCESS_DATE
                                      --AND AW.AVG_VALUE <= W.CLOSE_PRICE
                                   and aw.avg_months >= 2) a
                         where dif_price < 0)
                
                 group by t.AVG_MONTHS
                
                 order by sum(g.CLOSE_PRICE - t.AVG_VALUE))
        
         where rownum = 1;
      
      exception
        when no_data_found then
          L_AVG_WEEK := null;
      end;
    
      /*select AVG_MONTHS
       into L_AVG_WEEK
       from (SELECT AW.AVG_MONTHS,
                    sum(W.CLOSE_PRICE - AW.AVG_VALUE) dif_price
               FROM GP_AVG_JYW AW, GP_JY_W W
              WHERE AW.GP_NUM = W.GP_NUM
                AND AW.GP_NUM = REC_R.GP_NUM
                AND AW.GP_JY_DATE = W.JY_DATE
                AND W.JY_DATE BETWEEN REC_R.U_RDATE AND REC_R.SUCESS_DATE
                   --AND AW.AVG_VALUE <= W.CLOSE_PRICE
                and aw.avg_months >= 2
                and not exists
              (select 1
                       from GP_JY_W t
                      where AW.GP_NUM = W.GP_NUM
                        AND t.GP_NUM = REC_R.GP_NUM
                        AND AW.GP_JY_DATE = t.JY_DATE
                        AND T.JY_DATE BETWEEN REC_R.U_RDATE AND
                            REC_R.SUCESS_DATE
                        AND AW.AVG_VALUE > T.CLOSE_PRICE)
              group by AW.AVG_MONTHS
      
      
              order by sum(abs(AW.AVG_VALUE - W.CLOSE_PRICE)))
      where rownum = 1;*/
    
      UPDATE GP_RESULT_T T
         SET T.MA_NUM = L_AVG_WEEK
       WHERE T.RECORD_ID = REC_R.RECORD_ID;
    
      COMMIT;
    
    END LOOP;
  END;
  function get_gbsum(p_gp_num varchar2, p_jy_date date) return number is
    l_sumqty number := 0;
  begin
    select TRADE_QTY
      into l_sumqty
      from (select TRADE_QTY
              from GP_GB_INFO_V bv
             where bv.GP_NUM = p_gp_num
               and bv.CHANGE_DATE < p_jy_date
             order by bv.CHANGE_DATE DESC)
     where rownum = 1;
  
    return l_sumqty;
  end;

  procedure insert_textnew is
    l_str   varchar2(2000);
    l_enter varchar2(15) := chr(10);
  begin
    l_str := '<xml>
  <Content><![CDATA[感谢关注熙盈投资！' || l_enter ||
             to_char(sysdate + 1, 'yyyymmdd') || ' 熙盈推荐：';
  
    for rec in (select * from gp_result_t t where t.V_RDATE = trunc(sysdate)) loop
      l_str := l_str || l_enter || rec.gp_num || ' ' || rec.gp_name;
    end loop;
    --select * from wechat_material t where t.id ='531552ea4c2290b7014c22c44336000e' for update
  
    l_str := l_str || l_enter || '更多精彩敬请期待!]]></Content>
  <ToUserName><![CDATA[{#ToUserName#}]]></ToUserName>
  <FromUserName><![CDATA[{#FromUserName#}]]></FromUserName>
  <CreateTime><![CDATA[0]]></CreateTime>
  <MsgType><![CDATA[text]]></MsgType>
  <FuncFlag><![CDATA[0]]></FuncFlag>
</xml>';
  /*  --熙盈精选
    update wechat.wechat_material t
       set t.xml_data = l_str
     where t.id = '531552ea4c2290b7014c22c45c2d0010';
    commit;*/
  
  end;

  PROCEDURE MAIN_W IS
  
    I          NUMBER;
    L_RIGHTNUM NUMBER;
    L_U_FLAG   VARCHAR2(5) := 'N';
    L_V_FLAG   VARCHAR2(5) := 'N';
  
    L_VLEFT_NUM NUMBER;
  
    L_PRICE1 NUMBER;
    L_PRICE2 NUMBER;
  
    L_VR_NUM NUMBER;
  
    L_NEXT_NUM NUMBER := 0;
  
    l_allqty_rate number;
    l_avgqty_rate number;
  
    l_str varchar2(2000);
  
  BEGIN
    SELECT GP_RESULT_BATCH_S.NEXTVAL INTO L_BATCH_ID FROM DUAL;
  
    EXECUTE IMMEDIATE 'TRUNCATE TABLE GP_RESULT_T';
  
    FOR REC_GP IN (SELECT *
                     FROM GP_INFO_T
                    where jys_no <> 'hk'
                   /*WHERE GP_NUM IN ('000919', '000925', '000938')*/
                   ) LOOP
    
      INSERT INTO GP_JY_W_TMP
        SELECT ROWNUM ROW_NUM, A.*
          FROM (SELECT W.*
                  FROM GP_JY_W W
                 WHERE W.GP_NUM = REC_GP.GP_NUM
                 ORDER BY W.JY_DATE) A;
    
      --1.获取U型
      --   左侧高点和右侧高点都取周最高价，U型底部取周最低价
      --   左侧高点的前段10周的最高点不超该点3%
      --   左侧高点和右侧高点间隔大于等于7周，小于等于N周
      --   左侧高点和右侧高点相差正负3%
      --   左侧高点与U型底部的最低价相差30%-50%
    
      --2.获取V型
      --  以U型的右侧高点为V型的左侧高点
      --  右侧高点需大于左侧高点，周期间隔大于等于3周，V型底部最低价相对左侧下探不超15%即满足
      --  若左侧高点下一周为左侧高点不超过1.05，且右侧第二周相对左侧高点仍上涨，则排除
      --  若左侧高点下一周为左侧高点不超过1.05，若右侧第二周相对左侧高点下探，则继续上方判断
      --  若左侧高点下一周超1.05即直接排除
      L_RIGHTNUM := 0;
      L_U_FLAG   := 'N';
      L_V_FLAG   := 'N';
    
      L_VLEFT_NUM := 0;
    
      L_PRICE1 := 0;
      L_PRICE2 := 0;
    
      L_VR_NUM   := 0;
      L_NEXT_NUM := 0;
    
      ----DBMS_OUTPUT.PUT_LINE('GP:' || REC_GP.GP_NUM);
      FOR REC_GPINFO IN (SELECT W.*
                           FROM GP_JY_W_TMP W
                          WHERE W.GP_NUM = REC_GP.GP_NUM
                            AND W.ROW_NUM > U_PREMONTH
                          ORDER BY W.ROW_NUM) LOOP
      
        -- DBMS_OUTPUT.PUT_LINE('GP:' || REC_GP.GP_NUM || '  ' ||
        --                    REC_GPINFO.JY_DATE);
        IF (L_NEXT_NUM < REC_GPINFO.ROW_NUM OR L_NEXT_NUM = 0) THEN
          IF (CHECK_ULEFT(REC_GPINFO.ROW_NUM,
                          REC_GPINFO.GP_NUM,
                          REC_GPINFO.HIGH_PRICE) = 'Y') THEN
          
            /*--DBMS_OUTPUT.PUT_LINE('L_NEXT_NUM:' || L_NEXT_NUM ||
            ' REC_NUM:' || REC_GPINFO.ROW_NUM);*/
          
            ----DBMS_OUTPUT.PUT_LINE('UL::' || REC_GPINFO.JY_DATE);
          
            U_LEFT      := REC_GPINFO.HIGH_PRICE; --确定一个左侧高点
            U_LEFT_DATE := REC_GPINFO.JY_DATE;
          
            GET_URIGHT(P_ROWNUM => REC_GPINFO.ROW_NUM,
                       P_GPNUM  => REC_GPINFO.GP_NUM,
                       P_LPRICE => REC_GPINFO.HIGH_PRICE,
                       P_RNUM   => L_RIGHTNUM,
                       P_JYDATE => U_RIGHT_DATE,
                       P_HPRICE => U_RIGHT);
            IF L_RIGHTNUM = 0 THEN
              --第一个U型右侧高点不满足
              U_LEFT       := NULL;
              U_LEFT_DATE  := NULL;
              U_RIGHT      := NULL;
              U_RIGHT_DATE := NULL;
            
              GOTO BEGINPOINT;
            
            ELSE
              ----DBMS_OUTPUT.PUT_LINE('UR:' || U_RIGHT_DATE);
              --第一个U型高点已满足，再验证U型底部最低价
              L_U_FLAG := CHECK_ULOW(REC_GPINFO.GP_NUM,
                                     REC_GPINFO.ROW_NUM,
                                     REC_GPINFO.HIGH_PRICE,
                                     L_RIGHTNUM);
              IF L_U_FLAG = 'Y' THEN
                --第一个U型确认，继续取V型
                V_LEFT      := U_RIGHT;
                L_VLEFT_NUM := L_RIGHTNUM;
              
                ----DBMS_OUTPUT.PUT_LINE('ULOW:' || REC_GPINFO.ROW_NUM || ' ' ||
                -- L_RIGHTNUM);
              
                --验证V型左侧高点的下一周
                BEGIN
                  SELECT W.HIGH_PRICE
                    INTO L_PRICE1
                    FROM GP_JY_W_TMP W
                   WHERE W.GP_NUM = REC_GPINFO.GP_NUM
                     AND ROW_NUM = L_VLEFT_NUM + 1;
                EXCEPTION
                  WHEN OTHERS THEN
                    GOTO BEGINPOINT;
                END;
                ----DBMS_OUTPUT.PUT_LINE('NEXT1');
                --左侧高点下一周为左侧高点不超过1.05
                IF L_PRICE1 > V_LEFT * (1 + V_LR_DIFF) THEN
                  GOTO BEGINPOINT;
                
                ELSE
                  --取左侧高点下第二周高点
                  ----DBMS_OUTPUT.PUT_LINE('NEXT2');
                  BEGIN
                    SELECT HIGH_PRICE
                      INTO L_PRICE2
                      FROM GP_JY_W_TMP W
                     WHERE W.GP_NUM = REC_GPINFO.GP_NUM
                       AND ROW_NUM = L_VLEFT_NUM + 2;
                  EXCEPTION
                    WHEN OTHERS THEN
                      GOTO BEGINPOINT;
                  END;
                  IF L_PRICE2 > V_LEFT THEN
                    GOTO BEGINPOINT;
                  
                  ELSE
                    --获取V型右侧高点
                    ----DBMS_OUTPUT.PUT_LINE('NEXT3');
                    GET_VRIGHT(P_GPNUM  => REC_GPINFO.GP_NUM,
                               P_LNUM   => L_VLEFT_NUM,
                               P_LPRICE => V_LEFT,
                               P_VRIGHT => V_RIGHT,
                               P_VRDATE => V_RIGHT_DATE,
                               P_VRNUM  => L_VR_NUM);
                    ----DBMS_OUTPUT.PUT_LINE('NEXT4 ' || REC_GPINFO.GP_NUM || ' ' ||
                    --    L_VLEFT_NUM || ' ' || V_LEFT);
                    IF L_VR_NUM = 0 THEN
                      GOTO BEGINPOINT;
                    
                    ELSE
                      --验证V型底部最低点
                      L_V_FLAG := CHECK_VLOW(REC_GPINFO.GP_NUM,
                                             L_VLEFT_NUM,
                                             V_LEFT,
                                             L_VR_NUM);
                      ----DBMS_OUTPUT.PUT_LINE('NEXT5 ');
                      IF L_V_FLAG = 'Y' THEN
                      
                        select sum(decode(sum_qty,
                                          0,
                                          0,
                                          (JY_QUANTITY / sum_qty))),
                               avg(decode(sum_qty,
                                          0,
                                          0,
                                          (JY_QUANTITY / sum_qty)))
                          into l_allqty_rate, l_avgqty_rate
                          from (select W.JY_QUANTITY,
                                       get_gbsum(w.gp_num, w.jy_date) sum_qty
                                  from GP_JY_d w
                                 where w.gp_num = REC_GP.GP_NUM
                                   and w.jy_date between U_LEFT_DATE and
                                       U_RIGHT_DATE);
                        ---将结果插入表中
                        INSERT INTO GP_RESULT_T
                          (RECORD_ID,
                           BATCH_ID,
                           TACTICS_CODE,
                           GP_NUM,
                           GP_NAME,
                           U_LDATE,
                           U_LHIGHT,
                           U_RDATE,
                           U_RHIGHT,
                           V_RDATE,
                           V_RHIGHT,
                           CREATE_DATE,
                           U_lowest,
                           u_lowest_date,
                           v_lowest,
                           v_lowest_date,
                           allqty_rate,
                           avgqty_rate)
                        VALUES
                          (GP_RESULT_T_S.NEXTVAL,
                           L_BATCH_ID,
                           'TACTICS_W_NEW',
                           REC_GP.GP_NUM,
                           REC_GP.GP_NAME,
                           U_LEFT_DATE,
                           U_LEFT,
                           U_RIGHT_DATE,
                           U_RIGHT,
                           V_RIGHT_DATE,
                           V_RIGHT,
                           SYSDATE,
                           U_lowest,
                           u_lowest_date,
                           V_LOWest,
                           v_lowest_date,
                           l_allqty_rate,
                           l_avgqty_rate);
                        -- COMMIT;
                        L_NEXT_NUM := L_VR_NUM;
                        --DBMS_OUTPUT.PUT_LINE('L_NEXT_NUM:' || L_NEXT_NUM);
                      
                        --INSERT_ROW();
                      ELSE
                        --  L_NEXT_NUM := L_VR_NUM;
                        GOTO BEGINPOINT;
                      
                      END IF;
                    END IF;
                  END IF;
                
                END IF;
              
              ELSE
                GOTO BEGINPOINT;
              
              END IF;
            
            END IF;
          
          END IF;
        
        END IF;
      
        <<BEGINPOINT>>
      -- ----DBMS_OUTPUT.PUT_LINE(' ');
      
        NULL;
      
      END LOOP;
    
      EXECUTE IMMEDIATE 'TRUNCATE TABLE GP_JY_W_TMP';
    
      COMMIT;
    
    END LOOP;
  
    CHECK_RESULT;
    CHECK_MA;
    CHECK_PRICE;
  
    for j in 1 .. 200 loop
      null;
    end loop;
    delete from CLOSING.TB_RESULT_ALL t
     where t.tactics_code = 'TACTICS_W_NEW';
    COMMIT;
    insert into CLOSING.TB_RESULT_ALL
      select '8888',
             to_char(sysdate, 'yyyymmdd'),
             record_id,
             batch_id,
             tactics_code,
             gp_num,
             gp_name,
             u_ldate,
             u_lhight,
             u_rdate,
             u_rhight,
             v_rdate,
             v_rhight,
             create_date,
             sucess_date,
             sucess_price,
             lower_date,
             lower_price,
             u_low1_price,
             u_low1_date,
             u_low2_price,
             u_low2_date,
             ma_num,
             u_lowest,
             u_lowest_date,
             v_lowest,
             v_lowest_date,
             allqty_rate,
             avgqty_rate,
             attribute1,
             attribute2,
             attribute3,
             attribute4,
             attribute5
      
        from gp_result_t t;
    commit;
    l_str := 'select t.gp_num   as 股票编号,' || 't.gp_name  as 股票名称,' ||
             't.u_ldate  as U型左侧高点周,' || 't.u_lhight as U型左侧高点价格,' ||
             't.u_rdate  as U型右侧高点周,' || 't.u_rhight as U型右侧高点价格,' ||
            
             't.u_lowest_date  as U型最低点周,' || 't.u_lowest as U型最低点价格,' ||
            
             't.v_lowest_date  as V型最低点周,' || 't.v_lowest as V型最低点价格,' ||
            
             't.v_rdate  as V型右侧高点周,' || 't.v_rhight as V型右侧高点价格,' ||
            
             't.sucess_date  as 最高点周九十天,' || 't.sucess_price as 最高点价格九十天,' ||
             't.lower_date  as 最低点周九十天,' || 't.lower_price as 内最低点价格九十天,' ||
            
             't.u_low1_date  as 最低点周三分之一,' ||
             't.u_low1_price as 最低点价格三分之一,' ||
             't.u_low2_date  as 最低点周四分之一,' ||
             't.u_low2_price as 最低点价格四分之一,' ||
            
             't.ma_num as MA支撑点 , ' || 't.allqty_rate as 累计换手率, ' ||
             't.avgqty_rate as 平均换手率 ,' || 'T.ATTRIBUTE3 AS 最近突破日 ,' ||
             'T.ATTRIBUTE1 AS 突破日收盘价 ,' || 't.attribute2 as 是否突破 ' ||
            
             ' from GP_RESULT_T t order by t.record_id';
  
    gp_mail_pkg.MAIN('SH_SZ', l_str);
  
    insert_textnew;
  
  END;

  /*PROCEDURE CALCULATION_AVG_M IS
    L_AVG_PRICE NUMBER := 0;
  BEGIN
    FOR REC_GP IN (SELECT *
                     FROM GP_INFO_T t
                   \* WHERE EXISTS (select 1
                    from gp_result_t r
                   where r.gp_num = t.gp_num)*\
                   ) LOOP
  
      INSERT INTO GP_JY_W_TMP
        SELECT ROWNUM ROW_NUM, A.*
          FROM (SELECT d.*
                  FROM GP_JY_W d
                 WHERE d.GP_NUM = REC_GP.GP_NUM
                 ORDER BY d.JY_DATE) A;
      FOR REC IN (SELECT *
                    FROM GP_JY_W_TMP T
                   where not exists
                   (select 1
                            from GP_AVG_W w
                           where w.gp_num = t.gp_num
                             and w.gp_record_id = t.record_id)
                   ORDER BY T.ROW_NUM) LOOP
        FOR I IN 1 .. 20 LOOP
  
          SELECT round(AVG(NVL(CLOSE_PRICE, 0)), 2)
            INTO L_AVG_PRICE
            FROM GP_JY_W_TMP
           WHERE ROW_NUM >= REC.ROW_NUM - (I - 1)
             and ROW_NUM <= REC.ROW_NUM;
          IF L_AVG_PRICE > 0 THEN
            INSERT INTO GP_AVG_W
              (RECORD_ID,
               GP_NUM,
               GP_RECORD_ID,
               GP_JY_DATE,
               AVG_MONTHS,
               AVG_VALUE,
               CREATE_DATE)
            VALUES
              (GP_AVG_W_S.NEXTVAL,
               REC.GP_NUM,
               REC.RECORD_ID,
               REC.JY_DATE,
               I,
               L_AVG_PRICE,
               SYSDATE);
          ELSE
            GOTO AVGPOINT;
          END IF;
        END LOOP;
        <<AVGPOINT>>
        NULL;
      END LOOP;
  
      EXECUTE IMMEDIATE 'TRUNCATE TABLE GP_JY_W_TMP';
  
      COMMIT;
  
    END LOOP;
  
  END;*/

  /*PROCEDURE INSERT_ROW(P_GP_NUM        VARCHAR2,
                       P_JYS_NO        VARCHAR2,
                       P_JY_DATE       VARCHAR2,
                       P_OPEN_PRICE    VARCHAR2,
                       P_HIGH_PRICE    VARCHAR2,
                       P_CLOSE_PRICE   VARCHAR2,
                       P_LOW_PRICE     VARCHAR2,
                       P_VOLUME_AMOUNT VARCHAR2,
                       P_ADJ_CLOSE     VARCHAR2) IS
  BEGIN
    INSERT INTO GP_JY_w
      (RECORD_ID,
       GP_NUM,
       JY_DATE,
       OPEN_PRICE,
       HIGH_PRICE,
       LOW_PRICE,
       CLOSE_PRICE,
       VOLUME_AMOUNT,
       ADJ_CLOSE,
       JYS_NO,
       CREATION_DATE)
    VALUES
      (GP_INFO_D_S.NEXTVAL,
       P_GP_NUM,
       TO_DATE(P_JY_DATE, 'YYYY-MM-DD'),
       P_OPEN_PRICE,
       P_HIGH_PRICE,
       P_LOW_PRICE,
       P_CLOSE_PRICE,
       null,
       null,
       --P_VOLUME_AMOUNT,
       --P_ADJ_CLOSE,
       upper(replace(P_JYS_NO, 'sh', 'ss')),
       SYSDATE);
  
    commit;
  END;*/

  /*PROCEDURE MAIN_ZS(P_GP_NUM IN VARCHAR2 DEFAULT NULL) IS
    CURSOR GP_RES IS(
      SELECT *
        FROM GP_RESULT_T T
       WHERE T.GP_NUM = NVL(P_GP_NUM, T.GP_NUM));
  
    ZS_COUNT NUMBER := 3;
    ZS_FLAG  VARCHAR2(2) := 'N';
  
    ZS_WHIGH DATE; --波峰日期
  BEGIN
    FOR REC_GP IN GP_RES LOOP
      INSERT INTO GP_JY_D_TMP
        SELECT ROWNUM ROW_NUM, A.*
          FROM (SELECT D.*
                  FROM GP_JY_D D
                 WHERE D.GP_NUM = REC_GP.GP_NUM
                   AND D.JY_DATE > (SYSDATE - 366)
                 ORDER BY D.JY_DATE) A;
  
      FOR REC_GPD IN (SELECT *
                        FROM GP_INFO_D_TMP
                       WHERE GP_NUM = REC_GP.GP_NUM
                       ORDER BY ROW_NUM) LOOP
        --1.确定波峰(最高价高于前后三天的最高价)
        <<nextdate>>
        IF REC_GPD.ROW_NUM > ZS_COUNT THEN
  
          SELECT 'Y'
            INTO ZS_FLAG
            FROM DUAL
           WHERE NOT EXISTS
           (SELECT 1
                    FROM GP_INFO_D_TMP D
                   WHERE D.ROW_NUM BETWEEN (REC_GPD.ROW_NUM - 3) AND
                         (REC_GPD.ROW_NUM + 3)
                     AND D.ROW_NUM <> REC_GPD.ROW_NUM
                     AND D.HIGH_PRICE > REC_GPD.HIGH_PRICE);
  
          IF ZS_FLAG = 'Y' THEN
            ZS_COUNT := REC_GPD.ROW_NUM + 3;
  
            ZS_WHIGH := REC_GPD.JY_DATE; --波峰确认
  
            --2.确定BP日期，最低价，类型，确认日期
  
          ELSE
            GOTO nextdate;
          END IF;
  
        end if;
  
      END LOOP;
  
    END LOOP;
  END;*/

  PROCEDURE INSERT_JYD(P_JY_DATE     VARCHAR2,
                       P_OPEN_PRICE  VARCHAR2,
                       P_HIGH_PRICE  VARCHAR2,
                       P_CLOSE_PRICE VARCHAR2,
                       P_LOW_PRICE   VARCHAR2,
                       P_JY_QUANTITY VARCHAR2,
                       P_JY_AMOUNT   VARCHAR2,
                       P_FQ_FACTOR   VARCHAR2,
                       P_GP_NUM      VARCHAR2) IS
  
    l_count number := 0;
  BEGIN
  
    select count(*)
      into l_count
      from gp_jy_d
     where gp_num = P_GP_NUM
       and jy_date = to_date(p_jy_date, 'yyyy-mm-dd');
    if l_count = 0 then
      INSERT INTO GP_JY_D
        (RECORD_ID,
         GP_NUM,
         JY_DATE,
         OPEN_PRICE,
         HIGH_PRICE,
         CLOSE_PRICE,
         LOW_PRICE,
         JY_QUANTITY,
         JY_AMOUNT,
         FQ_FACTOR,
         CREATION_DATE,
         LAST_UPDATE_DATE)
      VALUES
        (GP_JY_D_S.NEXTVAL,
         P_GP_NUM,
         to_date(P_JY_DATE, 'yyyy-mm-dd'),
         P_OPEN_PRICE,
         P_HIGH_PRICE,
         P_CLOSE_PRICE,
         P_LOW_PRICE,
         P_JY_QUANTITY,
         P_JY_AMOUNT,
         P_FQ_FACTOR,
         SYSDATE,
         SYSDATE);
    end if;
  END;

  PROCEDURE INSERT_JYW IS
    l_count number := 0;
    l_odate date;
    l_cdate date;
  
    l_jy_date     date;
    l_open_price  number;
    l_high_price  number;
    l_close_price number;
    l_low_price   number;
    l_jy_quantity number;
    l_jy_amount   number;
  BEGIN
  
    --EXECUTE IMMEDIATE 'TRUNCATE TABLE GP_JY_W';
  
    FOR REC_GP IN (SELECT *
                     FROM GP_INFO_T T
                    WHERE EXISTS
                    (SELECT 1 FROM GP_JY_D D WHERE D.GP_NUM = T.GP_NUM)) LOOP
    
      FOR REC_WEEK IN (SELECT T.WEEK_NUM,
                              MIN(T.D_DATE) BEGIN_DATE,
                              MAX(T.D_DATE) END_DATE
                         FROM GP_WEEK_INFO T
                        WHERE NVL(T.OPEN_FLAG, 'Y') <> 'N'
                          AND T.D_DATE < SYSDATE
                       /*  and not exists
                       (select 1
                                from gp_jy_w w
                               where w.gp_num = rec_gp.gp_num
                                 and w.week_num = t.week_num)*/
                        GROUP BY T.WEEK_NUM
                        ORDER BY T.WEEK_NUM) LOOP
      
        l_count := 0;
        select count(*)
          into l_count
          from gp_jy_d jd
         where jd.gp_num = rec_gp.gp_num
           and jd.jy_date between REC_WEEK.Begin_Date and REC_WEEK.end_date;
      
        l_jy_date := REC_WEEK.end_date;
      
        if l_count > 0 then
          select max(d.jy_date) c_date, min(d.jy_date) o_date
            into l_cdate, l_odate
            from gp_jy_d d
           where d.gp_num = rec_gp.gp_num
             and d.jy_date between rec_week.begin_date and
                 rec_week.end_date;
        
          select d.open_price
            into l_open_price
            from gp_jy_d d
           where d.gp_num = rec_gp.gp_num
             and d.jy_date = l_odate;
        
          select d.close_price
            into l_close_price
            from gp_jy_d d
           where d.gp_num = rec_gp.gp_num
             and d.jy_date = l_cdate;
        
          select max(d.high_price),
                 min(d.low_price),
                 sum(d.jy_quantity),
                 sum(d.jy_amount)
            into l_high_price, l_low_price, l_jy_quantity, l_jy_amount
            from gp_jy_d d
           where d.gp_num = rec_gp.gp_num
             and d.jy_date between REC_WEEK.Begin_Date and
                 REC_WEEK.end_date;
        
          select count(*)
            into l_count
            from gp_jy_w p
           where p.gp_num = rec_gp.gp_num
             and p.week_num = rec_week.week_num;
        
          if l_count = 1 then
          
            update gp_jy_w p
               set p.jy_date   = l_jy_date,
                   open_price  = l_open_price,
                   high_price  = l_high_price,
                   close_price = l_close_price,
                   low_price   = l_low_price,
                   jy_quantity = l_jy_quantity,
                   jy_amount   = l_jy_amount,
                   attribute1  = sysdate
             where p.gp_num = rec_gp.gp_num
               and p.week_num = rec_week.week_num;
          else
          
            INSERT INTO GP_JY_W
              (record_id,
               gp_num,
               jy_date,
               week_num,
               open_price,
               high_price,
               close_price,
               low_price,
               jy_quantity,
               jy_amount,
               creation_date)
            VALUES
              (GP_JY_W_s.Nextval,
               rec_gp.gp_num,
               l_jy_date,
               rec_week.week_num,
               l_open_price,
               l_high_price,
               l_close_price,
               l_low_price,
               l_jy_quantity,
               l_jy_amount,
               sysdate);
          end if;
        end if;
      
      END LOOP;
      commit;
    END LOOP;
  
  END;

  PROCEDURE CALCULATION_AVG_JYM IS
    L_AVG_PRICE NUMBER := 0;
    l_count     number := 0;
  BEGIN
    FOR REC_GP IN (SELECT * FROM GP_INFO_T t WHERE T.JYS_NO IN ('sz', 'sh')) LOOP
    
      INSERT INTO GP_JY_W_TMP
        SELECT ROWNUM ROW_NUM, A.*
          FROM (SELECT d.*
                  FROM GP_JY_W d
                 WHERE d.GP_NUM = REC_GP.GP_NUM
                 ORDER BY d.JY_DATE) A;
      FOR REC IN (SELECT *
                    FROM GP_JY_W_TMP T
                   where not exists (select 1
                            from GP_AVG_JYW w
                           where w.gp_num = t.gp_num
                             and w.gp_week_num = t.week_num
                             and w.gp_jy_date = t.jy_date)
                   ORDER BY T.ROW_NUM) LOOP
        FOR I IN 1 .. 20 LOOP
        
          SELECT round(AVG(NVL(CLOSE_PRICE, 0)), 2)
            INTO L_AVG_PRICE
            FROM GP_JY_W_TMP
           WHERE ROW_NUM >= REC.ROW_NUM - (I - 1)
             and ROW_NUM <= REC.ROW_NUM;
          IF L_AVG_PRICE > 0 THEN
          
            select count(*)
              into l_count
              from gp_avg_jyw t
             where t.gp_num = REC.GP_NUM
               and t.gp_week_num = rec.week_num
               and t.avg_months = i;
          
            if l_count > 0 then
              update GP_AVG_JYW t
                 set t.gp_jy_date = rec.jy_date, t.avg_value = L_AVG_PRICE
               where t.gp_num = REC.GP_NUM
                 and t.gp_week_num = rec.week_num
                 and t.avg_months = i;
            
            else
            
              INSERT INTO GP_AVG_JYW
                (RECORD_ID,
                 GP_NUM,
                 GP_week_num,
                 GP_JY_DATE,
                 AVG_MONTHS,
                 AVG_VALUE,
                 CREATE_DATE)
              VALUES
                (GP_AVG_JYW_S.NEXTVAL,
                 REC.GP_NUM,
                 REC.WEEK_NUM,
                 REC.JY_DATE,
                 I,
                 L_AVG_PRICE,
                 SYSDATE);
            end if;
          ELSE
            GOTO AVGPOINT;
          END IF;
        END LOOP;
        <<AVGPOINT>>
        NULL;
      END LOOP;
    
      EXECUTE IMMEDIATE 'TRUNCATE TABLE GP_JY_W_TMP';
    
      COMMIT;
    
    END LOOP;
  
  END;

  PROCEDURE CALCU_FLUCT is
    l_Daily_TR    number;
    i             number := 0;
    l_fluct_ratio number := 0;
  begin
    for rec in (select * from gp_jy_d) loop
      select greatest(abs(t.high_price - t.low_price),
                      abs(t.high_price - t.close_price),
                      abs(t.close_price - t.low_price))
        into l_Daily_TR
        from gp_jy_d t
       where record_id = rec.record_id;
    
      update gp_jy_d t
         set t.attribute1 = l_Daily_TR
       where t.record_id = rec.record_id;
    
      if i = 1000 then
        commit;
        i := 0;
      else
        i := i + 1;
      end if;
    
    end loop;
  
    commit;
    i := 0;
  
    for rec_gp in (select * from gp_info_t t where t.jys_no in ('sh', 'sz')) loop
      execute immediate 'truncate table GP_JY_D_TMP';
      --dbms_output.put_line(rec_gp.gp_num);
      insert into GP_JY_D_TMP
        select rownum row_num, a.*
          from (select d.*
                  from gp_jy_d d
                 where d.gp_num = rec_gp.gp_num
                 order by jy_date) a;
    
      for rec_d in (select d.*
                      from GP_JY_D_TMP d
                     where d.gp_num = rec_gp.gp_num
                       and row_num >= 20
                       and d.attribute2 is null
                     order by jy_date) loop
      
        select sum(attribute1) / 20
          into l_fluct_ratio
          from GP_JY_D_TMP
         where gp_num = rec_gp.gp_num
           and row_num between rec_d.row_num - 19 and rec_d.row_num;
      
        update gp_jy_d
           set attribute2 = l_fluct_ratio
         where record_id = rec_d.record_id;
      
      end loop;
      commit;
      --  execute immediate 'truncate table GP_JY_D_TMP';
    end loop;
  end;

  PROCEDURE CALCUL_AVG_DQTY IS
    L_AVG_PRICE NUMBER := 0;
    l_count     number := 0;
  
    l_record_id number := 0;
  
    l_flag NUMBER := 0;
  BEGIN
    FOR REC_GP IN (SELECT * FROM GP_INFO_T t WHERE T.JYS_NO IN ('sz', 'sh')) LOOP
      EXECUTE IMMEDIATE 'TRUNCATE TABLE GP_JY_D_TMP';
      INSERT INTO GP_JY_D_TMP
        SELECT ROWNUM ROW_NUM, A.*
          FROM (SELECT d.*
                  FROM GP_JY_D d
                 WHERE d.GP_NUM = REC_GP.GP_NUM
                 ORDER BY d.JY_DATE) A;
      FOR REC IN (SELECT *
                    FROM GP_JY_D_TMP T
                  -- where t.jy_date >= trunc(sysdate - 30)
                  /*where not exists (select 1
                   from GP_AVG_JYW w
                  where w.gp_num = t.gp_num
                    and w.gp_week_num = t.week_num
                    and w.gp_jy_date = t.jy_date)*/
                   ORDER BY T.ROW_NUM) LOOP
        FOR I IN 1 .. 30 LOOP
        
          if i in (5, 10, 20, 30) then
            SELECT COUNT(*)
              INTO L_FLAG
              FROM GP_AVG_DTRADE T
             WHERE T.GP_NUM = REC.GP_NUM
               AND T.GP_JY_DATE = REC.JY_DATE
               AND T.AVG_DAYS = I;
            IF L_FLAG = 0 THEN
            
              select GP_AVG_DTRADE_S.nextval into l_record_id from dual;
              INSERT INTO GP_AVG_DTRADE
                (record_id,
                 gp_num,
                 gp_jy_date,
                 avg_days,
                 avg_qty,
                 avg_amount,
                 create_date)
                select l_record_id,
                       rec.gp_num,
                       rec.jy_date,
                       i,
                       avg(nvl(d.jy_quantity, 0)),
                       avg(nvl(d.jy_amount, 0)),
                       sysdate
                  from GP_JY_D_TMP d
                 where d.gp_num = rec.gp_num
                   and d.jy_date < rec.jy_date
                   and d.row_num between rec.row_num - (i + 1) and
                       (rec.row_num - 1);
            END IF;
          END IF;
        END LOOP;
        NULL;
      END LOOP;
    
      COMMIT;
    
    END LOOP;
  
  END;

  PROCEDURE CALCUL_AVG_WQTY IS
    L_AVG_PRICE NUMBER := 0;
    l_count     number := 0;
  
    l_record_id number := 0;
  
    l_flag NUMBER := 0;
  BEGIN
    FOR REC_GP IN (SELECT * FROM GP_INFO_T t WHERE T.JYS_NO IN ('sz', 'sh')) LOOP
      EXECUTE IMMEDIATE 'TRUNCATE TABLE GP_JY_W_TMP';
      INSERT INTO GP_JY_W_TMP
        SELECT ROWNUM ROW_NUM, A.*
          FROM (SELECT d.*
                  FROM GP_JY_W d
                 WHERE d.GP_NUM = REC_GP.GP_NUM
                 ORDER BY d.JY_DATE) A;
      FOR REC IN (SELECT *
                    FROM GP_JY_W_TMP T
                   where t.jy_date >= trunc(sysdate - 90)
                  /*where not exists (select 1
                   from GP_AVG_JYW w
                  where w.gp_num = t.gp_num
                    and w.gp_week_num = t.week_num
                    and w.gp_jy_date = t.jy_date)*/
                   ORDER BY T.ROW_NUM) LOOP
        FOR I IN 1 .. 30 LOOP
        
          if i in (5, 10, 20, 30) then
            SELECT COUNT(*)
              INTO L_FLAG
              FROM GP_AVG_WTRADE T
             WHERE T.GP_NUM = REC.GP_NUM
               AND T.GP_JY_DATE = REC.JY_DATE
               AND T.AVG_DAYS = I;
            IF L_FLAG = 0 THEN
            
              select GP_AVG_WTRADE_S.nextval into l_record_id from dual;
              INSERT INTO GP_AVG_WTRADE
                (record_id,
                 gp_num,
                 gp_jy_date,
                 avg_days,
                 avg_qty,
                 avg_amount,
                 create_date)
                select l_record_id,
                       rec.gp_num,
                       rec.jy_date,
                       i,
                       avg(nvl(d.jy_quantity, 0)),
                       avg(nvl(d.jy_amount, 0)),
                       sysdate
                  from GP_JY_w_TMP d
                 where d.gp_num = rec.gp_num
                   and d.jy_date < rec.jy_date
                   and d.row_num between rec.row_num - (i + 1) and
                       (rec.row_num - 1);
            END IF;
          END IF;
        END LOOP;
        NULL;
      END LOOP;
    
      COMMIT;
    
    END LOOP;
  
  END;

  PROCEDURE CHECK_PRICE IS
    L_PRICE NUMBER := 0;
    L_FLAG  VARCHAR2(10) := '突破';
    l_date  varchar2(10);
  BEGIN
    FOR REC IN (SELECT * FROM GP_RESULT_T) LOOP
      BEGIN
        SELECT a.CLOSE_PRICE, to_char(a.jy_date, 'yyyy-mm-dd')
          INTO L_PRICE, l_date
        
          from (select *
                  FROM GP_JY_D T
                 WHERE T.JY_DATE > rec.v_rdate
                   and t.close_price > rec.v_rhight
                   AND T.GP_NUM = REC.GP_NUM
                
                 order by t.jy_date desc) a
         where rownum = 1;
        L_FLAG := '突破';
      EXCEPTION
        WHEN OTHERS THEN
          L_PRICE := 0;
          l_date  := null;
          L_FLAG  := '未突破';
      END;
    
      UPDATE GP_RESULT_T T
         SET T.ATTRIBUTE1 = L_PRICE,
             T.ATTRIBUTE2 = L_FLAG,
             t.attribute3 = l_date
       WHERE T.RECORD_ID = REC.RECORD_ID;
    END LOOP;
    COMMIT;
  
  END;

  PROCEDURE MAIN IS
  BEGIN
    --CALCUL_AVG_DQTY;
    INSERT_JYW;
    CALCUL_AVG_WQTY;
    CALCULATION_AVG_JYM;
    MAIN_W;
    CALCU_FLUCT;
  
    commit;
  
    /* for i in 1 .. 8000 loop
      null;
    end loop;
    gp_basepoint_pkg.send_mail;
    
    commit;
    
     for i in 1 .. 8000 loop
      null;
    end loop;
    gp_trend_pkg.Trend_main;*/
    
    gp_Trend_pkg.Trend_main('SZ_SH_TREND');
    
    gp_basepoint_pkg.main(p_sub => '');
  
  END;

END GP_ANALYSE_PKG;
/
