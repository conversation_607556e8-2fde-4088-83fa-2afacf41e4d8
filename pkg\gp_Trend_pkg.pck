﻿create or replace package gpuser.gp_Trend_pkg is

  -- Author  : CN
  -- Created : 2014/9/9 16:07:52
  -- Purpose :

  -- Public type declarations
  --趋势线策略分析
  function check_tl(p_num number, p_price number) return varchar2;

  procedure Trend_Analy(p_num in number);

  procedure Trend_main(p_sub in varchar2);

end gp_Trend_pkg;

 
/
create or replace package body gpuser.gp_Trend_pkg is

  function check_tl(p_num number, p_price number) return varchar2 is
    l_flag varchar2(2) := 'N';
  begin
    select 'Y'
      into l_flag
      FROM DUAL
     WHERE NOT EXISTS (select 1
              from gp_jy_d_tmp t
             where t.row_num between p_num + 1 and p_num + 3
               and t.close_price <= p_price * 1.03);
  
    return l_flag;
  exception
    when others then
      return 'N';
  end;

  function CALCUL_AVG_DQTY(p_gp_num varchar2, p_jy_date date, p_num number)
    return number IS
    L_AVG_qty NUMBER := 0;
    l_count   number := 0;
  
    l_record_id number := 0;
  
    l_flag NUMBER := 0;
  BEGIN
  
    EXECUTE IMMEDIATE 'TRUNCATE TABLE GP_JY_D_TMP';
    INSERT INTO GP_JY_D_TMP
      SELECT ROWNUM ROW_NUM, A.*
        FROM (SELECT d.*
                FROM GP_JY_D d
               WHERE d.GP_NUM = p_GP_NUM
               ORDER BY d.JY_DATE) A;
  
    select avg(nvl(d.jy_quantity, 0)) /*,
                   avg(nvl(d.jy_amount, 0))*/
      into L_AVG_qty
      from GP_JY_D_TMP d
     where d.gp_num = p_gp_num
       and d.jy_date < p_jy_date
       and d.row_num between d.row_num - (p_num + 1) and (d.row_num - 1);
  
    return L_AVG_qty;
  
  END;

  procedure Trend_Analy(p_num in number) is
    l_gp_num     varchar2(10); --股票代码
    l_dates_type number; --时间框架(200天/400天/600天)
    l_down_a0    date; --a0日期(下降点1)
    l_down_a2    date; --a2日期(下降点2)
    l_point_a    number; --系数：a
    l_point_b    number; --系数：b
    l_top_a0     number; --a0最高价
    l_top_a2     number; --a2最高价
    l_tp_b0      date; --b0日期(突破点)
    l_up_a1      date; --a1日期(上升点1)
    l_up_b2      date; --b2日期(上升点2)
    l_point_c    number; --系数：c
    l_point_d    number; --系数：d
    l_low_a1     number; --a1最低价
    l_low_b2     number; --b2最低价
    l_date_b1    date; --b1日期
  
    ln_down_a2 date; --a2日期(下降点2)
    ln_point_a number; --系数：a
    ln_point_b number; --系数：b
    ln_point_c number; --系数：c
    ln_point_d number; --系数：d
  
    ln_tp_b0 date;
  
    l_total_num number;
  
    i int := 0;
  
    l_aa1   number;
    l_aa2   number;
    l_ldate date;
  
    ln_aa1   number;
    ln_aa2   number;
    ln_ldate date;
  
    l_flag number;
  
    l_min_price1 number;
    l_min_num1   number;
    l_max_num0   number;
  
    l_upmax_price number;
    l_upmax_num   number;
  
    l_upmin_price number;
  
    tp_i number := 0;
  
    l_avg_qty number := 0;
  
    l_avgflag varchar2(10) := 'N';
  
    ln_avg_qty number := 0;
  
    ln_avgflag varchar2(10) := 'N';
  
    l_avg_trade number := 0;
  
    L_PRE_CLOSE   NUMBER := 0;
    L_TREND_PRICE NUMBER := 0;
  
  begin
    l_dates_type := p_num;
    for rec_re in (select distinct gp_num
                     from gp_info_t t
                    where t.jys_no in ('sh', 'sz')
                      and exists
                    (select 1 from gp_jy_d where gp_num = t.gp_num)
                   /* where gp_num = '601002'*/
                   ) loop
      execute immediate 'truncate table gp_jy_d_tmp';
      insert into gp_jy_d_tmp
        (select rownum row_num, a.*
           from (select w.*
                   from gp_jy_d w
                  where w.gp_num = rec_re.gp_num
                  order by w.jy_date) a);
      commit;
    
      l_gp_num := rec_re.gp_num;
    
      select max(row_num) into l_total_num from gp_jy_d_tmp;
    
      --获取最低的high_price及对应jy_date
      select min(t1.high_price)
        into l_min_price1
        from gp_jy_d_tmp t1
       where t1.row_num > l_total_num - (p_num + 1);
    
      select d.jy_date, d.row_num, d.low_price
        into l_up_a1, l_min_num1, l_upmin_price
        from (select rownum rnum, a.*
                from (select *
                        from gp_jy_d_tmp t1
                       where t1.row_num > l_total_num - (p_num + 1)
                         and t1.high_price = l_min_price1
                       order by row_num) a) d
       where rnum = 1;
    
      --获取最高high_price及jy_date
      select max(t0.high_price)
        into l_top_a0
        from gp_jy_d_tmp t0
       where t0.row_num > l_total_num - (p_num + 1)
         and t0.jy_date <= l_up_a1;
    
      select jy_date, row_num, low_price
        into l_down_a0, l_max_num0, l_low_a1
        from (select rownum rnum, a.*
                from (select *
                        from gp_jy_d_tmp t0
                       where t0.row_num > l_total_num - (p_num + 1)
                         and t0.jy_date <= l_up_a1
                         and t0.high_price = l_top_a0
                       order by row_num) a) d
       where rnum = 1;
    
      --查找验证斜率最低点
    
      l_aa1  := 0;
      ln_aa1 := 0;
    
      l_aa2  := 0;
      ln_aa2 := 0;
    
      ln_ldate := null;
      l_ldate  := null;
    
      i := 0;
      for rec in (select *
                    from gp_jy_d_tmp
                   where jy_date between (l_down_a0 + 20) and l_up_a1
                  --and check_tl(row_num, high_price) = 'Y'
                   order by jy_date) loop
        i := i + 1;
      
        ----dbms_output.put_line(i);
      
        if i = 1 then
          l_aa1  := (l_top_a0 - rec.high_price) /
                    (l_max_num0 - rec.row_num);
          ln_aa1 := (ln(l_top_a0) - ln(rec.high_price)) /
                    (l_max_num0 - rec.row_num);
        
          l_ldate  := rec.jy_date;
          ln_ldate := rec.jy_date;
        
          l_point_b  := l_top_a0 - l_max_num0 * l_aa1;
          ln_point_b := ln(l_top_a0) - l_max_num0 * l_aa1;
        
        else
          BEGIN
            l_aa2  := (l_top_a0 - rec.high_price) /
                      (l_max_num0 - rec.row_num);
            ln_aa2 := (ln(l_top_a0) - ln(rec.high_price)) /
                      (l_max_num0 - rec.row_num);
          EXCEPTION
            WHEN OTHERS THEN
              /*dbms_output.put_line('ERROR:' || l_top_a0 || '  ' ||
                                   rec.high_price || '  ' || rec.gp_num || ' ' ||
                                   rec.jy_date || '  ' || l_max_num0 || '  ' ||
                                   rec.row_num);*/
            
              return;
          END;
        
          if l_aa1 < l_aa2 then
            l_aa1 := l_aa2;
            l_aa2 := 0;
            if nvl(l_aa1, 0) <> 0 then
              l_point_b := l_top_a0 - l_max_num0 * l_aa1;
            end if;
            l_ldate := rec.jy_date;
          
            l_top_a2 := rec.high_price;
          
          end if;
        
          if ln_aa1 < ln_aa2 then
            ln_aa1   := ln_aa2;
            ln_ldate := rec.jy_date;
            if nvl(ln_aa1, 0) <> 0 then
              ln_point_b := ln(l_top_a0) - l_max_num0 * ln_aa1;
            end if;
            l_top_a2 := rec.high_price;
          
            ln_aa2 := 0;
          end if;
        end if;
      
      end loop;
    
      l_down_a2 := l_ldate;
      l_point_a := l_aa1;
    
      ln_down_a2 := ln_ldate;
      ln_point_a := ln_aa1;
    
      ---------------------------------------------------------------------------------------------------
      --2.突破
      if nvl(l_point_a, 0) <> 0 then
        tp_i      := 0;
        l_tp_b0   := null;
        l_avg_qty := 0;
        l_avgflag := 'N';
        for rec_tp in (select dt.*,
                              (l_point_a * dt.row_num + l_point_b) l_ypoint
                         from gp_jy_d_tmp dt
                        where dt.row_num > l_min_num1) loop
          
        /*  dbms_output.put_line(rec_tp.jy_date || '  ' || l_min_num1 || '  ' ||
                               rec_tp.close_price || '  ' ||
                               rec_tp.l_ypoint);*/
                               dbms_output.put_line(1);
          if tp_i = 0 then
            dbms_output.put_line(rec_tp.gp_num || '  ' ||rec_tp.jy_date);
            begin
              /*select t.avg_qty
                into l_avg_trade
                from gp_avg_dtrade t
               where t.gp_num = rec_tp.gp_num
                 and t.gp_jy_date = rec_tp.jy_date
                 and t.avg_days = 30;*/
                 
                 l_avg_trade := CALCUL_AVG_DQTY(rec_tp.gp_num,rec_tp.jy_date,30);
                 dbms_output.put_line(l_avg_trade);
            exception
              when others then
                l_avg_trade := 99999999999999;
            end;
            select dt.CLOSE_PRICE,
                   (l_point_a * dt.row_num + l_point_b) l_ypoint
              INTO L_PRE_CLOSE, L_TREND_PRICE
              from gp_jy_d_tmp dt
             where dt.row_num = rec_tp.ROW_NUM - 1;
          
            -- ADD BY XUE.XU 2014-11-9
            --modify 2015-10-11 系数从2修改为1
            if rec_tp.jy_quantity >= l_avg_trade * 1 then
              if rec_tp.close_price > rec_tp.l_ypoint AND
                 L_PRE_CLOSE < L_TREND_PRICE then
                tp_i := tp_i + 1;
              else
                tp_i := 0;
              end if;
            else
              tp_i := 0;
            end if;
          else
            if rec_tp.close_price > rec_tp.l_ypoint * 1.03 then
              tp_i := tp_i + 1;
            else
              tp_i := 0;
            end if;
          end if;
        
          /*if rec_tp.close_price > rec_tp.l_ypoint * 1.03 then
            tp_i := tp_i + 1;
          else
            tp_i := 0;
          end if;*/
          if tp_i = 3 then
          
            /* select avg(ydt.jy_quantity)
              into l_avg_qty
              from gp_jy_d_tmp ydt
             where ydt.row_num between rec_tp.row_num - 20 and
                   rec_tp.row_num - 1;
            
            if rec_tp.jy_quantity > l_avg_qty * 1.5 then
              l_avgflag := 'Y';
            else
              l_avgflag := 'N';
            end if;*/
            l_avgflag := 'Y';
            l_tp_b0   := rec_tp.jy_date;
            goto tp_point2;
          
          end if;
        end loop;
      
        --end if;
        <<tp_point2>>
      
        tp_i       := 0;
        ln_tp_b0   := null;
        ln_avg_qty := 0;
        ln_avgflag := 'N';
        --2.71828183
        begin
          for rec_tp in (select dt.*,
                                round(power(2.71828183,
                                            (round(ln_point_a * dt.row_num, 8) +
                                            round(ln_point_b, 8))),
                                      8) ln_ypoint
                           from gp_jy_d_tmp dt
                          where dt.row_num > l_min_num1) loop
          
            /*  --dbms_output.put_line(rec_tp.jy_date || '  ' || l_min_num1 || '  ' ||
            rec_tp.close_price || '  ' ||
            rec_tp.ln_ypoint);*/
            if rec_tp.close_price > rec_tp.ln_ypoint * 1.03 then
              tp_i := tp_i + 1;
            else
              tp_i := 0;
            end if;
            if tp_i = 3 then
            
              select avg(ydt.jy_quantity)
                into ln_avg_qty
                from gp_jy_d_tmp ydt
               where ydt.row_num between rec_tp.row_num - 20 and
                     rec_tp.row_num - 1;
              --modify 2015-10-11 注释
              /*if rec_tp.jy_quantity > l_avg_qty * 1.5 then
                ln_avgflag := 'Y';
              else
                ln_avgflag := 'N';
              end if;*/
              ln_avgflag := 'Y';
              ln_tp_b0   := rec_tp.jy_date;
              goto tp_point;
            
            end if;
          end loop;
        exception
          when others then
            dbms_output.put_line('err:' || l_min_num1 || '-' || ln_point_a || '-' ||
                                 ln_point_b);
        end;
      
      end if;
      <<tp_point>>
    
      ---------------------------------------------------------------------------------------------------
      --3.上升
      --获取上升最高价及对应日期
      select max(t1.high_price)
        into l_upmax_price
        from gp_jy_d_tmp t1
       where t1.row_num > l_min_num1;
    
      begin
        select a.jy_date, a.row_num
          into l_date_b1, l_upmax_num
          from (select rownum rnum, d.*
                  from (select *
                          from gp_jy_d_tmp t1
                         where t1.row_num > l_min_num1
                           and t1.high_price = l_upmax_price
                         order by row_num) d) a
         where rnum = 1;
      exception
        when others then
          l_upmax_num := 0; -----20141113
      end;
    
      --获取最小斜率上升趋势线
      if (l_min_num1 + 20) <= l_upmax_num then
        select a.jy_date,
               l_rate,
               ln_rate,
               a.low_price,
               (a.low_price - l_rate * a.row_num),
               (ln(a.low_price) - l_rate * a.row_num)
          into l_up_b2,
               l_point_c,
               ln_point_c,
               l_low_b2,
               l_point_d,
               ln_point_d
          from (select rownum rnum, d.*
                  from (select jt.*,
                               (l_upmin_price - jt.low_price) /
                               (l_min_num1 - jt.row_num) l_rate,
                               (ln(l_upmin_price) - ln(jt.low_price)) /
                               (l_min_num1 - jt.row_num) ln_rate
                          from gp_jy_d_tmp jt
                         where row_num between (l_min_num1 + 20) and
                               l_upmax_num
                         order by (l_min_price1 - jt.low_price) /
                                  (l_min_num1 - jt.row_num)) d) a
         where rnum = 1;
      end if;
      /*  for rec_up in (select *
                       from gp_jy_d_tmp
                      where row_num between (l_min_num1 + 20) and
                            l_upmax_num
                      order by jy_date) loop
        null;
      end loop;*/
    
      ---------------------------------------------------------------------------------------------------
      insert into gp_trend_result
        (record_id,
         gp_num,
         dates_type,
         down_a0,
         down_a2,
         point_a,
         point_b,
         top_a0,
         top_a2,
         tp_b0,
         up_a1,
         up_b2,
         point_c,
         point_d,
         low_a1,
         low_b2,
         date_b1,
         attribute1,
         attribute2)
      values
        (gp_trend_result_s.nextval,
         l_gp_num,
         l_dates_type,
         l_down_a0,
         l_down_a2,
         l_point_a,
         l_point_b,
         l_top_a0,
         l_top_a2,
         l_tp_b0,
         l_up_a1,
         l_up_b2,
         l_point_c,
         l_point_d,
         l_low_a1,
         l_low_b2,
         l_date_b1,
         '自然数',
         l_avgflag);
    
      insert into gp_trend_result
        (record_id,
         gp_num,
         dates_type,
         down_a0,
         down_a2,
         point_a,
         point_b,
         top_a0,
         top_a2,
         tp_b0,
         up_a1,
         up_b2,
         point_c,
         point_d,
         low_a1,
         low_b2,
         date_b1,
         attribute1,
         attribute2)
      values
        (gp_trend_result_s.nextval,
         l_gp_num,
         l_dates_type,
         l_down_a0,
         ln_down_a2,
         ln_point_a,
         ln_point_b,
         l_top_a0,
         l_top_a2,
         ln_tp_b0,
         l_up_a1,
         l_up_b2,
         ln_point_c,
         ln_point_d,
         l_low_a1,
         l_low_b2,
         l_date_b1,
         '自然对数',
         ln_avgflag);
    
    end loop;
    commit;
  end;

  procedure Trend_main(p_sub in varchar2) is
  
    l_ldate date;
    l_str   varchar2(2000);
  
    l_tactics_code varchar2(20);
  begin
    select decode(p_sub, 'SZ_SH_TREND', 'TACTICS_W_NEW', 'TACTICS_W_NEW')
      into l_tactics_code
      from dual;
    execute immediate 'truncate table gp_trend_result';
    Trend_Analy(200);
    Trend_Analy(400);
    -- Trend_Analy(600);
    -- Trend_Analy(2000);
  
    commit;
  
    delete from closing.GP_TREND_RESULT
     where tactics_code = l_tactics_code;
    insert into closing.GP_TREND_RESULT
      (tradedate,
       branch_code,
       tactics_code,
       record_id,
       gp_num,
       dates_type,
       down_a0,
       down_a2,
       point_a,
       point_b,
       top_a0,
       top_a2,
       tp_b0,
       up_a1,
       up_b2,
       point_c,
       point_d,
       low_a1,
       low_b2,
       date_b1,
       attribute1,
       attribute2,
       attribute3,
       attribute4,
       attribute5,
       attribute6,
       attribute7,
       attribute8,
       attribute9,
       attribute10,
       attribute11,
       attribute12,
       attribute13,
       attribute14,
       attribute15,
       creation_date)
      select to_char(sysdate, 'yyyy-mm-dd'),
             '8888',
             'l_tactics_code',
             record_id,
             gp_num,
             dates_type,
             down_a0,
             down_a2,
             point_a,
             point_b,
             top_a0,
             top_a2,
             tp_b0,
             up_a1,
             up_b2,
             point_c,
             point_d,
             low_a1,
             low_b2,
             date_b1,
             attribute1,
             attribute2,
             attribute3,
             attribute4,
             attribute5,
             attribute6,
             attribute7,
             attribute8,
             attribute9,
             attribute10,
             attribute11,
             attribute12,
             attribute13,
             attribute14,
             attribute15,
             creation_date
      
        from GP_TREND_RESULT;
  
    commit;
  
    for i in 1 .. 10000 loop
      ----dbms_output.put_line(i);
      null;
    end loop;
  
    l_str := 'select GP_NUM     as 股票代码, ' || 'DATES_TYPE as 时间框架, ' ||
             'DOWN_A0    as A0下降趋势第一高点 , ' ||
             'DOWN_A2    as A2下降趋势第二高点   , ' || 'POINT_A    as 系数A  , ' ||
             'POINT_B    as 系数B  , ' || 'TOP_A0     as A0最高价 , ' ||
             'TOP_A2     as A2最高价 , ' || 'TP_B0      as B0下降趋势确认    , ' ||
             'UP_A1      as A1下降趋势拐点   , ' ||
             'UP_B2      as B2上升趋势第二低点   , ' || 'POINT_C    as 系数C  , ' ||
             'POINT_D    as 系数D  , ' || 'LOW_A1     as A1最低价 , ' ||
             'LOW_B2     as B2最低价 , ' || 'DATE_B1    as B1上升趋势最高点   , ' ||
             'ATTRIBUTE1 as 类型, ' || 'attribute2 as flag ' ||
             ' from GP_TREND_RESULT t';
  
    gp_mail_pkg.MAIN(P_JYS => nvl(p_sub, 'SZ_SH_TREND'), p_str => l_str);
  end;
end gp_Trend_pkg;
/
