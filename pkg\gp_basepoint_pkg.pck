﻿create or replace package gpuser.gp_basepoint_pkg is

  -- Author  : ADMINISTRATOR
  -- Created : 2014/11/8 18:31:01
  -- Purpose :

  -- Public type declarations
  function check_bf(p_num number, p_price number) return varchar2;

  --procedure insert_textnew;

  procedure main(p_sub in varchar2);

  procedure send_mail(p_subj in varchar2);

end gp_basepoint_pkg;


 
/
create or replace package body gpuser.gp_basepoint_pkg is

  function check_bf(p_num number, p_price number) return varchar2 is
    l_flag  varchar2(1) := 'N';
    L_PRICE NUMBER;
  begin
    l_flag := 'N';
    select MAX(T.HIGH_PRICE)
      INTO L_PRICE
      from gp_jy_d_tmp t
     where t.row_num between (p_num - 7) and (p_num + 7)
       and t.row_num <> p_num;
    --dbms_output.put_line(l_flag);
    IF L_PRICE < p_price THEN
      l_flag := 'Y';
    END IF;
    return l_flag;
  exception
    when others then
      return 'N';
  end;
  /*procedure insert_textnew is
    l_str   varchar2(2000);
    l_enter varchar2(15) := chr(10);

    l_material_id varchar2(32);
  begin

    for rec_gp in (select distinct gp_num, gp_name
                     from venture.vw_gp_bp_info) loop
      l_str := '<xml>
  <Content><![CDATA[感谢关注熙盈投资！' || l_enter || rec_gp.gp_num ||rec_gp.gp_name||
               ' BP信息：' ;
      for rec in (select *
                    from venture.vw_gp_bp_info t
                   where t.gp_num = rec_gp.gp_num) loop
        l_str := l_str || l_enter || ' ' || rec.bp_type || ' ' ||
                 rec.jy_date || ' ' || rec.low_price || ';';
      end loop;

      l_str := l_str || l_enter || '更多精彩敬请期待!]]></Content>
  <ToUserName><![CDATA[{#ToUserName#}]]></ToUserName>
  <FromUserName><![CDATA[{#FromUserName#}]]></FromUserName>
  <CreateTime><![CDATA[0]]></CreateTime>
  <MsgType><![CDATA[text]]></MsgType>
  <FuncFlag><![CDATA[0]]></FuncFlag>
</xml>';

      begin
        select t.material_id
          into l_material_id
          from wechat.wechat_resp_msg_action t
         where t.key_word = 'B' || rec_gp.gp_num;

        update wechat.wechat_material t
           set t.xml_data = l_str
         where t.id = l_material_id;
        commit;

      exception
        when no_data_found then
          select sys_guid() into l_material_id from dual;

          merge into wechat.wechat_material t
          using (select l_material_id as material_id,
                        sysdate as in_time,
                        'text' as msg_type,
                        l_str as xml_data,
                        '146F23C0D32844879E2B5A149151BCBF' as user_id
                   from dual) a
          on (t.id = a.material_id)
          when matched then
            update set t.xml_data = a.xml_data
          when not matched then
            insert
            values
              (a.material_id, a.in_time, a.msg_type, a.xml_data, a.user_id);

          insert into wechat.wechat_resp_msg_action
            (id,
             action_type,
             event_type,
             ext_type,
             in_time,
             key_word,
             req_type,
             app_id,
             material_id,
             user_id)
          values
            (sys_guid(),
             'material',
             null,
             null,
             sysdate,
             'B' || rec_gp.gp_num,
             'text',
             null,
             l_material_id,
             '146F23C0D32844879E2B5A149151BCBF');
          commit;
      end;

    --select * from wechat_material t where t.id ='531552ea4c2290b7014c22c44336000e' for update
    end loop;
  end;*/
  -- Private type declarations
  procedure main(p_sub in varchar2) is
    l_gp_jy_d_100   gp_jy_d_tmp%rowtype; --前第100天
    l_gp_jy_d_3     gp_jy_d_tmp%rowtype; --三日累计
    l_gp_jy_d_break gp_jy_d_tmp%rowtype; --BPC－break

    l_gp_jy_d_low gp_jy_d_tmp%rowtype;

    --l_bp_price    number := 0;
    l_count number := 0;
    l_flag  varchar2(2) := 'N';
  begin
    execute immediate 'truncate table gp_bp_info';
    for rec_gp in (select * from gp_result_t) loop
      execute immediate 'truncate table gp_jy_d_tmp';

      insert into gp_jy_d_tmp
        select rownum,
               a.RECORD_ID,
               a.GP_NUM,
               a.JY_DATE,
               round(a.OPEN_PRICE / decode(FQ_FACTOR, 0, 1, FQ_FACTOR), 2) OPEN_PRICE,
               round(a.HIGH_PRICE / decode(FQ_FACTOR, 0, 1, FQ_FACTOR), 2) HIGH_PRICE,
               round(a.CLOSE_PRICE / decode(FQ_FACTOR, 0, 1, FQ_FACTOR), 2) CLOSE_PRICE,
               round(a.LOW_PRICE / decode(FQ_FACTOR, 0, 1, FQ_FACTOR), 2) LOW_PRICE,
               a.JY_QUANTITY,
               a.JY_AMOUNT,
               a.FQ_FACTOR,
               a.CREATION_DATE,
               a.LAST_UPDATE_DATE,
               a.ATTRIBUTE1,
               a.ATTRIBUTE2,
               a.ATTRIBUTE3,
               a.ATTRIBUTE4,
               a.ATTRIBUTE5,
               a.ATTRIBUTE6,
               a.ATTRIBUTE7,
               a.ATTRIBUTE8,
               a.ATTRIBUTE9,
               a.ATTRIBUTE10

          from (select *
                  from gp_jy_d
                 where gp_num = rec_gp.gp_num
                 order by jy_date) a;

      select t.*
        into l_gp_jy_d_100
        from gp_jy_d_tmp t
       where t.row_num = (select max(row_num) from gp_jy_d_tmp) - 100;

      --波峰
      for rec_bf in (select *
                       from gp_jy_d_tmp t
                      where t.row_num >= l_gp_jy_d_100.row_num
                        and check_bf(t.row_num, t.high_price) = 'Y') loop
        --dbms_output.put_line(rec_bf.gp_num ||'  ' || rec_bf.jy_date);
        --BP计算1：（回调低点）
        for rec_but in (select *
                          from gp_jy_d_tmp d
                         where d.row_num > rec_bf.row_num
                           and d.low_price =
                               (select min(t.low_price)
                                --into l_bp_price
                                  from gp_jy_d_tmp t
                                 where t.row_num between rec_bf.row_num + 1 and
                                       d.row_num
                                   and t.row_num > rec_bf.row_num)) loop
          ---是否包含波峰日？ 可能存在多个最低价等于波峰到当前日的最积极啊
          --累计三日
          l_count := 0;
          select count(*)
            into l_count
            from gp_jy_d_tmp t
           where t.row_num > rec_but.row_num
             and t.low_price > rec_but.high_price; --累计三日吗？
          --dbms_output.put_line('3 '||l_count);

          --dbms_output.put_line('4 '||rec_bf.row_num || '  ' || rec_bf.high_price);
          if l_count >= 3 then
            begin
              select ROW_NUM,
                     RECORD_ID,
                     GP_NUM,
                     JY_DATE,
                     OPEN_PRICE,
                     HIGH_PRICE,
                     CLOSE_PRICE,
                     LOW_PRICE,
                     JY_QUANTITY,
                     JY_AMOUNT,
                     FQ_FACTOR,
                     CREATION_DATE,
                     LAST_UPDATE_DATE,
                     ATTRIBUTE1,
                     ATTRIBUTE2,
                     ATTRIBUTE3,
                     ATTRIBUTE4,
                     ATTRIBUTE5,
                     ATTRIBUTE6,
                     ATTRIBUTE7,
                     ATTRIBUTE8,
                     ATTRIBUTE9,
                     ATTRIBUTE10
                into l_gp_jy_d_3
                from (select rownum rnum, a.*
                        from (select *
                                from gp_jy_d_tmp t
                               where t.row_num > rec_but.row_num
                                 and t.low_price > rec_BUT.high_price
                               order by Row_num) a)
               where rnum = 3;

              select ROW_NUM,
                     RECORD_ID,
                     GP_NUM,
                     JY_DATE,
                     OPEN_PRICE,
                     HIGH_PRICE,
                     CLOSE_PRICE,
                     LOW_PRICE,
                     JY_QUANTITY,
                     JY_AMOUNT,
                     FQ_FACTOR,
                     CREATION_DATE,
                     LAST_UPDATE_DATE,
                     ATTRIBUTE1,
                     ATTRIBUTE2,
                     ATTRIBUTE3,
                     ATTRIBUTE4,
                     ATTRIBUTE5,
                     ATTRIBUTE6,
                     ATTRIBUTE7,
                     ATTRIBUTE8,
                     ATTRIBUTE9,
                     ATTRIBUTE10
                into l_gp_jy_d_low
                from (select rownum rnum, a.*
                        from (select *
                                from gp_jy_d_tmp t
                               where t.row_num between rec_bf.row_num and
                                     l_gp_jy_d_3.Row_num
                               order by t.low_price) a)
               where rnum = 1;

              --dbms_output.put_line(rec_but.row_num ||'  <>   ' || l_gp_jy_d_low.row_num);

              --最低价也是波峰至三日累计的最低价
              if rec_but.row_num = l_gp_jy_d_low.row_num then
                insert into gp_bp_info
                  (RECORD_ID,
                   GP_NUM,
                   BP_TYPE,
                   JY_DATE,
                   LOW_PRICE,
                   CREATION_DATE,
                   attribute1)
                values
                  (gp_bp_info_s.nextval,
                   rec_gp.gp_num,
                   'BP－bottom',
                   rec_but.jy_date,
                   rec_but.low_price,
                   sysdate,
                   to_char(rec_bf.jy_date, 'yyyy-mm-dd'));

              end if;

            exception
              when others then
                null;
            end;

          end if;
        end loop;

        ---BP计算2：（波峰突破）

        begin
          select ROW_NUM,
                 RECORD_ID,
                 GP_NUM,
                 JY_DATE,
                 OPEN_PRICE,
                 HIGH_PRICE,
                 CLOSE_PRICE,
                 LOW_PRICE,
                 JY_QUANTITY,
                 JY_AMOUNT,
                 FQ_FACTOR,
                 CREATION_DATE,
                 LAST_UPDATE_DATE,
                 ATTRIBUTE1,
                 ATTRIBUTE2,
                 ATTRIBUTE3,
                 ATTRIBUTE4,
                 ATTRIBUTE5,
                 ATTRIBUTE6,
                 ATTRIBUTE7,
                 ATTRIBUTE8,
                 ATTRIBUTE9,
                 ATTRIBUTE10

            into l_gp_jy_d_break
            from (select rownum rnum, a.*
                    from (select *
                            from gp_jy_d_tmp d
                           where d.high_price > rec_bf.high_price * 1.03
                             and d.row_num > rec_bf.row_num
                           order by row_num) a)
           where rnum = 1;

          l_flag := 'N';
          select 'Y' --累计三日or连续三日
            into l_flag
            from (select rownum rnum, a.*
                    from (select *
                            from gp_jy_d_tmp d
                           where d.row_num > l_gp_jy_d_break.row_num
                             and d.low_price > l_gp_jy_d_break.high_price
                           order by row_num) a)
           where rnum = 3;

          if l_flag = 'Y' then
            insert into gp_bp_info
              (RECORD_ID,
               GP_NUM,
               BP_TYPE,
               JY_DATE,
               LOW_PRICE,
               CREATION_DATE,
               attribute1)
            values
              (gp_bp_info_s.nextval,
               rec_gp.gp_num,
               'BP－break',
               l_gp_jy_d_break.jy_date,
               l_gp_jy_d_break.low_price,
               sysdate,
               to_char(rec_bf.jy_date, 'yyyy-mm-dd'));

          end if;

        exception
          when others then
            null;

        end;
      end loop;
      commit;
    end loop;

    COMMIT;

    FOR I IN 1 .. 5000 LOOP
      NULL;
    END LOOP;
    --insert_textnew;

    send_mail(p_subj => nvl(p_sub, 'BP'));
  end;

  procedure send_mail(p_subj in varchar2) is
    l_str varchar2(1000);
  begin
    l_str := 'select distinct gp_num, bp_type,jy_date, low_price' ||
             ' from gp_bp_info' ||
             ' where (gp_num, bp_type, low_price) in ' ||
             ' (select t.gp_num, t.bp_type, max(low_price) low_price ' ||
             ' from gp_bp_info t ' ||
             ' group by t.gp_num, t.bp_type) ORDER BY GP_NUM,BP_TYPE ';

    gp_mail_pkg.MAIN(P_JYS => nvl(p_subj, 'BP'), p_str => l_str);
  end;

end gp_basepoint_pkg;
/
