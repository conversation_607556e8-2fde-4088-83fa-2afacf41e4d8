﻿create or replace package gpuser.gp_dzjy_info_pkg is

  -- Author  : ADMINISTRATOR
  -- Created : 2014/10/31 16:57:17
  -- Purpose :

  -- Public type declarations
  procedure insert_row(p_jy_date   varchar2,
                       p_gp_num    varchar2,
                       p_gp_name   varchar2,
                       p_cj_price  varchar2,
                       p_cj_qty    varchar2,
                       p_cj_amount varchar2,
                       p_buy_yyb   varchar2,
                       p_sale_yyb  varchar2,
                       p_gp_type   varchar2);

  procedure delete_row(p_gp_num varchar2, p_jy_date varchar2);

  procedure send_mail;

end gp_dzjy_info_pkg;


 
/
create or replace package body gpuser.gp_dzjy_info_pkg is

  -- Private type declarations
  procedure insert_row(p_jy_date   varchar2,
                       p_gp_num    varchar2,
                       p_gp_name   varchar2,
                       p_cj_price  varchar2,
                       p_cj_qty    varchar2,
                       p_cj_amount varchar2,
                       p_buy_yyb   varchar2,
                       p_sale_yyb  varchar2,
                       p_gp_type   varchar2) is
  begin
    insert into gp_dzjy_info
      (record_id,
       jy_date,
       gp_num,
       gp_name,
       cj_price,
       cj_qty,
       cj_amount,
       buy_yyb,
       sale_yyb,
       gp_type,
       creation_date)
    values
      (gp_dzjy_info_s.nextval,
       TO_DATE(p_jy_date, 'YYYY-MM-DD'),
       p_gp_num,
       p_gp_name,
       p_cj_price,
       p_cj_qty,
       p_cj_amount,
       p_buy_yyb,
       p_sale_yyb,
       p_gp_type,
       sysdate);

    commit;
  end;

  procedure delete_row(p_gp_num varchar2, p_jy_date varchar2) is
  begin
    delete from gp_dzjy_info t
     where t.gp_num = p_gp_num
       and t.jy_date = to_date(p_jy_date, 'yyyy-mm-dd');
    commit;
  end;

  procedure send_mail is
    l_str varchar2(1000);
    begin
      l_str := 'select JY_DATE   as "交易日期", '||
                       'GP_NUM    as "证券代码", '||
                       'GP_NAME   as "证券简称", '||
                       'CJ_PRICE  as "成交价格(元)", '||
                       'CJ_QTY    as "成交量(万股)", '||
                       'CJ_AMOUNT as "成交金额(万元)", '||
                       'BUY_YYB   as "买方营业部", '||
                       'SALE_YYB  as "卖方营业部", '||
                       'GP_TYPE   as "证券类型", '||
                       'gp_gb_info_pkg.get_qbsum(t.jy_date,t.gp_num) as "总股本(万股)", '||
                       'gp_gb_info_pkg.get_tradeqty(t.jy_date,t.gp_num) as "流通总股本(万股)", '||
                       'decode(gp_gb_info_pkg.get_tradeqty(t.jy_date,t.gp_num),0,null,round(t.CJ_QTY/gp_gb_info_pkg.get_tradeqty(t.jy_date,t.gp_num),8)*100)  as "成交流通占比(%)"  '||
                  'from GP_DZJY_INFO t '||
                  'where t.jy_date >= trunc(sysdate-365) '||
                  'order by t.jy_date desc,t.gp_num,CJ_AMOUNT desc ';

     gp_mail_pkg.MAIN(P_JYS => 'DZJY',p_str => l_str);
    end;

end gp_dzjy_info_pkg;
/
