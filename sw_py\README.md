# U型V型股票技术分析系统

基于Oracle存储过程重构的Python版本股票技术分析系统，用于识别U型和V型技术形态。

## 功能概述
- U型形态识别：左侧高点、右侧高点、底部最低点分析
- V型形态识别：基于U型右侧高点的V型分析
- 交易量验证：放大/收缩条件检查
- 换手率统计：累计和平均换手率计算
- 多线程并行处理：提高分析效率
- Excel报告导出：详细分析结果
- 邮件通知：自动发送分析报告

## 系统要求
- Python 3.8+
- MySQL 8.0+
- 依赖包：pandas, pymysql, openpyxl, smtplib

## 安装和配置

### 1. 数据库配置
编辑 `config/database_config.py`：
```python
MYSQL_CONFIG = {
    'host': 'your_host',
    'port': 3306,
    'user': 'your_username',
    'password': 'your_password',
    'database': 'your_database',
    'charset': 'utf8mb4'
}
```

### 2. 邮件配置
复制 `config/email_config_example.py` 为 `config/email_config.py` 并配置：
```python
EMAIL_CONFIG = {
    'smtp_server': 'smtp.163.com',
    'smtp_port': 465,
    'use_ssl': True,
    'username': '<EMAIL>',
    'password': 'your_auth_code',
    'from_email': '<EMAIL>',
    'to_emails': ['<EMAIL>'],
    'enable_email': True  # 设为False禁用邮件
}
```

### 3. 分析参数配置
编辑 `config/analysis_config.py` 调整分析参数（通常不需要修改）。

## 使用方法

### 基本命令
```bash
# 测试模式：分析前10只股票
python main.py --test --count 10

# 生产模式：分析全部股票
python main.py

# 帮助信息
python main.py --help
```

### 分析流程
1. **初始化数据库表**：创建必要的数据表
2. **处理周线数据**：将日线数据转换为周线数据
3. **计算平均交易量**：计算5、10、20、30日移动平均
4. **执行形态分析**：多线程并行识别U型V型形态
5. **导出Excel报告**：生成详细分析报告
6. **发送邮件通知**：自动发送分析结果

## 技术参数说明

### U型形态参数
- `pre_month`: 10周 - U型前段周期数
- `low_period`: 7周 - U型间隔最低周期
- `left_diff_rate`: 3% - U型左侧高点涨幅限制
- `lr_diff_rate`: 3% - U左右高点价差范围
- `ll_low_diff`: 12% - 左侧高点与底部最低价差下限
- `ll_high_diff`: 50% - 左侧高点与底部最低价差上限

### V型形态参数
- `low_period`: 3周 - V型间隔最低周期
- `low_diff`: 15% - V型底部最低价相对左侧下探限制
- `lr_diff`: 5% - 右侧高点下一周相对左侧高点限制

### 交易量参数
- `volume_amplify_rate`: 1.05 - 交易量放大倍数
- `volume_shrink_rate`: 0.5 - 交易量收缩倍数

## 输出结果

### Excel报告字段
- 股票代码、股票名称
- U型左侧日期、U型左侧价格
- U型右侧日期、U型右侧价格
- U型最低价、U型最低日期
- V型右侧日期、V型右侧价格
- V型最低价、V型最低日期
- 总换手率、平均换手率

### 数据库表
- `stock_weekly_data`：周线数据
- `stock_avg_trade`：平均交易量数据
- `stock_analysis_result`：分析结果

## 性能配置

### 多线程设置
编辑 `config/analysis_config.py`：
```python
THREADING_CONFIG = {
    'max_workers': 4,      # 最大工作线程数
    'timeout': 300,        # 任务超时时间（秒）
    'chunk_size': 50,      # 每批处理的股票数量
}
```

### 数据过滤设置
```python
DATA_FILTER_CONFIG = {
    'exchanges': ['sz', 'sh'],        # 分析的交易所
    'min_trading_days': 100,          # 最少交易天数
    'start_date': '2013-01-01',       # 分析开始日期
    'exclude_st': True,               # 是否排除ST股票
}
```

## 日志和监控
- 日志文件：`logs/stock_analysis.log`
- 日志级别：INFO（可调整为DEBUG查看详细信息）
- 自动轮转：10MB文件大小，保留5个备份

## 故障排除

### 常见问题
1. **数据库连接失败**：检查 `database_config.py` 配置
2. **邮件发送失败**：检查邮箱授权码和SMTP设置
3. **无分析结果**：可能股票数据不符合技术形态条件
4. **内存不足**：减少 `chunk_size` 或 `max_workers`

### 调试模式
```bash
# 启用调试日志
python -c "
from config.analysis_config import LOGGING_CONFIG
LOGGING_CONFIG['level'] = 'DEBUG'
" && python main.py --test --count 3
```

## 与Oracle版本对应关系

### 存储过程映射
- `GP_ANALYSE_PKG.main` → `main.py`
- `INSERT_JYW` → `process_weekly_data`
- `CALCUL_AVG_WQTY` → `calculate_avg_trade_volume`
- `MAIN_W` → `analyze_patterns_multithread`

### 核心函数映射
- `CHECK_ULEFT` → `_check_u_left`
- `GET_URIGHT` → `_get_u_right`
- `CHECK_ULOW` → `_check_u_bottom`
- `GET_VRIGHT` → `_get_v_right`
- `CHECK_VLOW` → `_check_v_bottom`

## 版本历史
- v1.0.0: 初始版本，完成Oracle到Python迁移
- v1.1.0: 修复邮件发送和数据处理问题
- v1.2.0: 完善算法逻辑一致性审计

## 技术支持
如有问题请查看 `AUDIT_REPORT.md` 审计报告或联系开发团队。 