#!/usr/bin/env python3
"""
深入分析U型右侧高点失败原因
"""

import sys
import os
import logging
from collections import defaultdict

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models.database import DatabaseManager, StockDataModel
from analysis.pattern_analyzer import PatternAnalyzer
from config.analysis_config import U_PATTERN_CONFIG, V_PATTERN_CONFIG, VOLUME_CONFIG

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def analyze_u_right_detailed():
    """详细分析U型右侧高点失败原因"""
    
    db = DatabaseManager()
    stock_model = StockDataModel(db)
    analyzer = PatternAnalyzer(U_PATTERN_CONFIG, V_PATTERN_CONFIG, VOLUME_CONFIG)
    
    # 统计各种失败原因
    failure_stats = {
        'price_range_mismatch': 0,      # 价格范围不匹配
        'volume_condition_failed': 0,    # 交易量条件失败
        'no_candidates_found': 0,        # 没有找到候选点
        'time_range_insufficient': 0,    # 时间范围不足
        'total_analyzed': 0
    }
    
    detailed_cases = []
    
    # 分析100只股票
    stock_list = stock_model.get_stock_list()[:100]
    
    for i, stock in enumerate(stock_list):
        stock_code = stock['gp_num']
        stock_name = stock['gp_name']
        
        failure_stats['total_analyzed'] += 1
        
        try:
            weekly_data = stock_model.get_weekly_data(stock_code)
            avg_trade_data = stock_model.get_avg_trade_data(stock_code)
            
            if len(weekly_data) < 20:
                continue
            
            # 找到U型左侧高点
            u_left_candidates = []
            for j in range(U_PATTERN_CONFIG['pre_month'], len(weekly_data)):
                current_high = float(weekly_data[j]['high_price'])
                if analyzer.check_u_left_strict(weekly_data, j, current_high):
                    u_left_candidates.append((j, current_high, weekly_data[j]['trade_date']))
            
            if not u_left_candidates:
                continue
            
            # 分析每个左侧高点为什么找不到右侧高点
            for left_idx, left_price, left_date in u_left_candidates[:3]:  # 分析前3个
                failure_reason = analyze_single_u_right_failure(
                    stock_code, weekly_data, avg_trade_data, left_idx, left_price, analyzer
                )
                
                failure_stats[failure_reason] += 1
                
                if len(detailed_cases) < 20:  # 收集20个详细案例
                    detailed_cases.append({
                        'stock_code': stock_code,
                        'left_idx': left_idx,
                        'left_price': left_price,
                        'left_date': left_date,
                        'failure_reason': failure_reason,
                        'details': get_failure_details(stock_code, weekly_data, avg_trade_data, left_idx, left_price, analyzer)
                    })
                
                break  # 每只股票只分析第一个左侧高点
        
        except Exception as e:
            logger.error(f'分析股票 {stock_code} 失败: {e}')
            continue
        
        if i % 20 == 0:
            logger.info(f'进度: {i}/100')
    
    # 打印分析结果
    print_u_right_analysis_results(failure_stats, detailed_cases)

def analyze_single_u_right_failure(stock_code, weekly_data, avg_trade_data, left_idx, left_price, analyzer):
    """分析单个U型左侧高点为什么找不到右侧高点"""
    
    left_price_float = float(left_price)
    price_range_low = left_price_float * (1 - U_PATTERN_CONFIG['lr_diff_rate'])
    price_range_high = left_price_float * (1 + U_PATTERN_CONFIG['lr_diff_rate'])
    
    candidates_in_range = 0
    volume_failed_count = 0
    no_candidates_after_left = True
    
    # 检查左侧高点之后的数据
    for i in range(left_idx + 1, len(weekly_data)):
        current_data = weekly_data[i]
        high_price = float(current_data['high_price'])
        max_price = max(float(current_data['open_price']), float(current_data['close_price']))
        
        no_candidates_after_left = False
        
        # 检查价格是否在范围内
        if (price_range_low <= high_price <= price_range_high) or (price_range_low <= max_price <= price_range_high):
            candidates_in_range += 1
            
            # 检查交易量条件
            volume_valid = analyzer.check_oracle_volume_condition(
                stock_code, current_data['trade_date'], avg_trade_data
            )
            
            if not volume_valid:
                volume_failed_count += 1
    
    # 判断失败原因
    if no_candidates_after_left:
        return 'time_range_insufficient'
    elif candidates_in_range == 0:
        return 'price_range_mismatch'
    elif volume_failed_count == candidates_in_range:
        return 'volume_condition_failed'
    else:
        return 'no_candidates_found'

def get_failure_details(stock_code, weekly_data, avg_trade_data, left_idx, left_price, analyzer):
    """获取失败的详细信息"""
    
    left_price_float = float(left_price)
    price_range_low = left_price_float * (1 - U_PATTERN_CONFIG['lr_diff_rate'])
    price_range_high = left_price_float * (1 + U_PATTERN_CONFIG['lr_diff_rate'])
    
    details = {
        'left_price': left_price_float,
        'price_range': f'{price_range_low:.2f} - {price_range_high:.2f}',
        'data_after_left': len(weekly_data) - left_idx - 1,
        'candidates_checked': 0,
        'price_matches': 0,
        'volume_matches': 0
    }
    
    for i in range(left_idx + 1, min(left_idx + 21, len(weekly_data))):  # 检查后20周
        current_data = weekly_data[i]
        high_price = float(current_data['high_price'])
        max_price = max(float(current_data['open_price']), float(current_data['close_price']))
        
        details['candidates_checked'] += 1
        
        # 检查价格匹配
        if (price_range_low <= high_price <= price_range_high) or (price_range_low <= max_price <= price_range_high):
            details['price_matches'] += 1
            
            # 检查交易量
            volume_valid = analyzer.check_oracle_volume_condition(
                stock_code, current_data['trade_date'], avg_trade_data
            )
            if volume_valid:
                details['volume_matches'] += 1
    
    return details

def print_u_right_analysis_results(failure_stats, detailed_cases):
    """打印U型右侧高点分析结果"""
    
    total = failure_stats['total_analyzed']
    
    print('\n' + '='*60)
    print('📊 U型右侧高点失败原因详细分析')
    print('='*60)
    
    print(f'\n📈 失败原因统计 (分析了{total}个左侧高点):')
    for reason, count in failure_stats.items():
        if reason != 'total_analyzed':
            percentage = count / total * 100 if total > 0 else 0
            print(f'  {reason}: {count} ({percentage:.1f}%)')
    
    print(f'\n🔍 典型失败案例分析:')
    
    # 按失败原因分组显示案例
    cases_by_reason = defaultdict(list)
    for case in detailed_cases:
        cases_by_reason[case['failure_reason']].append(case)
    
    for reason, cases in cases_by_reason.items():
        print(f'\n{reason.upper()} 案例:')
        for case in cases[:3]:  # 每种原因显示3个案例
            details = case['details']
            print(f'  {case["stock_code"]} (左侧第{case["left_idx"]}周):')
            print(f'    左侧价格: {case["left_price"]:.2f}')
            print(f'    价格范围: {details["price_range"]}')
            print(f'    后续数据: {details["data_after_left"]}周')
            print(f'    检查候选: {details["candidates_checked"]}个')
            print(f'    价格匹配: {details["price_matches"]}个')
            print(f'    交易量匹配: {details["volume_matches"]}个')
    
    print(f'\n💡 关键发现:')
    
    price_mismatch_rate = failure_stats['price_range_mismatch'] / total * 100
    volume_fail_rate = failure_stats['volume_condition_failed'] / total * 100
    
    if price_mismatch_rate > 40:
        print(f'   - 价格范围匹配失败率过高({price_mismatch_rate:.1f}%)，±3%的价格范围可能过于严格')
    
    if volume_fail_rate > 30:
        print(f'   - 交易量条件失败率过高({volume_fail_rate:.1f}%)，1.05倍的交易量要求可能过于严格')
    
    print(f'\n📋 建议优化方案:')
    print(f'   1. 考虑放宽价格匹配范围从±3%到±5%')
    print(f'   2. 考虑放宽交易量条件从1.05倍到1.02倍')
    print(f'   3. 检查Oracle版本的实际交易量验证逻辑')

def test_relaxed_conditions():
    """测试放宽条件后的效果"""
    
    print(f'\n🧪 测试放宽条件的效果:')
    
    db = DatabaseManager()
    stock_model = StockDataModel(db)
    
    # 原始配置
    original_config = U_PATTERN_CONFIG.copy()
    
    # 放宽配置
    relaxed_configs = [
        {'lr_diff_rate': 0.05, 'volume_amplify_rate': 1.03, 'name': '价格±5%,交易量1.03倍'},
        {'lr_diff_rate': 0.08, 'volume_amplify_rate': 1.02, 'name': '价格±8%,交易量1.02倍'},
        {'lr_diff_rate': 0.05, 'volume_amplify_rate': 1.0, 'name': '价格±5%,无交易量要求'},
    ]
    
    test_stocks = ['000001', '000002', '600000', '600036', '000858']
    
    for config in relaxed_configs:
        print(f'\n--- 测试配置: {config["name"]} ---')
        
        # 创建修改后的配置
        test_config = original_config.copy()
        test_config.update(config)
        
        analyzer = PatternAnalyzer(test_config, V_PATTERN_CONFIG, VOLUME_CONFIG)
        
        success_count = 0
        for stock_code in test_stocks:
            stock_info = db.execute_query("SELECT name FROM stock_info_a_code_name WHERE code = %s", (stock_code,))
            stock_name = stock_info[0]['name'] if stock_info else stock_code
            
            weekly_data = stock_model.get_weekly_data(stock_code)
            avg_trade_data = stock_model.get_avg_trade_data(stock_code)
            
            if len(weekly_data) < 20:
                continue
            
            result = analyzer.analyze_stock_patterns_strict(stock_code, stock_name, weekly_data, avg_trade_data)
            if result:
                success_count += 1
        
        print(f'  成功找到形态: {success_count}/{len(test_stocks)} ({success_count/len(test_stocks):.1%})')

def main():
    """主函数"""
    logger.info('🔍 开始深入分析U型右侧高点失败原因')
    
    try:
        analyze_u_right_detailed()
        test_relaxed_conditions()
        
        logger.info('✅ U型右侧高点分析完成')
        
    except Exception as e:
        logger.error(f'❌ 分析失败: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
