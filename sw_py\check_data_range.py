#!/usr/bin/env python3

from models.database import DatabaseManager

db = DatabaseManager()

# 检查数据时间范围
print('=== 检查数据时间范围 ===')
time_range = db.execute_query('''
    SELECT 
        MIN(trade_date) as min_date, 
        MAX(trade_date) as max_date,
        COUNT(*) as total_count
    FROM stock_weekly_data
''')

if time_range:
    result = time_range[0]
    print(f'周线数据时间范围: {result["min_date"]} ~ {result["max_date"]}')
    print(f'总记录数: {result["total_count"]}')

# 检查2013年后的数据
post_2013 = db.execute_query('''
    SELECT COUNT(*) as count
    FROM stock_weekly_data 
    WHERE trade_date >= '2013-01-01'
''')

if post_2013:
    print(f'2013年后的数据: {post_2013[0]["count"]} 条')

# 检查最近几年的数据分布
yearly_dist = db.execute_query('''
    SELECT 
        YEAR(trade_date) as year,
        COUNT(*) as count
    FROM stock_weekly_data 
    GROUP BY YEAR(trade_date)
    ORDER BY year DESC
    LIMIT 10
''')

print('\n=== 年度数据分布 ===')
for row in yearly_dist:
    print(f'{row["year"]}年: {row["count"]} 条')

# 检查Oracle可能使用的数据范围
oracle_range = db.execute_query('''
    SELECT COUNT(*) as count
    FROM stock_weekly_data 
    WHERE trade_date >= '2013-01-01' AND trade_date < '2024-01-01'
''')

if oracle_range:
    print(f'\n2013-2023年数据: {oracle_range[0]["count"]} 条')

# 检查股票数量在不同年份的分布
stock_count_by_year = db.execute_query('''
    SELECT 
        YEAR(trade_date) as year,
        COUNT(DISTINCT stock_code) as stock_count
    FROM stock_weekly_data 
    GROUP BY YEAR(trade_date)
    ORDER BY year DESC
    LIMIT 5
''')

print('\n=== 各年份股票数量 ===')
for row in stock_count_by_year:
    print(f'{row["year"]}年: {row["stock_count"]} 只股票')

# 检查是否有历史数据（2013-2022年）
historical_check = db.execute_query('''
    SELECT 
        COUNT(*) as count,
        COUNT(DISTINCT stock_code) as stock_count
    FROM stock_weekly_data 
    WHERE trade_date >= '2013-01-01' AND trade_date <= '2022-12-31'
''')

if historical_check:
    result = historical_check[0]
    print(f'\n2013-2022年历史数据: {result["count"]} 条记录, {result["stock_count"]} 只股票')
