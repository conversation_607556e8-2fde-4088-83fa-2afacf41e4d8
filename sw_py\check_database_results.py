#!/usr/bin/env python3
"""
检查数据库中的分析结果
查看实际保存的数据是否包含修复后的值
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.database import DatabaseManager

def check_database_results():
    """检查数据库中的分析结果"""
    
    print('🔍 检查数据库中的分析结果')
    print('=' * 60)
    
    try:
        print('🔗 连接数据库...')
        db = DatabaseManager()
        print('✅ 数据库连接成功')

        # 检查表是否存在
        sql_check_table = """
        SHOW TABLES LIKE 'stock_analysis_result'
        """

        table_result = db.execute_query(sql_check_table)
        if not table_result:
            print('❌ stock_analysis_result 表不存在')
            return

        print('✅ stock_analysis_result 表存在')

        # 获取最新批次的分析结果
        sql_batch = """
        SELECT DISTINCT batch_id
        FROM stock_analysis_result
        ORDER BY batch_id DESC
        LIMIT 1
        """

        print('🔍 查询最新批次...')
        batch_result = db.execute_query(sql_batch)
        if not batch_result:
            print('❌ 数据库中没有分析结果')

            # 检查表中是否有任何数据
            sql_count = "SELECT COUNT(*) as count FROM stock_analysis_result"
            count_result = db.execute_query(sql_count)
            if count_result:
                print(f'📊 表中总记录数: {count_result[0]["count"]}')
            return
        
        latest_batch_id = batch_result[0]['batch_id']
        print(f'📊 最新批次ID: {latest_batch_id}')
        
        # 获取该批次的详细结果
        sql_details = """
        SELECT stock_code, stock_name, ma_num, all_turnover_rate, avg_turnover_rate,
               is_breakthrough, breakthrough_price, breakthrough_date,
               u_left_date, u_left_price, v_right_date, v_right_price
        FROM stock_analysis_result 
        WHERE batch_id = %s
        ORDER BY id
        LIMIT 10
        """
        
        results = db.execute_query(sql_details, (latest_batch_id,))
        
        if not results:
            print(f'❌ 批次 {latest_batch_id} 中没有数据')
            return
        
        print(f'📋 批次 {latest_batch_id} 中共有 {len(results)} 条记录')
        print('\n🔍 前10条记录的关键字段:')
        print('=' * 120)
        print(f'{"股票代码":<10} {"股票名称":<15} {"MA支撑点":<8} {"累计换手率":<12} {"平均换手率":<12} {"是否突破":<8} {"突破价格":<10}')
        print('-' * 120)
        
        ma_values = set()
        turnover_values = set()
        avg_turnover_values = set()
        breakthrough_values = set()
        
        for result in results:
            stock_code = result['stock_code']
            stock_name = result['stock_name'][:12] + '...' if len(result['stock_name']) > 12 else result['stock_name']
            ma_num = result['ma_num']
            all_turnover = result['all_turnover_rate']
            avg_turnover = result['avg_turnover_rate']
            is_breakthrough = '是' if result['is_breakthrough'] else '否'
            breakthrough_price = result['breakthrough_price'] or 0
            
            ma_values.add(ma_num)
            turnover_values.add(all_turnover)
            avg_turnover_values.add(avg_turnover)
            breakthrough_values.add(is_breakthrough)
            
            print(f'{stock_code:<10} {stock_name:<15} {ma_num:<8} {all_turnover:<12.6f} {avg_turnover:<12.6f} {is_breakthrough:<8} {breakthrough_price:<10.2f}')
        
        # 统计字段差异化情况
        print('\n📊 字段差异化统计:')
        print('=' * 60)
        print(f'MA支撑点: {len(ma_values)} 个不同值 - {sorted(ma_values)}')
        print(f'累计换手率: {len(turnover_values)} 个不同值 - {sorted(list(turnover_values))[:5]}...')
        print(f'平均换手率: {len(avg_turnover_values)} 个不同值 - {sorted(list(avg_turnover_values))[:5]}...')
        print(f'是否突破: {len(breakthrough_values)} 个不同值 - {sorted(breakthrough_values)}')
        
        # 检查是否所有值都相同
        print('\n🎯 修复效果评估:')
        print('=' * 60)
        
        if len(ma_values) == 1:
            print(f'❌ MA支撑点: 所有股票都是 {list(ma_values)[0]}')
        else:
            print(f'✅ MA支撑点: {len(ma_values)} 个不同值，修复成功')
        
        if len(turnover_values) == 1 and list(turnover_values)[0] == 0:
            print(f'❌ 累计换手率: 所有股票都是 0')
        else:
            print(f'✅ 累计换手率: {len(turnover_values)} 个不同值，修复成功')
        
        if len(avg_turnover_values) == 1 and list(avg_turnover_values)[0] == 0:
            print(f'❌ 平均换手率: 所有股票都是 0')
        else:
            print(f'✅ 平均换手率: {len(avg_turnover_values)} 个不同值，修复成功')
        
        if len(breakthrough_values) == 1:
            print(f'❌ 是否突破: 所有股票都是 {list(breakthrough_values)[0]}')
        else:
            print(f'✅ 是否突破: {len(breakthrough_values)} 个不同值，修复成功')
        
        # 获取全部数据的统计
        sql_all = """
        SELECT COUNT(*) as total_count,
               COUNT(DISTINCT ma_num) as ma_distinct,
               COUNT(DISTINCT all_turnover_rate) as turnover_distinct,
               COUNT(DISTINCT avg_turnover_rate) as avg_turnover_distinct,
               COUNT(DISTINCT is_breakthrough) as breakthrough_distinct
        FROM stock_analysis_result 
        WHERE batch_id = %s
        """
        
        stats = db.execute_query(sql_all, (latest_batch_id,))
        if stats:
            stat = stats[0]
            print(f'\n📈 全部 {stat["total_count"]} 条记录的统计:')
            print('=' * 60)
            print(f'MA支撑点不同值数量: {stat["ma_distinct"]}')
            print(f'累计换手率不同值数量: {stat["turnover_distinct"]}')
            print(f'平均换手率不同值数量: {stat["avg_turnover_distinct"]}')
            print(f'是否突破不同值数量: {stat["breakthrough_distinct"]}')
            
            # 计算修复成功率
            success_count = 0
            total_fields = 4
            
            if stat["ma_distinct"] > 1:
                success_count += 1
            if stat["turnover_distinct"] > 1:
                success_count += 1
            if stat["avg_turnover_distinct"] > 1:
                success_count += 1
            if stat["breakthrough_distinct"] > 1:
                success_count += 1
            
            success_rate = (success_count / total_fields) * 100
            print(f'\n🎯 数据库修复成功率: {success_rate:.1f}% ({success_count}/{total_fields})')
        
    except Exception as e:
        print(f'❌ 检查数据库结果失败: {e}')
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    check_database_results()

if __name__ == "__main__":
    main()
