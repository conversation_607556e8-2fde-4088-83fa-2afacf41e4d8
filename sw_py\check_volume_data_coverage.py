#!/usr/bin/env python3
"""
检查交易量数据覆盖情况
"""

import sys
import os
import logging

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models.database import DatabaseManager

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_volume_data_coverage():
    """检查交易量数据覆盖情况"""
    
    print('🔍 检查交易量数据覆盖情况')
    
    db = DatabaseManager()
    
    # 检查总体数据分布
    print(f'\n📊 总体数据分布:')
    
    # 周线数据分布
    weekly_dist = db.execute_query('''
        SELECT 
            YEAR(trade_date) as year,
            COUNT(*) as count
        FROM stock_weekly_data 
        GROUP BY YEAR(trade_date)
        ORDER BY year
    ''')
    
    print(f'周线数据分布:')
    for row in weekly_dist:
        print(f'  {row["year"]}年: {row["count"]:,}条')
    
    # 5日平均交易量数据分布
    avg_dist = db.execute_query('''
        SELECT 
            YEAR(trade_date) as year,
            COUNT(*) as count
        FROM stock_avg_trade 
        WHERE avg_days = 5
        GROUP BY YEAR(trade_date)
        ORDER BY year
    ''')
    
    print(f'\n5日平均交易量数据分布:')
    for row in avg_dist:
        print(f'  {row["year"]}年: {row["count"]:,}条')
    
    # 检查具体股票的数据覆盖
    print(f'\n🔍 具体股票数据覆盖检查:')
    
    test_stocks = ['000001', '000002', '600000']
    
    for stock_code in test_stocks:
        print(f'\n--- 股票 {stock_code} ---')
        
        # 周线数据范围
        weekly_range = db.execute_query('''
            SELECT 
                MIN(trade_date) as min_date,
                MAX(trade_date) as max_date,
                COUNT(*) as count
            FROM stock_weekly_data 
            WHERE stock_code = %s
        ''', (stock_code,))
        
        if weekly_range:
            row = weekly_range[0]
            print(f'周线数据: {row["min_date"]} 到 {row["max_date"]} ({row["count"]}条)')
        
        # 5日平均交易量数据范围
        avg_range = db.execute_query('''
            SELECT 
                MIN(trade_date) as min_date,
                MAX(trade_date) as max_date,
                COUNT(*) as count
            FROM stock_avg_trade 
            WHERE stock_code = %s AND avg_days = 5
        ''', (stock_code,))
        
        if avg_range:
            row = avg_range[0]
            print(f'5日均量: {row["min_date"]} 到 {row["max_date"]} ({row["count"]}条)')
        
        # 检查数据匹配情况
        match_check = db.execute_query('''
            SELECT 
                COUNT(DISTINCT w.trade_date) as weekly_dates,
                COUNT(DISTINCT a.trade_date) as avg_dates,
                COUNT(DISTINCT CASE WHEN a.trade_date IS NOT NULL THEN w.trade_date END) as matched_dates
            FROM stock_weekly_data w
            LEFT JOIN stock_avg_trade a ON w.stock_code = a.stock_code AND w.trade_date = a.trade_date AND a.avg_days = 5
            WHERE w.stock_code = %s
        ''', (stock_code,))
        
        if match_check:
            row = match_check[0]
            match_rate = row["matched_dates"] / row["weekly_dates"] * 100
            print(f'数据匹配: {row["matched_dates"]}/{row["weekly_dates"]} ({match_rate:.1f}%)')
        
        # 检查2024年数据缺失情况
        missing_2024 = db.execute_query('''
            SELECT 
                w.trade_date
            FROM stock_weekly_data w
            LEFT JOIN stock_avg_trade a ON w.stock_code = a.stock_code AND w.trade_date = a.trade_date AND a.avg_days = 5
            WHERE w.stock_code = %s 
                AND YEAR(w.trade_date) = 2024
                AND a.trade_date IS NULL
            ORDER BY w.trade_date
            LIMIT 10
        ''', (stock_code,))
        
        if missing_2024:
            print(f'2024年缺失的5日均量数据 (前10个):')
            for row in missing_2024:
                print(f'  {row["trade_date"]}')

def analyze_volume_data_gap():
    """分析交易量数据缺口"""
    
    print(f'\n🔍 分析交易量数据缺口:')
    
    db = DatabaseManager()
    
    # 按月统计数据缺失情况
    gap_analysis = db.execute_query('''
        SELECT 
            YEAR(w.trade_date) as year,
            MONTH(w.trade_date) as month,
            COUNT(w.trade_date) as weekly_count,
            COUNT(a.trade_date) as avg_count,
            COUNT(w.trade_date) - COUNT(a.trade_date) as missing_count
        FROM stock_weekly_data w
        LEFT JOIN stock_avg_trade a ON w.stock_code = a.stock_code AND w.trade_date = a.trade_date AND a.avg_days = 5
        WHERE w.stock_code = '000001'  -- 使用000001作为样本
        GROUP BY YEAR(w.trade_date), MONTH(w.trade_date)
        ORDER BY year, month
    ''')
    
    print(f'000001股票按月数据缺失分析:')
    print(f'年月\t周线\t5日均量\t缺失\t缺失率')
    
    for row in gap_analysis:
        missing_rate = row["missing_count"] / row["weekly_count"] * 100 if row["weekly_count"] > 0 else 0
        print(f'{row["year"]}-{row["month"]:02d}\t{row["weekly_count"]}\t{row["avg_count"]}\t{row["missing_count"]}\t{missing_rate:.1f}%')

def suggest_solutions():
    """建议解决方案"""
    
    print(f'\n💡 解决方案建议:')
    
    print(f'1. 数据问题根源:')
    print(f'   - 5日平均交易量数据主要集中在2023年')
    print(f'   - 2024-2025年的5日平均交易量数据大量缺失')
    print(f'   - 导致所有2024-2025年的U型右侧高点都无法通过交易量验证')
    
    print(f'\n2. 临时解决方案:')
    print(f'   - 选项A: 放宽交易量验证条件，允许无5日均量数据时跳过验证')
    print(f'   - 选项B: 使用当周交易量与历史平均值比较')
    print(f'   - 选项C: 重新生成2024-2025年的5日平均交易量数据')
    
    print(f'\n3. 推荐方案:')
    print(f'   - 优先选择选项A：修改交易量验证逻辑，当缺少5日均量数据时跳过验证')
    print(f'   - 这样可以快速验证其他逻辑是否正确')
    print(f'   - 后续再补充完整的交易量数据')

def test_without_volume_check():
    """测试跳过交易量检查的效果"""
    
    print(f'\n🧪 测试跳过交易量检查的效果:')
    
    db = DatabaseManager()
    
    # 检查如果跳过交易量验证，有多少U型右侧高点可以通过
    test_query = '''
        SELECT 
            w1.trade_date as left_date,
            w1.high_price as left_price,
            w2.trade_date as right_date,
            w2.high_price as right_price
        FROM stock_weekly_data w1
        JOIN stock_weekly_data w2 ON w1.stock_code = w2.stock_code
        WHERE w1.stock_code = '000001'
            AND w2.trade_date > w1.trade_date
            AND w2.high_price BETWEEN w1.high_price * 0.97 AND w1.high_price * 1.03  -- ±3%价格范围
            AND DATEDIFF(w2.trade_date, w1.trade_date) >= 35  -- 至少5周间隔
        ORDER BY w1.trade_date, w2.trade_date
        LIMIT 10
    '''
    
    potential_pairs = db.execute_query(test_query)
    
    print(f'000001股票潜在的U型左右高点配对 (忽略交易量):')
    for row in potential_pairs:
        left_price = float(row['left_price'])
        right_price = float(row['right_price'])
        price_diff = abs(right_price - left_price) / left_price * 100
        print(f'  {row["left_date"]} ({left_price:.2f}) → {row["right_date"]} ({right_price:.2f}) 价差{price_diff:.1f}%')

def main():
    """主函数"""
    logger.info('🔍 开始检查交易量数据覆盖情况')
    
    try:
        check_volume_data_coverage()
        analyze_volume_data_gap()
        suggest_solutions()
        test_without_volume_check()
        
        logger.info('✅ 交易量数据检查完成')
        
    except Exception as e:
        logger.error(f'❌ 检查失败: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
