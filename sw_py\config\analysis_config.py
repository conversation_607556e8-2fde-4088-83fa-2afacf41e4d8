"""
技术分析配置参数
专门用于股票形态分析的核心参数配置
"""

# U型形态分析参数 - 严格对应Oracle包的参数
U_PATTERN_CONFIG = {
    'pre_month': 10,                  # U型前段时间周期（周）- Oracle: U_PREMONTH = 10
    'left_diff_rate': 0.03,           # U型左侧高点涨幅限制3% - Oracle: U_LDIFF_RATE = 0.03
    'lr_diff_rate': 0.03,             # U左右高点价差±3% - Oracle: U_LR_DIFF_RATE = 0.03
    'll_low_diff': 0.12,              # 左侧高点与底部最低价差下限12% - Oracle: U_LL_LDIFF = 0.12
    'll_high_diff': 0.50,             # 左侧高点与底部最低价差上限50% - Oracle: U_LL_HDIFF = 0.5
    'volume_amplify_rate': 1.05,      # 右侧高点交易量放大倍数1.05 - Oracle: (dt.avg_qty * 1.05) <= wt.jy_quantity
    'low_period': 7,                  # U型低点间隔最少7周 - Oracle: U_LPERIOD = 7
    'high_period': 6000,              # U型高点间隔最大6000周 - Oracle: U_HPERIOD = 6000
}

# V型形态分析参数 - 严格对应Oracle包的参数
V_PATTERN_CONFIG = {
    'low_diff': 0.15,                 # V型底部下探幅度15% - Oracle: V_LLOW_DIFF = 0.15
    'lr_diff': 0.05,                  # V型左右高点差异5% - Oracle: V_LR_DIFF = 0.05
    'low_period': 3,                  # V型最低间隔周期3周 - Oracle: V_LPERIOD = 3
    'volume_shrink_threshold': 0.5,   # V型交易量收缩阈值0.5 - Oracle: if l_qty * 0.5 > l_avg_qty
}

# 交易量分析参数
VOLUME_CONFIG = {
    'avg_days': [5, 10, 20, 30],      # 计算平均交易量的天数 - Oracle: 5, 10, 20, 30日均量
    # 注意：volume_amplify_rate已在U_PATTERN_CONFIG中定义，避免重复
}

# 多线程配置 - 性能优化配置
THREADING_CONFIG = {
    # 核心并行参数 - 根据CPU核心数优化
    'max_workers': 12,                # 分析线程的最大工作线程数（提升到12）
    'default_threads': 4,             # 默认线程数（提升基准）

    # 各步骤专用线程配置 - 优化并行度
    'weekly_data_threads': 6,         # 第2步处理周线数据的线程数（减少I/O竞争）
    'avg_volume_threads': 6,          # 第3步计算平均交易量的线程数（减少I/O竞争）
    'moving_average_threads': 6,      # 第3.5步计算移动平均线的线程数
    'volatility_threads': 6,          # 第3.6步计算波动率的线程数
    'pattern_analysis_threads': 12,   # 第4步形态分析的线程数（CPU密集型，提升并行度）

    # 性能控制参数 - 优化批处理大小
    'timeout': 1200,                  # 任务超时时间（秒）- 增加超时时间
    'chunk_size': 100,                # 每批处理的股票数量（增大批次减少线程切换）
    'batch_insert_size': 5000,        # 批量插入的大小（增大批次提升I/O效率）
    'data_processing_chunk_size': 100, # 数据处理批次大小（增大批次）
}

# 数据过滤配置
DATA_FILTER_CONFIG = {
    # 注意：不再按交易所过滤，因为数据表结构变更，从stock_info_a_code_name获取的股票都需要分析
    'min_trading_days': 100,          # 最少交易天数
    'start_date': '2013-01-01',       # 分析开始日期（首次运行使用）- 与Oracle版本保持一致
    'exclude_st': False,               # 是否排除ST股票
    'min_weekly_data': 20,            # 最少周线数据量
    'min_daily_data': 50,             # 最少日线数据量

    # 增量处理相关配置
    'incremental_start_date': None,   # 增量处理开始日期（动态计算）
    'use_incremental': True,          # 是否使用增量处理
}

# 执行流程控制配置 - 控制程序执行哪些步骤
EXECUTION_CONFIG = {
    # 主要执行模式控制
    'only_analysis': True,            # 仅执行分析（跳过第1-3步数据准备，直接执行第4步形态分析）
    
    # 细分步骤控制（当only_analysis=False时有效）
    'skip_table_init': False,         # 跳过数据库表初始化（第1步）
    'skip_weekly_data': False,        # 跳过周线数据处理（第2步）
    'skip_avg_volume': False,         # 跳过平均交易量计算（第3步）
    'skip_moving_average': False,     # 跳过移动平均线计算（第3.5步）- 新增
    'skip_volatility': False,         # 跳过波动率计算（第3.6步）- 新增
    
    # 后续步骤控制
    'skip_export': False,             # 跳过Excel导出（第5步）
    'skip_email': False,              # 跳过邮件通知（第6步）
    
    # 数据验证控制
    'validate_data_before_analysis': True,  # 当跳过数据准备时，是否验证数据完整性
    'skip_data_collection': False,    # 跳过数据收集（第1步）
    'skip_data_processing': False,    # 跳过数据处理（第2步）
    'skip_pattern_analysis': False,   # 跳过形态分析（第4步）
    'skip_result_completion': False,  # 跳过结果完善（第5步）
}

# 调试配置 - 用于排查形态识别问题
DEBUG_CONFIG = {
    'enable_detailed_logging': False,  # 关闭详细日志
    'save_intermediate_data': False,   # 关闭中间数据保存
    'log_pattern_details': False,      # 关闭形态识别详细信息
    'test_single_stock': None,         # 不测试单只股票
    'enable_detailed_logs': False,    # 启用详细日志
    'save_intermediate_results': False, # 保存中间结果
    'print_filter_statistics': True,  # 打印筛选统计
}

# 移动平均线配置 - 新增
MOVING_AVERAGE_CONFIG = {
    'periods': list(range(1, 21)),     # 计算1-20周期的移动平均线（与Oracle一致）
    'min_data_points': 20,            # 最少需要20周数据才计算移动平均线
    'precision': 2,                   # 保留2位小数（与Oracle round(,2)一致）
    'use_oracle_logic': True,         # 使用Oracle CALCULATION_AVG_JYM逻辑
    'batch_size': 1000,               # 批量插入大小
}

# 波动率配置 - 新增
VOLATILITY_CONFIG = {
    'atr_period': 20,                 # 20日平均真实波幅（与Oracle一致）
    'min_data_points': 20,            # 最少需要20日数据才计算波动率
    'precision': 6,                   # 保留6位小数
    'use_oracle_logic': True,         # 使用Oracle CALCU_FLUCT逻辑
    'batch_size': 1000,               # 批量插入大小
} 