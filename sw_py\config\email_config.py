"""
邮件配置文件
注意：这是测试配置，实际使用请填写真实的邮箱信息
"""

EMAIL_CONFIG = {
    'smtp_server': 'smtp.exmail.qq.com',
    'smtp_port': 465,
    'use_ssl': True,
    
    # 测试配置（实际使用时请修改）
    'username': '<EMAIL>',               # 发件人邮箱
    'password': 'cAkcpgvAtBgfnT3W',             # 邮箱授权码
    'from_email': '<EMAIL>',             # 发件人邮箱
    'to_emails': ["<EMAIL>", "<EMAIL>", "<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>"],        # 收件人邮箱列表
    
    'subject': '杯柄策略技术分析结果报告',
    'enable_email': True,                    # 设为真实发送邮件，False禁用邮件发送
}

# 使用说明：
# 1. 复制此文件为 email_config.py
# 2. 填写实际的邮箱信息
# 3. 获取邮箱授权码（不是登录密码）
#    - 163邮箱：登录网页版 -> 设置 -> POP3/SMTP/IMAP -> 开启SMTP服务 -> 获取授权码
#    - QQ邮箱：登录网页版 -> 设置 -> 账户 -> 开启SMTP服务 -> 获取授权码
# 4. 重新运行分析程序 