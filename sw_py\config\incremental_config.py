"""
增量数据处理配置
用于优化非首次执行时的数据处理性能
"""

from datetime import datetime, timedelta

# 增量处理配置
INCREMENTAL_CONFIG = {
    # 增量处理开关
    'enable_incremental': True,           # 是否启用增量处理
    'force_full_rebuild': False,          # 强制全量重建（忽略增量设置）
    
    # 首次运行检测
    'first_run_detection': {
        'method': 'table_check',          # 检测方法: 'table_check', 'marker_file', 'config_flag'
        'marker_file': 'data/.last_run_marker',  # 标记文件路径
        'min_data_threshold': 1000,       # 判断为首次运行的最小数据量阈值
    },
    
    # 增量时间窗口配置
    'time_window': {
        'default_months': 3,              # 默认重算最近3个月数据
        'min_months': 1,                  # 最小1个月
        'max_months': 12,                 # 最大12个月
        'overlap_days': 7,                # 重叠天数，确保数据连续性
    },
    
    # 不同数据类型的增量策略
    'data_strategies': {
        'weekly_data': {
            'enabled': True,
            'months': 3,                  # 重算最近3个月周线数据
            'overlap_weeks': 2,           # 重叠2周确保连续性
        },
        'avg_trade': {
            'enabled': True,
            'months': 3,                  # 重算最近3个月平均交易量
            'overlap_weeks': 4,           # 重叠4周（因为需要计算30日均量）
        },
        'moving_average': {
            'enabled': True,
            'months': 6,                  # 重算最近6个月移动平均线（需要更多历史数据）
            'overlap_weeks': 20,          # 重叠20周（因为需要计算20周期移动平均）
        },
        'volatility': {
            'enabled': True,
            'months': 3,                  # 重算最近3个月波动率
            'overlap_weeks': 4,           # 重叠4周
        },
        'analysis_result': {
            'enabled': True,
            'always_full': True,          # 分析结果总是全量重算（因为依赖所有历史数据）
        }
    },
    
    # 性能优化配置
    'performance': {
        'batch_delete_size': 10000,       # 批量删除大小
        'parallel_cleanup': True,         # 并行清理旧数据
        'vacuum_after_cleanup': False,    # 清理后是否执行VACUUM（MySQL不需要）
    },
    
    # 数据完整性检查
    'integrity_check': {
        'enabled': True,                  # 是否启用完整性检查
        'check_continuity': True,         # 检查数据连续性
        'check_overlap': True,            # 检查重叠区域数据一致性
        'auto_fix_gaps': True,            # 自动修复数据缺口
    },
    
    # 日志和监控
    'monitoring': {
        'log_time_savings': True,         # 记录时间节省情况
        'log_data_volumes': True,         # 记录数据量统计
        'alert_on_anomalies': True,       # 异常情况告警
    }
}

# 增量处理辅助函数
class IncrementalHelper:
    """增量处理辅助类"""
    
    @staticmethod
    def calculate_incremental_date_range(months: int = None, overlap_days: int = 7):
        """
        计算增量处理的日期范围
        
        Args:
            months: 重算月数，默认使用配置值
            overlap_days: 重叠天数
            
        Returns:
            tuple: (start_date, end_date)
        """
        if months is None:
            months = INCREMENTAL_CONFIG['time_window']['default_months']
        
        end_date = datetime.now().date()
        start_date = end_date - timedelta(days=months * 30 + overlap_days)
        
        return start_date, end_date
    
    @staticmethod
    def get_data_strategy(data_type: str) -> dict:
        """
        获取指定数据类型的增量策略
        
        Args:
            data_type: 数据类型 ('weekly_data', 'avg_trade', 'moving_average', 'volatility')
            
        Returns:
            dict: 策略配置
        """
        strategies = INCREMENTAL_CONFIG['data_strategies']
        return strategies.get(data_type, {})
    
    @staticmethod
    def is_first_run(db_manager) -> bool:
        """
        检测是否为首次运行
        
        Args:
            db_manager: 数据库管理器实例
            
        Returns:
            bool: True表示首次运行
        """
        config = INCREMENTAL_CONFIG['first_run_detection']
        method = config['method']
        
        if method == 'table_check':
            # 检查关键表的数据量
            try:
                result = db_manager.execute_query(
                    "SELECT COUNT(*) as count FROM stock_weekly_data"
                )
                if result and len(result) > 0:
                    count = result[0]['count']
                    return count < config['min_data_threshold']
                return True
            except:
                return True
                
        elif method == 'marker_file':
            # 检查标记文件
            import os
            return not os.path.exists(config['marker_file'])
            
        elif method == 'config_flag':
            # 检查配置标志
            return INCREMENTAL_CONFIG.get('force_full_rebuild', False)
        
        return True
    
    @staticmethod
    def create_run_marker():
        """创建运行标记文件"""
        config = INCREMENTAL_CONFIG['first_run_detection']
        if config['method'] == 'marker_file':
            import os
            marker_file = config['marker_file']
            
            # 确保目录存在
            os.makedirs(os.path.dirname(marker_file), exist_ok=True)
            
            # 创建标记文件
            with open(marker_file, 'w') as f:
                f.write(f"Last run: {datetime.now().isoformat()}\n")
    
    @staticmethod
    def get_incremental_sql_condition(data_type: str, table_alias: str = '') -> tuple:
        """
        获取增量查询的SQL条件
        
        Args:
            data_type: 数据类型
            table_alias: 表别名
            
        Returns:
            tuple: (where_condition, params)
        """
        strategy = IncrementalHelper.get_data_strategy(data_type)
        if not strategy.get('enabled', False):
            return '', []
        
        months = strategy.get('months', 3)
        overlap_weeks = strategy.get('overlap_weeks', 2)
        overlap_days = overlap_weeks * 7
        
        start_date, end_date = IncrementalHelper.calculate_incremental_date_range(
            months, overlap_days
        )
        
        alias_prefix = f"{table_alias}." if table_alias else ""
        where_condition = f"{alias_prefix}trade_date >= %s"
        params = [start_date]
        
        return where_condition, params

# 导出配置和辅助类
__all__ = ['INCREMENTAL_CONFIG', 'IncrementalHelper']
