"""
日志配置文件
用于配置程序的日志输出格式和级别
"""

import os

# 日志配置
LOGGING_CONFIG = {
    'level': 'INFO',
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    'file': 'logs/stock_analysis.log',
    'max_size': 10 * 1024 * 1024,    # 10MB
    'backup_count': 5,
    'encoding': 'utf-8',
}

# 调试日志配置
DEBUG_LOGGING_CONFIG = {
    'level': 'DEBUG',
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s',
    'file': 'logs/pattern_debug.log',
    'max_size': 50 * 1024 * 1024,    # 50MB
    'backup_count': 3,
    'encoding': 'utf-8',
}

# 数据处理日志配置
DATA_PROCESSING_LOGGING_CONFIG = {
    'level': 'INFO',
    'format': '%(asctime)s - [%(levelname)s] %(name)s - %(message)s',
    'file': 'logs/data_processing.log',
    'max_size': 20 * 1024 * 1024,    # 20MB
    'backup_count': 3,
    'encoding': 'utf-8',
}

# MA支撑点调试日志配置
MA_DEBUG_LOGGING_CONFIG = {
    'level': 'DEBUG',
    'format': '%(asctime)s - [MA-DEBUG] %(funcName)s:%(lineno)d - %(message)s',
    'file': 'logs/ma_support_debug.log',
    'max_size': 10 * 1024 * 1024,    # 10MB
    'backup_count': 2,
    'encoding': 'utf-8',
}

# 增量处理日志配置
INCREMENTAL_LOGGING_CONFIG = {
    'level': 'INFO',
    'format': '%(asctime)s - [INCREMENTAL] %(levelname)s - %(message)s',
    'file': 'logs/incremental_processing.log',
    'max_size': 15 * 1024 * 1024,    # 15MB
    'backup_count': 3,
    'encoding': 'utf-8',
}

# 日志级别映射
LOG_LEVEL_MAPPING = {
    'DEBUG': 10,
    'INFO': 20,
    'WARNING': 30,
    'ERROR': 40,
    'CRITICAL': 50
}

# 彩色日志配置（控制台输出）
COLORED_LOGGING_CONFIG = {
    'colors': {
        'DEBUG': '\033[36m',      # 青色
        'INFO': '\033[32m',       # 绿色
        'WARNING': '\033[33m',    # 黄色
        'ERROR': '\033[31m',      # 红色
        'CRITICAL': '\033[35m',   # 紫色
        'RESET': '\033[0m'        # 重置
    },
    'format': '%(asctime)s - %(color)s[%(levelname)s]%(reset)s %(name)s - %(message)s'
}