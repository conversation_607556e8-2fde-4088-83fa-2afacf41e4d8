#!/usr/bin/env python3
"""
增量处理配置管理工具
允许用户动态调整增量处理参数
"""

import sys
import os
import argparse
import json
from datetime import datetime, timedelta

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config.incremental_config import INCREMENTAL_CONFIG
from models.database import DatabaseManager

def show_current_config():
    """显示当前配置"""
    print("📋 当前增量处理配置")
    print("=" * 50)
    
    print(f"全局设置:")
    print(f"  启用增量处理: {INCREMENTAL_CONFIG['enable_incremental']}")
    print(f"  强制全量重建: {INCREMENTAL_CONFIG['force_full_rebuild']}")
    print(f"  默认时间窗口: {INCREMENTAL_CONFIG['time_window']['default_months']} 个月")
    
    print(f"\n数据策略:")
    for data_type, strategy in INCREMENTAL_CONFIG['data_strategies'].items():
        enabled = strategy.get('enabled', False)
        if enabled:
            months = strategy.get('months', 'N/A')
            overlap = strategy.get('overlap_weeks', 'N/A')
            print(f"  {data_type}: {months}个月 + {overlap}周重叠")
        else:
            print(f"  {data_type}: 禁用")
    
    print(f"\n首次运行检测:")
    detection = INCREMENTAL_CONFIG['first_run_detection']
    print(f"  检测方法: {detection['method']}")
    print(f"  数据量阈值: {detection['min_data_threshold']:,} 条")
    
    print(f"\n性能优化:")
    perf = INCREMENTAL_CONFIG['performance']
    print(f"  批量删除大小: {perf['batch_delete_size']:,}")
    print(f"  并行清理: {perf['parallel_cleanup']}")

def analyze_data_distribution():
    """分析数据分布，为配置优化提供建议"""
    print("\n📊 数据分布分析")
    print("=" * 50)
    
    db_manager = DatabaseManager()
    
    tables = {
        'stock_weekly_data': '周线数据',
        'stock_avg_trade': '平均交易量',
        'stock_moving_average': '移动平均线',
        'stock_volatility': '波动率'
    }
    
    recommendations = []
    
    for table_name, description in tables.items():
        try:
            # 获取总数据量
            result = db_manager.execute_query(f"SELECT COUNT(*) as count FROM {table_name}")
            total_count = result[0]['count'] if result else 0
            
            if total_count == 0:
                print(f"{description}: 无数据")
                continue
            
            # 获取时间范围
            result = db_manager.execute_query(f"""
                SELECT 
                    MIN(trade_date) as min_date,
                    MAX(trade_date) as max_date
                FROM {table_name}
            """)
            
            if not result or not result[0]['min_date']:
                continue
                
            min_date = result[0]['min_date']
            max_date = result[0]['max_date']
            total_days = (max_date - min_date).days
            
            print(f"\n{description} ({table_name}):")
            print(f"  总数据量: {total_count:,} 条")
            print(f"  时间跨度: {total_days} 天 ({min_date} ~ {max_date})")
            
            # 分析不同时间窗口的数据量
            time_windows = [1, 3, 6, 12]  # 月数
            
            for months in time_windows:
                cutoff_date = datetime.now().date() - timedelta(days=months * 30)
                
                result = db_manager.execute_query(f"""
                    SELECT COUNT(*) as count 
                    FROM {table_name} 
                    WHERE trade_date >= %s
                """, (cutoff_date,))
                
                recent_count = result[0]['count'] if result else 0
                percentage = (recent_count / total_count * 100) if total_count > 0 else 0
                
                print(f"  最近{months}个月: {recent_count:,} 条 ({percentage:.1f}%)")
                
                # 生成建议
                if months == 3 and percentage < 20:
                    recommendations.append(f"{description}: 建议使用3个月增量窗口 (仅{percentage:.1f}%数据)")
                elif months == 6 and 20 <= percentage < 40:
                    recommendations.append(f"{description}: 建议使用6个月增量窗口 (约{percentage:.1f}%数据)")
                elif months == 12 and percentage >= 40:
                    recommendations.append(f"{description}: 数据较新，建议使用12个月窗口或全量处理")
                    
        except Exception as e:
            print(f"{description}: 分析失败 - {e}")
    
    # 输出建议
    if recommendations:
        print(f"\n💡 配置优化建议:")
        for i, rec in enumerate(recommendations, 1):
            print(f"  {i}. {rec}")

def update_time_window(data_type: str, months: int):
    """更新指定数据类型的时间窗口"""
    print(f"\n🔧 更新 {data_type} 的时间窗口为 {months} 个月")
    
    # 注意：这里只是演示，实际应用中需要修改配置文件
    print("⚠️  注意：此功能需要修改配置文件才能永久生效")
    print(f"请在 config/incremental_config.py 中修改:")
    print(f"  INCREMENTAL_CONFIG['data_strategies']['{data_type}']['months'] = {months}")

def toggle_incremental(enable: bool):
    """切换增量处理开关"""
    status = "启用" if enable else "禁用"
    print(f"\n🔧 {status}增量处理")
    
    print("⚠️  注意：此功能需要修改配置文件才能永久生效")
    print(f"请在 config/incremental_config.py 中修改:")
    print(f"  INCREMENTAL_CONFIG['enable_incremental'] = {enable}")

def force_full_rebuild():
    """强制下次全量重建"""
    print(f"\n🔧 设置下次运行为全量重建")
    
    print("⚠️  注意：此功能需要修改配置文件才能永久生效")
    print(f"请在 config/incremental_config.py 中修改:")
    print(f"  INCREMENTAL_CONFIG['force_full_rebuild'] = True")
    print(f"运行完成后记得改回 False")

def estimate_performance_gain():
    """估算性能提升"""
    print(f"\n⚡ 性能提升估算")
    print("=" * 50)
    
    db_manager = DatabaseManager()
    
    # 基于当前数据分布估算
    tables = ['stock_weekly_data', 'stock_avg_trade', 'stock_moving_average', 'stock_volatility']
    total_savings = 0
    
    for table_name in tables:
        try:
            # 总数据量
            result = db_manager.execute_query(f"SELECT COUNT(*) as count FROM {table_name}")
            total_count = result[0]['count'] if result else 0
            
            if total_count == 0:
                continue
            
            # 3个月数据量
            three_months_ago = datetime.now().date() - timedelta(days=90)
            result = db_manager.execute_query(f"""
                SELECT COUNT(*) as count 
                FROM {table_name} 
                WHERE trade_date >= %s
            """, (three_months_ago,))
            
            recent_count = result[0]['count'] if result else 0
            percentage = (recent_count / total_count * 100) if total_count > 0 else 0
            savings = 100 - percentage
            
            print(f"{table_name}:")
            print(f"  增量处理可节省: {savings:.1f}% 的数据处理时间")
            
            total_savings += savings
            
        except Exception as e:
            print(f"{table_name}: 估算失败 - {e}")
    
    if len(tables) > 0:
        avg_savings = total_savings / len(tables)
        print(f"\n📈 平均预期性能提升: {avg_savings:.1f}%")
        
        # 估算实际时间节省
        print(f"\n⏱️  时间节省估算 (基于历史运行时间):")
        print(f"  如果全量处理需要 60 分钟")
        print(f"  增量处理预计需要: {60 * (100 - avg_savings) / 100:.1f} 分钟")
        print(f"  节省时间: {60 * avg_savings / 100:.1f} 分钟")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='增量处理配置管理工具')
    parser.add_argument('--show', action='store_true', help='显示当前配置')
    parser.add_argument('--analyze', action='store_true', help='分析数据分布')
    parser.add_argument('--estimate', action='store_true', help='估算性能提升')
    parser.add_argument('--update-window', nargs=2, metavar=('DATA_TYPE', 'MONTHS'),
                       help='更新时间窗口 (例如: weekly_data 6)')
    parser.add_argument('--enable', action='store_true', help='启用增量处理')
    parser.add_argument('--disable', action='store_true', help='禁用增量处理')
    parser.add_argument('--force-rebuild', action='store_true', help='强制下次全量重建')
    
    args = parser.parse_args()
    
    if args.show:
        show_current_config()
    elif args.analyze:
        show_current_config()
        analyze_data_distribution()
    elif args.estimate:
        estimate_performance_gain()
    elif args.update_window:
        data_type, months = args.update_window
        update_time_window(data_type, int(months))
    elif args.enable:
        toggle_incremental(True)
    elif args.disable:
        toggle_incremental(False)
    elif args.force_rebuild:
        force_full_rebuild()
    else:
        # 默认显示配置和分析
        show_current_config()
        analyze_data_distribution()
        estimate_performance_gain()

if __name__ == "__main__":
    main()
