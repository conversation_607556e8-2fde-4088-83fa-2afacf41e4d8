#!/usr/bin/env python3
"""
调试分析器
"""

import sys
import os
import logging

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置详细日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%H:%M:%S'
)

logger = logging.getLogger(__name__)

try:
    from models.database import DatabaseManager, StockDataModel
    from analysis.pattern_analyzer import PatternAnalyzer
    from config.analysis_config import U_PATTERN_CONFIG, V_PATTERN_CONFIG, VOLUME_CONFIG
    
    logger.info("1. 初始化...")
    db = DatabaseManager()
    stock_model = StockDataModel(db)
    
    logger.info("2. 获取数据...")
    stock_code = '000004'
    stock_name = '*ST国华'
    weekly_data = stock_model.get_weekly_data(stock_code)
    avg_trade_data = stock_model.get_avg_trade_data(stock_code)
    
    logger.info(f"   周线数据: {len(weekly_data)} 条")
    logger.info(f"   平均交易量数据: {len(avg_trade_data)} 条")
    
    logger.info("3. 创建分析器...")
    analyzer = PatternAnalyzer(U_PATTERN_CONFIG, V_PATTERN_CONFIG, VOLUME_CONFIG)
    
    logger.info("4. 开始分析...")
    
    # 手动调用analyze_uv_pattern_combined方法
    logger.info("调用analyze_uv_pattern_combined...")
    result = analyzer.analyze_uv_pattern_combined(stock_code, stock_name, weekly_data, avg_trade_data)
    
    if result:
        logger.info(f"✅ 发现形态: {result}")
    else:
        logger.info("❌ 未发现形态")
    
    logger.info("5. 完成")
    
except Exception as e:
    logger.error(f"❌ 错误: {e}")
    import traceback
    traceback.print_exc()
