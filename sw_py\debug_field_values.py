#!/usr/bin/env python3
"""
调试字段值相同问题
详细排查MA支撑点、换手率、突破判断等字段为什么都是相同值
"""

import sys
import os
from datetime import datetime, date

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models.database import DatabaseManager, StockDataModel
from analysis.pattern_analyzer import PatternAnalyzer
from config.analysis_config import U_PATTERN_CONFIG, V_PATTERN_CONFIG, VOLUME_CONFIG

def debug_ma_support_calculation():
    """调试MA支撑点计算"""
    
    print('🔍 调试MA支撑点计算')
    print('=' * 60)
    
    try:
        # 初始化组件
        db = DatabaseManager()
        stock_model = StockDataModel(db)
        analyzer = PatternAnalyzer(U_PATTERN_CONFIG, V_PATTERN_CONFIG, VOLUME_CONFIG)
        analyzer.stock_model = stock_model  # 手动设置stock_model
        
        # 测试数据
        test_stock_code = '000001'
        u_right_date = '2023-02-13'
        success_date = '2023-04-10'
        
        print(f'📊 测试股票: {test_stock_code}')
        print(f'📅 U右侧日期: {u_right_date}')
        print(f'📅 成功日期: {success_date}')
        
        # 1. 检查是否有移动平均线数据
        print(f'\n🔍 检查移动平均线数据:')
        try:
            ma_data = stock_model.get_moving_average_data(
                test_stock_code, u_right_date, success_date
            )
            print(f'  移动平均线数据条数: {len(ma_data) if ma_data else 0}')
            if ma_data:
                print(f'  数据示例: {ma_data[:3]}')
            else:
                print('  ❌ 未找到移动平均线数据')
        except Exception as e:
            print(f'  ❌ 获取移动平均线数据失败: {e}')
        
        # 2. 测试MA支撑点计算
        print(f'\n🧮 测试MA支撑点计算:')
        ma_num = analyzer._find_best_moving_average_support(
            test_stock_code, u_right_date, success_date
        )
        print(f'  计算结果: {ma_num}')

        # 3. 测试不同股票的MA支撑点
        print(f'\n🧮 测试不同股票的MA支撑点:')
        test_stocks = ['000001', '000002', '000003', '300001', '688001']
        for stock in test_stocks:
            ma_num = analyzer._find_best_moving_average_support(
                stock, u_right_date, success_date
            )
            print(f'  {stock}: {ma_num}')
        
        # 3. 检查数据库表结构
        print(f'\n📋 检查相关数据表:')
        tables_to_check = [
            'stock_moving_average',
            'stock_daily_data', 
            'stock_share_info',
            'stock_basic_info'
        ]
        
        for table in tables_to_check:
            try:
                sql = f"SELECT COUNT(*) as count FROM {table} LIMIT 1"
                result = db.execute_query(sql)
                if result:
                    print(f'  ✅ {table}: 存在')
                else:
                    print(f'  ❌ {table}: 不存在或为空')
            except Exception as e:
                print(f'  ❌ {table}: 不存在 ({e})')
                
    except Exception as e:
        print(f'❌ MA支撑点调试失败: {e}')
        import traceback
        traceback.print_exc()

def debug_turnover_calculation():
    """调试换手率计算"""
    
    print('\n🔍 调试换手率计算')
    print('=' * 60)
    
    try:
        # 初始化组件
        db = DatabaseManager()
        stock_model = StockDataModel(db)
        analyzer = PatternAnalyzer(U_PATTERN_CONFIG, V_PATTERN_CONFIG, VOLUME_CONFIG)
        analyzer.stock_model = stock_model  # 手动设置stock_model
        
        # 测试数据
        test_stock_code = '000001'
        start_date = '2023-01-09'
        end_date = '2023-02-13'
        
        print(f'📊 测试股票: {test_stock_code}')
        print(f'📅 计算期间: {start_date} 到 {end_date}')
        
        # 1. 检查日线数据
        print(f'\n🔍 检查日线数据:')
        try:
            daily_data = stock_model.get_daily_data_for_turnover(
                test_stock_code, start_date, end_date
            )
            print(f'  日线数据条数: {len(daily_data) if daily_data else 0}')
            if daily_data:
                print(f'  数据示例: {daily_data[:3]}')
            else:
                print('  ❌ 未找到日线数据')
        except Exception as e:
            print(f'  ❌ 获取日线数据失败: {e}')
        
        # 2. 检查总股本数据
        print(f'\n🔍 检查总股本数据:')
        try:
            total_shares = analyzer._get_total_shares(test_stock_code, end_date)
            print(f'  总股本: {total_shares:,.0f} 股')
        except Exception as e:
            print(f'  ❌ 获取总股本失败: {e}')
        
        # 3. 测试换手率计算
        print(f'\n🧮 测试换手率计算:')
        turnover_data = analyzer._calculate_turnover_rates(
            test_stock_code, start_date, end_date
        )
        print(f'  计算结果: {turnover_data}')

        # 4. 测试不同股票的换手率
        print(f'\n🧮 测试不同股票的换手率:')
        test_stocks = ['000001', '000002', '000003', '300001', '688001']
        for stock in test_stocks:
            turnover_data = analyzer._calculate_turnover_rates(
                stock, start_date, end_date
            )
            print(f'  {stock}: {turnover_data}')
        
        # 4. 检查周线数据作为对比
        print(f'\n📊 检查周线数据（作为对比）:')
        try:
            sql = """
            SELECT trade_date, volume
            FROM stock_weekly_data 
            WHERE stock_code = %s 
            AND trade_date BETWEEN %s AND %s
            ORDER BY trade_date
            LIMIT 5
            """
            weekly_data = db.execute_query(sql, (test_stock_code, start_date, end_date))
            print(f'  周线数据条数: {len(weekly_data) if weekly_data else 0}')
            if weekly_data:
                print(f'  数据示例: {weekly_data[:3]}')
        except Exception as e:
            print(f'  ❌ 获取周线数据失败: {e}')
                
    except Exception as e:
        print(f'❌ 换手率调试失败: {e}')
        import traceback
        traceback.print_exc()

def debug_breakthrough_calculation():
    """调试突破判断计算"""
    
    print('\n🔍 调试突破判断计算')
    print('=' * 60)
    
    try:
        # 初始化组件
        db = DatabaseManager()
        stock_model = StockDataModel(db)
        analyzer = PatternAnalyzer(U_PATTERN_CONFIG, V_PATTERN_CONFIG, VOLUME_CONFIG)
        analyzer.stock_model = stock_model  # 手动设置stock_model
        
        # 测试数据
        test_result = {
            'stock_code': '000001',
            'v_right_date': '2023-03-06',
            'v_right_price': 13.80
        }
        
        print(f'📊 测试股票: {test_result["stock_code"]}')
        print(f'📅 V右侧日期: {test_result["v_right_date"]}')
        print(f'💰 V右侧价格: {test_result["v_right_price"]}')
        
        # 1. 测试突破判断
        print(f'\n🧮 测试突破判断:')
        price_data = analyzer.check_price_logic(test_result)
        print(f'  计算结果: {price_data}')

        # 2. 测试不同股票的突破判断
        print(f'\n🧮 测试不同股票的突破判断:')
        test_stocks = ['000001', '000002', '000003', '300001', '688001']
        for stock in test_stocks:
            test_result_stock = {
                'stock_code': stock,
                'v_right_date': '2023-03-06',
                'v_right_price': 13.80
            }
            price_data = analyzer.check_price_logic(test_result_stock)
            print(f'  {stock}: {price_data}')
                
    except Exception as e:
        print(f'❌ 突破判断调试失败: {e}')
        import traceback
        traceback.print_exc()

def debug_actual_analysis_results():
    """调试实际分析结果"""
    
    print('\n🔍 调试实际分析结果')
    print('=' * 60)
    
    try:
        # 初始化组件
        db = DatabaseManager()
        stock_model = StockDataModel(db)
        
        # 获取最近的分析结果
        print(f'📊 获取最近的分析结果:')
        sql = """
        SELECT stock_code, stock_name, 
               all_turnover_rate, avg_turnover_rate, 
               ma_num, is_breakthrough, breakthrough_price, breakthrough_date
        FROM stock_analysis_result 
        ORDER BY id DESC 
        LIMIT 10
        """
        
        results = db.execute_query(sql)
        if results:
            print(f'  结果数量: {len(results)}')
            print(f'  字段值统计:')
            
            # 统计各字段的唯一值
            fields = ['all_turnover_rate', 'avg_turnover_rate', 'ma_num', 
                     'is_breakthrough', 'breakthrough_price']
            
            for field in fields:
                values = [r.get(field) for r in results]
                unique_values = list(set(values))
                print(f'    {field}: {len(unique_values)}个唯一值 -> {unique_values}')
            
            print(f'\n  前5条记录:')
            for i, result in enumerate(results[:5], 1):
                print(f'    {i}. {result["stock_code"]} {result["stock_name"]}:')
                print(f'       换手率: {result["all_turnover_rate"]}/{result["avg_turnover_rate"]}')
                print(f'       MA支撑: {result["ma_num"]}')
                print(f'       突破: {result["is_breakthrough"]}/{result["breakthrough_price"]}')
        else:
            print('  ❌ 未找到分析结果')
                
    except Exception as e:
        print(f'❌ 实际结果调试失败: {e}')
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    try:
        print('🔧 开始调试字段值相同问题')
        print('=' * 80)
        
        # 1. 调试MA支撑点计算
        debug_ma_support_calculation()
        
        # 2. 调试换手率计算
        debug_turnover_calculation()
        
        # 3. 调试突破判断计算
        debug_breakthrough_calculation()
        
        # 4. 调试实际分析结果
        debug_actual_analysis_results()
        
        print('\n' + '=' * 80)
        print('📋 问题总结:')
        print('=' * 80)
        print('❌ MA支撑点: 总是返回默认值2（缺少移动平均线数据）')
        print('❌ 换手率: 总是返回0（缺少日线数据或总股本数据）')
        print('❌ 突破判断: 总是返回固定值（方法实现为占位符）')
        print('\n🔧 修复建议:')
        print('1. 实现真实的MA支撑点计算逻辑')
        print('2. 使用周线数据估算换手率（如果没有日线数据）')
        print('3. 实现真实的突破判断逻辑')
        print('4. 为每个股票计算不同的值，而不是返回固定值')
        
    except Exception as e:
        print(f'❌ 调试失败: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
