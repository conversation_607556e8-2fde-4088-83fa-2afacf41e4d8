#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试MA数据质量问题
分析为什么MA支撑点计算经常回退到哈希值
"""

import sys
import os
from datetime import datetime, date

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from models.database import DatabaseManager, StockDataModel
    from utils.enhanced_logger import get_enhanced_logger
    from analysis.pattern_analyzer import PatternAnalyzer
    from config.analysis_config import U_PATTERN_CONFIG, V_PATTERN_CONFIG, VOLUME_CONFIG
except ImportError as e:
    print(f"导入失败: {e}")
    sys.exit(1)

def analyze_ma_data_quality():
    """分析MA数据质量"""
    print("=== MA数据质量分析 ===")
    
    # 设置日志
    logger = get_enhanced_logger('ma_debug')
    
    # 创建数据库管理器
    db_manager = DatabaseManager()
    
    # 1. 检查移动平均线表的总体情况
    print("\n1. 移动平均线表总体情况")
    try:
        with db_manager.get_connection() as conn:
            with conn.cursor() as cursor:
                # 检查表是否存在
                cursor.execute("SHOW TABLES LIKE 'stock_moving_average'")
                table_exists = cursor.fetchone()
                
                if not table_exists:
                    print("❌ stock_moving_average 表不存在")
                    return
                
                print("✅ stock_moving_average 表存在")
                
                # 检查总记录数
                cursor.execute("SELECT COUNT(*) as total FROM stock_moving_average")
                total_records = cursor.fetchone()[0]
                print(f"📊 总记录数: {total_records:,}")
                
                if total_records == 0:
                    print("❌ 移动平均线表为空，这是MA支撑点计算失败的主要原因")
                    return
                
                # 检查股票数量
                cursor.execute("SELECT COUNT(DISTINCT stock_code) as stock_count FROM stock_moving_average")
                stock_count = cursor.fetchone()[0]
                print(f"📈 包含股票数量: {stock_count:,}")
                
                # 检查日期范围
                cursor.execute("SELECT MIN(trade_date) as min_date, MAX(trade_date) as max_date FROM stock_moving_average")
                result = cursor.fetchone()
                print(f"📅 日期范围: {result[0]} ~ {result[1]}")
                
                # 检查avg_months分布
                cursor.execute("""
                    SELECT avg_months, COUNT(*) as count 
                    FROM stock_moving_average 
                    GROUP BY avg_months 
                    ORDER BY avg_months
                """)
                avg_months_dist = cursor.fetchall()
                print("📊 avg_months分布:")
                for avg_months, count in avg_months_dist:
                    print(f"  MA{avg_months}: {count:,} 条记录")
                
    except Exception as e:
        print(f"❌ 检查移动平均线表失败: {e}")
        return
    
    # 2. 测试具体股票的MA数据
    print("\n2. 测试具体股票的MA数据")
    test_stocks = ['000001', '000002', '300001', '600001', '688001']
    
    for stock_code in test_stocks:
        print(f"\n--- 股票 {stock_code} ---")
        try:
            # 查询该股票的MA数据
            ma_data = db_manager.get_moving_average_data(stock_code)
            print(f"📊 总MA记录数: {len(ma_data)}")
            
            if ma_data:
                # 分析日期范围
                dates = [data['trade_date'] for data in ma_data]
                min_date = min(dates)
                max_date = max(dates)
                print(f"📅 日期范围: {min_date} ~ {max_date}")
                
                # 分析avg_months分布
                avg_months_set = set(data['avg_months'] for data in ma_data)
                print(f"📈 可用MA周期: {sorted(avg_months_set)}")
                
                # 测试特定日期范围的查询
                test_start = date(2023, 7, 1)
                test_end = date(2023, 8, 15)
                filtered_data = db_manager.get_moving_average_data(stock_code, test_start, test_end)
                print(f"🔍 测试期间({test_start}~{test_end})数据: {len(filtered_data)} 条")
                
                if filtered_data:
                    # 分析测试期间的MA周期
                    test_avg_months = set(data['avg_months'] for data in filtered_data)
                    print(f"🎯 测试期间可用MA周期: {sorted(test_avg_months)}")
                    
                    # 显示几条样本数据
                    print("📋 样本数据:")
                    for i, data in enumerate(filtered_data[:3]):
                        print(f"  {i+1}. 日期:{data['trade_date']}, MA{data['avg_months']}, 值:{data['avg_value']:.2f}")
                else:
                    print("⚠️ 测试期间无数据")
            else:
                print("❌ 该股票无MA数据")
                
        except Exception as e:
            print(f"❌ 查询股票 {stock_code} MA数据失败: {e}")
    
    # 3. 检查最近的MA数据
    print("\n3. 检查最近的MA数据")
    try:
        with db_manager.get_connection() as conn:
            with conn.cursor() as cursor:
                # 查询最近一个月的数据
                cursor.execute("""
                    SELECT stock_code, COUNT(*) as count
                    FROM stock_moving_average 
                    WHERE trade_date >= DATE_SUB(CURDATE(), INTERVAL 1 MONTH)
                    GROUP BY stock_code
                    ORDER BY count DESC
                    LIMIT 10
                """)
                recent_data = cursor.fetchall()
                
                if recent_data:
                    print("📊 最近一个月MA数据最多的10只股票:")
                    for stock_code, count in recent_data:
                        print(f"  {stock_code}: {count} 条记录")
                else:
                    print("⚠️ 最近一个月无MA数据")
                    
    except Exception as e:
        print(f"❌ 检查最近MA数据失败: {e}")
    
    # 4. 测试改进后的MA支撑点计算
    print("\n4. 测试改进后的MA支撑点计算")
    try:
        # 创建模式分析器
        db_manager = DatabaseManager()
        stock_model = StockDataModel(db_manager)
        analyzer = PatternAnalyzer(U_PATTERN_CONFIG, V_PATTERN_CONFIG, VOLUME_CONFIG)
        analyzer.stock_model = stock_model  # 设置stock_model

        test_stocks = ['000001', '000002', '300001', '600001', '688001']

        for stock_code in test_stocks:
            print(f"\n--- 测试股票 {stock_code} MA支撑点计算 ---")
            try:
                # 测试MA支撑点计算
                test_start = date(2023, 7, 1)
                test_end = date(2023, 8, 15)

                ma_num = analyzer._find_best_moving_average_support(
                    stock_code, test_start, test_end
                )
                print(f"✅ {stock_code} MA支撑点: MA{ma_num}")

                # 获取详细的MA数据进行验证
                ma_data = stock_model.get_moving_average_data(stock_code, test_start, test_end)
                if ma_data:
                    # 分析实际使用的MA数据
                    ma_by_period = {}
                    for data in ma_data:
                        period = data.get('avg_months')
                        if period and period >= 2:
                            if period not in ma_by_period:
                                ma_by_period[period] = []
                            ma_by_period[period].append(data)

                    print(f"📊 可用MA周期及数据量: {dict((k, len(v)) for k, v in ma_by_period.items())}")

                    if ma_num in ma_by_period:
                        print(f"✅ 选择的MA{ma_num}有 {len(ma_by_period[ma_num])} 条实际数据")
                    else:
                        print(f"⚠️ 选择的MA{ma_num}使用了回退值")
                else:
                    print(f"❌ {stock_code} 测试期间无MA数据")

            except Exception as e:
                print(f"❌ 测试股票 {stock_code} 失败: {e}")

    except Exception as e:
        print(f"❌ MA支撑点计算测试失败: {e}")

    print("\n=== 分析完成 ===")

if __name__ == "__main__":
    analyze_ma_data_quality()
