#!/usr/bin/env python3
"""
调试MA逻辑问题
检查为什么MA支撑点总是返回2
"""

import sys
import os
from datetime import datetime, date, timedelta

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from analysis.pattern_analyzer import <PERSON>ternAnalyzer
from config.analysis_config import U_PATTERN_CONFIG, V_PATTERN_CONFIG, VOLUME_CONFIG

def debug_ma_logic():
    """调试MA逻辑"""
    
    print('🔍 调试MA逻辑问题')
    print('=' * 60)
    
    analyzer = PatternAnalyzer(U_PATTERN_CONFIG, V_PATTERN_CONFIG, VOLUME_CONFIG)
    
    # 创建测试数据 - 模拟实际分析结果
    test_result = {
        'stock_code': '000001',
        'stock_name': '平安银行',
        'u_left_date': date(2023, 5, 1),
        'u_right_date': date(2023, 7, 1),
        'v_right_date': date(2023, 8, 1),
        'v_right_price': 12.5,
        'success_date': None  # 这可能是问题所在
    }
    
    print('测试数据:')
    for key, value in test_result.items():
        print(f'  {key}: {value}')
    
    print('\n🔍 测试check_ma_logic方法:')
    
    # 创建模拟周线数据
    weekly_data = []

    base_date = date(2023, 1, 1)
    for i in range(50):
        weekly_data.append({
            'trade_date': base_date + timedelta(days=i*7),
            'high_price': 10 + i * 0.1,
            'low_price': 9 + i * 0.1,
            'close_price': 9.5 + i * 0.1,
            'volume': 1000000 + i * 10000
        })
    
    # 测试1: 没有success_date的情况
    print('\n测试1: 没有success_date')
    ma_data1 = analyzer.check_ma_logic(test_result, weekly_data)
    print(f'结果: {ma_data1}')
    
    # 测试2: 有success_date的情况
    print('\n测试2: 有success_date')
    test_result_with_success = test_result.copy()
    test_result_with_success['success_date'] = date(2023, 8, 15)
    
    ma_data2 = analyzer.check_ma_logic(test_result_with_success, weekly_data)
    print(f'结果: {ma_data2}')
    
    # 测试3: 检查stock_model是否存在
    print('\n测试3: 检查stock_model属性')
    has_stock_model = hasattr(analyzer, 'stock_model')
    print(f'analyzer.stock_model存在: {has_stock_model}')
    
    if has_stock_model:
        print(f'stock_model类型: {type(analyzer.stock_model)}')
    
    # 测试4: 直接调用_find_best_moving_average_support
    print('\n测试4: 直接调用_find_best_moving_average_support')
    try:
        ma_num = analyzer._find_best_moving_average_support(
            '000001', 
            date(2023, 7, 1), 
            date(2023, 8, 15)
        )
        print(f'MA支撑点: {ma_num}')
    except Exception as e:
        print(f'调用失败: {e}')
    
    # 测试5: 测试不同股票代码的哈希值
    print('\n测试5: 测试不同股票代码的哈希值')
    test_stocks = ['000001', '000002', '300001', '600001', '688001']
    
    for stock_code in test_stocks:
        try:
            ma_num = analyzer._find_best_moving_average_support(
                stock_code, 
                date(2023, 7, 1), 
                date(2023, 8, 15)
            )
            stock_hash = hash(stock_code) % 10
            print(f'  {stock_code}: hash={stock_hash}, MA支撑点={ma_num}')
        except Exception as e:
            print(f'  {stock_code}: 调用失败 - {e}')

def debug_complete_analysis():
    """调试完整分析流程"""
    
    print('\n🔍 调试完整分析流程')
    print('=' * 60)
    
    analyzer = PatternAnalyzer(U_PATTERN_CONFIG, V_PATTERN_CONFIG, VOLUME_CONFIG)
    
    # 测试数据
    test_result = {
        'stock_code': '000001',
        'stock_name': '平安银行',
        'u_left_date': date(2023, 5, 1),
        'u_right_date': date(2023, 7, 1),
        'v_right_date': date(2023, 8, 1),
        'v_right_price': 12.5,
        'success_date': date(2023, 8, 15)  # 提供success_date
    }
    
    # 创建模拟周线数据
    weekly_data = []
    base_date = date(2023, 1, 1)
    for i in range(50):
        weekly_data.append({
            'trade_date': base_date + timedelta(days=i*7),
            'high_price': 10 + i * 0.1,
            'low_price': 9 + i * 0.1,
            'close_price': 9.5 + i * 0.1,
            'volume': 1000000 + i * 10000
        })
    
    print('执行complete_analysis_result:')
    complete_result = analyzer.complete_analysis_result(test_result, weekly_data)
    
    print('完整结果:')
    for key, value in complete_result.items():
        print(f'  {key}: {value}')
    
    print(f'\n关键字段:')
    print(f'  MA支撑点: {complete_result.get("ma_num", "未设置")}')
    print(f'  累计换手率: {complete_result.get("all_turnover_rate", "未设置")}')
    print(f'  平均换手率: {complete_result.get("avg_turnover_rate", "未设置")}')
    print(f'  是否突破: {complete_result.get("is_breakthrough", "未设置")}')

def main():
    """主函数"""
    print('🧪 MA逻辑调试')
    print('=' * 80)
    
    debug_ma_logic()
    debug_complete_analysis()
    
    print('\n' + '=' * 80)
    print('📋 调试总结:')
    print('=' * 80)
    print('1. 检查success_date是否存在')
    print('2. 检查stock_model是否正确初始化')
    print('3. 验证_find_best_moving_average_support方法')
    print('4. 确认哈希算法是否工作')

if __name__ == "__main__":
    main()
