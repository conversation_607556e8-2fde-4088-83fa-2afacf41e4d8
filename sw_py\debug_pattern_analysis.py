#!/usr/bin/env python3
"""
调试形态分析 - 分析为什么没有找到符合条件的股票
"""

import sys
import os
import logging

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models.database import DatabaseManager, StockDataModel
from analysis.pattern_analyzer import PatternAnalyzer
from config.analysis_config import U_PATTERN_CONFIG, V_PATTERN_CONFIG, VOLUME_CONFIG, DATA_FILTER_CONFIG

# 设置详细日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%H:%M:%S'
)

logger = logging.getLogger(__name__)

def analyze_single_stock_detailed(stock_code: str, stock_name: str):
    """详细分析单只股票的形态筛选过程"""
    logger.info(f"=== 详细分析股票 {stock_code} ({stock_name}) ===")
    
    # 获取数据
    db = DatabaseManager()
    stock_model = StockDataModel(db)
    
    weekly_data = stock_model.get_weekly_data(stock_code)
    avg_trade_data = stock_model.get_avg_trade_data(stock_code)
    
    logger.info(f"周线数据: {len(weekly_data)} 条")
    logger.info(f"平均交易量数据: {len(avg_trade_data)} 条")
    
    if len(weekly_data) < DATA_FILTER_CONFIG.get('min_weekly_data', 20):
        logger.warning(f"周线数据不足: {len(weekly_data)} < {DATA_FILTER_CONFIG.get('min_weekly_data', 20)}")
        return None
    
    # 显示最近几周的数据
    logger.info("最近10周数据:")
    for i, data in enumerate(weekly_data[-10:]):
        logger.info(f"  {i+len(weekly_data)-10}: {data['trade_date']} 高:{data['high_price']} 低:{data['low_price']} 量:{data['jy_quantity']}")
    
    # 创建分析器并启用详细调试
    analyzer = PatternAnalyzer(U_PATTERN_CONFIG, V_PATTERN_CONFIG, VOLUME_CONFIG)
    analyzer.logger.setLevel(logging.DEBUG)
    
    # 执行分析
    result = analyzer.analyze_stock_patterns_strict(stock_code, stock_name, weekly_data, avg_trade_data)
    
    # 显示筛选统计
    logger.info("=== 筛选统计 ===")
    stats = analyzer.filter_stats
    for key, value in stats.items():
        logger.info(f"{key}: {value}")
    
    return result

def test_u_pattern_conditions():
    """测试U型形态的各个条件"""
    logger.info("=== 测试U型形态条件 ===")
    
    # 获取一些股票进行测试
    db = DatabaseManager()
    stock_model = StockDataModel(db)
    stock_list = stock_model.get_stock_list()[:10]  # 测试前10只
    
    analyzer = PatternAnalyzer(U_PATTERN_CONFIG, V_PATTERN_CONFIG, VOLUME_CONFIG)
    
    for stock in stock_list:
        stock_code = stock['gp_num']
        stock_name = stock['gp_name']
        
        logger.info(f"\n--- 测试股票 {stock_code} ({stock_name}) ---")
        
        weekly_data = stock_model.get_weekly_data(stock_code)
        avg_trade_data = stock_model.get_avg_trade_data(stock_code)
        
        if len(weekly_data) < 20:
            logger.info(f"跳过：数据不足 {len(weekly_data)} 条")
            continue
        
        # 测试U型左侧高点条件
        u_left_candidates = 0
        for i in range(U_PATTERN_CONFIG['pre_month'], len(weekly_data)):
            current_high = float(weekly_data[i]['high_price'])
            if analyzer.check_u_left_strict(weekly_data, i, current_high):
                u_left_candidates += 1
        
        logger.info(f"U型左侧高点候选: {u_left_candidates}")
        
        # 如果有候选，测试右侧高点
        if u_left_candidates > 0:
            for i in range(U_PATTERN_CONFIG['pre_month'], min(len(weekly_data), U_PATTERN_CONFIG['pre_month'] + 50)):
                current_high = float(weekly_data[i]['high_price'])
                if analyzer.check_u_left_strict(weekly_data, i, current_high):
                    u_right_result = analyzer.get_u_right_strict(weekly_data, avg_trade_data, i, current_high, stock_code)
                    if u_right_result:
                        logger.info(f"找到U型右侧高点: 左侧第{i}周 -> 右侧第{u_right_result[0]}周")
                        
                        # 测试底部条件
                        u_bottom_result = analyzer.check_u_bottom_strict(weekly_data, i, u_right_result[0], current_high)
                        if u_bottom_result:
                            logger.info(f"✓ 完整U型形态: 左{i} 右{u_right_result[0]} 底价{u_bottom_result[0]}")
                        else:
                            logger.info(f"✗ U型底部不满足条件")
                        break
        
        # 只测试前几只股票，避免输出过多
        if stock_list.index(stock) >= 3:
            break

def check_oracle_vs_python_conditions():
    """对比Oracle和Python的条件差异"""
    logger.info("=== Oracle vs Python 条件对比 ===")
    
    logger.info("U型形态参数:")
    logger.info(f"  前段周期: {U_PATTERN_CONFIG['pre_month']} (Oracle: U_PREMONTH = 10)")
    logger.info(f"  左侧涨幅限制: {U_PATTERN_CONFIG['left_diff_rate']} (Oracle: U_LDIFF_RATE = 0.03)")
    logger.info(f"  左右价差: {U_PATTERN_CONFIG['lr_diff_rate']} (Oracle: U_LR_DIFF_RATE = 0.03)")
    logger.info(f"  底部价差下限: {U_PATTERN_CONFIG['ll_low_diff']} (Oracle: U_LL_LDIFF = 0.12)")
    logger.info(f"  底部价差上限: {U_PATTERN_CONFIG['ll_high_diff']} (Oracle: U_LL_HDIFF = 0.5)")
    logger.info(f"  交易量放大: {U_PATTERN_CONFIG['volume_amplify_rate']} (Oracle: 1.05)")
    logger.info(f"  最小间隔: {U_PATTERN_CONFIG['low_period']} (Oracle: U_LPERIOD = 7)")
    
    logger.info("V型形态参数:")
    logger.info(f"  底部下探: {V_PATTERN_CONFIG['low_diff']} (Oracle: V_LLOW_DIFF = 0.15)")
    logger.info(f"  左右差异: {V_PATTERN_CONFIG['lr_diff']} (Oracle: V_LR_DIFF = 0.05)")
    logger.info(f"  最小间隔: {V_PATTERN_CONFIG['low_period']} (Oracle: V_LPERIOD = 3)")
    logger.info(f"  交易量收缩: {V_PATTERN_CONFIG['volume_shrink_threshold']} (Oracle: 0.5)")

def check_data_quality():
    """检查数据质量问题"""
    logger.info("=== 检查数据质量 ===")
    
    db = DatabaseManager()
    stock_model = StockDataModel(db)
    
    # 检查周线数据
    weekly_count = db.execute_query("SELECT COUNT(*) as count FROM stock_weekly_data")[0]['count']
    logger.info(f"周线数据总量: {weekly_count}")
    
    # 检查平均交易量数据
    avg_count = db.execute_query("SELECT COUNT(*) as count FROM stock_avg_trade")[0]['count']
    logger.info(f"平均交易量数据总量: {avg_count}")
    
    # 检查数据时间范围
    weekly_range = db.execute_query("SELECT MIN(trade_date) as min_date, MAX(trade_date) as max_date FROM stock_weekly_data")
    if weekly_range:
        logger.info(f"周线数据时间范围: {weekly_range[0]['min_date']} ~ {weekly_range[0]['max_date']}")
    
    avg_range = db.execute_query("SELECT MIN(trade_date) as min_date, MAX(trade_date) as max_date FROM stock_avg_trade")
    if avg_range:
        logger.info(f"平均交易量时间范围: {avg_range[0]['min_date']} ~ {avg_range[0]['max_date']}")
    
    # 检查数据匹配情况
    match_check = db.execute_query("""
        SELECT COUNT(DISTINCT w.stock_code) as weekly_stocks,
               COUNT(DISTINCT a.stock_code) as avg_stocks,
               COUNT(DISTINCT CASE WHEN a.stock_code IS NOT NULL THEN w.stock_code END) as matched_stocks
        FROM stock_weekly_data w
        LEFT JOIN stock_avg_trade a ON w.stock_code = a.stock_code
    """)
    if match_check:
        result = match_check[0]
        logger.info(f"周线数据股票数: {result['weekly_stocks']}")
        logger.info(f"平均交易量股票数: {result['avg_stocks']}")
        logger.info(f"匹配股票数: {result['matched_stocks']}")

def main():
    """主函数"""
    logger.info("🔍 开始调试形态分析")
    
    try:
        # 1. 检查数据质量
        check_data_quality()
        
        # 2. 检查配置参数
        check_oracle_vs_python_conditions()
        
        # 3. 测试U型形态条件
        test_u_pattern_conditions()
        
        # 4. 详细分析几只具体股票
        db = DatabaseManager()
        stock_model = StockDataModel(db)
        stock_list = stock_model.get_stock_list()[:3]  # 分析前3只
        
        for stock in stock_list:
            analyze_single_stock_detailed(stock['gp_num'], stock['gp_name'])
        
        logger.info("✅ 调试分析完成")
        
    except Exception as e:
        logger.error(f"❌ 调试分析失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
