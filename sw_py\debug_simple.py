#!/usr/bin/env python3
"""
简化调试脚本 - 分析具体股票的筛选过程
"""

import sys
import os
import logging

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models.database import DatabaseManager, StockDataModel
from analysis.pattern_analyzer import PatternAnalyzer
from config.analysis_config import U_PATTERN_CONFIG, V_PATTERN_CONFIG, VOLUME_CONFIG

# 设置详细日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%H:%M:%S'
)

logger = logging.getLogger(__name__)

def analyze_specific_stock(stock_code: str):
    """分析指定股票"""
    logger.info(f"=== 分析股票 {stock_code} ===")
    
    db = DatabaseManager()
    stock_model = StockDataModel(db)
    
    # 获取股票信息
    stock_info = db.execute_query("SELECT * FROM stock_info_a_code_name WHERE code = %s", (stock_code,))
    if not stock_info:
        logger.error(f"股票 {stock_code} 不存在")
        return
    
    stock_name = stock_info[0]['name']
    logger.info(f"股票名称: {stock_name}")
    
    # 获取数据
    weekly_data = stock_model.get_weekly_data(stock_code)
    avg_trade_data = stock_model.get_avg_trade_data(stock_code)
    
    logger.info(f"周线数据: {len(weekly_data)} 条")
    logger.info(f"平均交易量数据: {len(avg_trade_data)} 条")
    
    if len(weekly_data) < 20:
        logger.warning(f"数据不足，跳过分析")
        return
    
    # 显示最近数据
    logger.info("最近5周数据:")
    for i, data in enumerate(weekly_data[-5:]):
        logger.info(f"  {data['trade_date']}: 高{data['high_price']} 低{data['low_price']} 量{data['jy_quantity']}")
    
    # 显示平均交易量数据样本
    logger.info("平均交易量数据样本:")
    for i, data in enumerate(avg_trade_data[-5:]):
        logger.info(f"  {data['trade_date']}: {data['avg_days']}日均量{data['avg_qty']}")
    
    # 创建分析器
    analyzer = PatternAnalyzer(U_PATTERN_CONFIG, V_PATTERN_CONFIG, VOLUME_CONFIG)
    analyzer.logger.setLevel(logging.DEBUG)
    
    # 手动测试U型左侧高点
    logger.info("\n=== 测试U型左侧高点 ===")
    u_left_count = 0
    for i in range(10, min(len(weekly_data), 50)):  # 只测试前40个点
        current_high = float(weekly_data[i]['high_price'])
        if analyzer.check_u_left_strict(weekly_data, i, current_high):
            u_left_count += 1
            logger.info(f"✓ U型左侧高点候选 {i}: {weekly_data[i]['trade_date']} 价格{current_high}")
            
            # 测试右侧高点
            u_right_result = analyzer.get_u_right_strict(weekly_data, avg_trade_data, i, current_high, stock_code)
            if u_right_result:
                right_idx, right_date, right_price = u_right_result
                logger.info(f"  ✓ 找到右侧高点 {right_idx}: {right_date} 价格{right_price}")
                
                # 测试底部
                u_bottom_result = analyzer.check_u_bottom_strict(weekly_data, i, right_idx, current_high)
                if u_bottom_result:
                    bottom_price, bottom_date = u_bottom_result
                    logger.info(f"    ✓ U型底部有效: {bottom_date} 价格{bottom_price}")
                    logger.info(f"    ✓ 完整U型形态: 左{i}({weekly_data[i]['trade_date']}) 右{right_idx}({right_date}) 底({bottom_date})")
                else:
                    logger.info(f"    ✗ U型底部无效")
            else:
                logger.info(f"  ✗ 未找到右侧高点")
            
            # 只显示前几个候选
            if u_left_count >= 3:
                break
    
    logger.info(f"U型左侧高点候选总数: {u_left_count}")
    
    # 执行完整分析
    logger.info("\n=== 执行完整分析 ===")
    result = analyzer.analyze_stock_patterns_strict(stock_code, stock_name, weekly_data, avg_trade_data)
    
    if result:
        logger.info(f"✅ 找到形态: {result}")
    else:
        logger.info("❌ 未找到符合条件的形态")
    
    # 显示统计
    logger.info("\n=== 筛选统计 ===")
    stats = analyzer.filter_stats
    for key, value in stats.items():
        logger.info(f"{key}: {value}")

def test_oracle_conditions():
    """测试Oracle条件的严格性"""
    logger.info("=== Oracle条件测试 ===")
    
    # 测试价格条件
    test_left_price = 10.0
    test_cases = [
        ("U型左侧涨幅3%", test_left_price * 1.03, test_left_price * (1 + U_PATTERN_CONFIG['left_diff_rate'])),
        ("U型左右价差±3%", test_left_price * 0.97, test_left_price * (1 - U_PATTERN_CONFIG['lr_diff_rate'])),
        ("U型底部下限12%", test_left_price * 0.88, test_left_price * (1 - U_PATTERN_CONFIG['ll_low_diff'])),
        ("U型底部上限50%", test_left_price * 0.50, test_left_price * (1 - U_PATTERN_CONFIG['ll_high_diff'])),
    ]
    
    for desc, test_value, threshold in test_cases:
        logger.info(f"{desc}: 测试值{test_value:.4f} vs 阈值{threshold:.4f}")

def main():
    """主函数"""
    logger.info("🔍 开始简化调试")
    
    try:
        # 测试Oracle条件
        test_oracle_conditions()
        
        # 分析几只具体股票
        test_stocks = ['000001', '000002', '600000', '600036']
        
        for stock_code in test_stocks:
            try:
                analyze_specific_stock(stock_code)
                logger.info("\n" + "="*50 + "\n")
            except Exception as e:
                logger.error(f"分析股票 {stock_code} 失败: {e}")
                continue
        
        logger.info("✅ 简化调试完成")
        
    except Exception as e:
        logger.error(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
