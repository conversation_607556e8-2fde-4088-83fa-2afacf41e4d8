#!/usr/bin/env python3
"""
调试U型左侧高点逻辑
"""

import sys
import os
import logging

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models.database import DatabaseManager, StockDataModel
from analysis.pattern_analyzer import PatternAnalyzer
from config.analysis_config import U_PATTERN_CONFIG, V_PATTERN_CONFIG, VOLUME_CONFIG

logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def debug_u_left_detailed():
    """详细调试U型左侧高点"""
    
    print('🔍 详细调试U型左侧高点逻辑')
    
    db = DatabaseManager()
    stock_model = StockDataModel(db)
    analyzer = PatternAnalyzer(U_PATTERN_CONFIG, V_PATTERN_CONFIG, VOLUME_CONFIG)
    
    # 测试一只股票
    stock_code = '000001'
    
    print(f'\n--- 调试股票 {stock_code} ---')
    
    weekly_data = stock_model.get_weekly_data(stock_code)
    print(f'周线数据: {len(weekly_data)}条')
    
    # 显示配置
    print(f'\nU型配置:')
    print(f'  前段周期(pre_month): {U_PATTERN_CONFIG["pre_month"]}')
    print(f'  左侧涨幅限制(left_diff_rate): {U_PATTERN_CONFIG["left_diff_rate"]:.1%}')
    
    # 手动检查U型左侧高点逻辑
    print(f'\n🔍 手动检查U型左侧高点:')
    
    found_left_points = 0
    for i in range(U_PATTERN_CONFIG['pre_month'], min(len(weekly_data), 50)):
        current_high = float(weekly_data[i]['high_price'])
        current_date = weekly_data[i]['trade_date']
        
        print(f'\n检查第{i}周 ({current_date}), 最高价: {current_high:.2f}')
        
        # 检查前段周期内是否有更高的点
        start_idx = max(0, i - U_PATTERN_CONFIG['pre_month'])
        has_higher = False
        higher_points = []
        
        for j in range(start_idx, i):
            check_high = float(weekly_data[j]['high_price'])
            threshold = current_high * (1 + U_PATTERN_CONFIG['left_diff_rate'])
            
            if check_high > threshold:
                has_higher = True
                higher_points.append((j, weekly_data[j]['trade_date'], check_high, threshold))
        
        if has_higher:
            print(f'  ❌ 前{U_PATTERN_CONFIG["pre_month"]}周内有更高点:')
            for j, date, high, threshold in higher_points[:3]:  # 只显示前3个
                print(f'    第{j}周({date}): {high:.2f} > {threshold:.2f}')
        else:
            print(f'  ✅ 符合U型左侧高点条件')
            found_left_points += 1
            
            # 使用analyzer验证
            analyzer_result = analyzer.check_u_left_strict(weekly_data, i, current_high)
            print(f'  analyzer验证: {"✅" if analyzer_result else "❌"}')
            
            if found_left_points >= 3:  # 只检查前3个
                break
    
    print(f'\n📊 手动检查结果: 找到 {found_left_points} 个U型左侧高点')
    
    # 使用analyzer的完整流程
    print(f'\n🔍 使用analyzer完整流程:')
    result = analyzer.analyze_stock_patterns_strict(stock_code, '平安银行', weekly_data, [])
    print(f'analyzer结果: {result}')
    
    stats = analyzer.filter_stats
    print(f'筛选统计: {stats}')

def test_relaxed_u_left():
    """测试放宽U型左侧条件"""
    
    print(f'\n🧪 测试放宽U型左侧条件:')
    
    db = DatabaseManager()
    stock_model = StockDataModel(db)
    
    # 原始配置
    original_config = U_PATTERN_CONFIG.copy()
    
    # 测试不同的配置
    test_configs = [
        {'left_diff_rate': 0.05, 'name': '左侧涨幅5%'},
        {'left_diff_rate': 0.08, 'name': '左侧涨幅8%'},
        {'left_diff_rate': 0.10, 'name': '左侧涨幅10%'},
        {'pre_month': 5, 'name': '前段周期5周'},
        {'pre_month': 5, 'left_diff_rate': 0.08, 'name': '前段5周+涨幅8%'},
    ]
    
    test_stocks = ['000001', '000002', '600000']
    
    for config in test_configs:
        print(f'\n--- 测试配置: {config["name"]} ---')
        
        # 创建修改后的配置
        test_u_config = original_config.copy()
        test_u_config.update({k: v for k, v in config.items() if k != 'name'})
        
        analyzer = PatternAnalyzer(test_u_config, V_PATTERN_CONFIG, VOLUME_CONFIG)
        
        total_left_points = 0
        for stock_code in test_stocks:
            weekly_data = stock_model.get_weekly_data(stock_code)
            
            if len(weekly_data) < 20:
                continue
            
            # 统计U型左侧高点数量
            left_count = 0
            for i in range(test_u_config['pre_month'], min(len(weekly_data), 30)):
                current_high = float(weekly_data[i]['high_price'])
                if analyzer.check_u_left_strict(weekly_data, i, current_high):
                    left_count += 1
            
            total_left_points += left_count
            print(f'  {stock_code}: {left_count}个左侧高点')
        
        print(f'  总计: {total_left_points}个左侧高点')

def check_data_quality():
    """检查数据质量"""
    
    print(f'\n🔍 检查数据质量:')
    
    db = DatabaseManager()
    stock_model = StockDataModel(db)
    
    stock_code = '000001'
    weekly_data = stock_model.get_weekly_data(stock_code)
    
    print(f'股票 {stock_code} 数据检查:')
    print(f'  总周数: {len(weekly_data)}')
    
    # 检查前20周的数据
    print(f'\n前20周数据:')
    for i in range(min(20, len(weekly_data))):
        data = weekly_data[i]
        print(f'  第{i}周 {data["trade_date"]}: 开{data["open_price"]} 高{data["high_price"]} 低{data["low_price"]} 收{data["close_price"]}')
    
    # 检查价格变化
    print(f'\n价格变化分析:')
    price_changes = []
    for i in range(1, min(30, len(weekly_data))):
        prev_high = float(weekly_data[i-1]['high_price'])
        curr_high = float(weekly_data[i]['high_price'])
        change = (curr_high - prev_high) / prev_high
        price_changes.append(change)
        
        if abs(change) > 0.03:  # 变化超过3%
            print(f'  第{i-1}→{i}周: {prev_high:.2f} → {curr_high:.2f} ({change:.1%})')
    
    avg_change = sum(abs(c) for c in price_changes) / len(price_changes)
    max_change = max(abs(c) for c in price_changes)
    print(f'\n价格波动统计:')
    print(f'  平均变化: {avg_change:.1%}')
    print(f'  最大变化: {max_change:.1%}')
    print(f'  超过3%的变化: {sum(1 for c in price_changes if abs(c) > 0.03)}次')

def main():
    """主函数"""
    logger.info('🔍 开始调试U型左侧高点逻辑')
    
    try:
        debug_u_left_detailed()
        test_relaxed_u_left()
        check_data_quality()
        
        logger.info('✅ U型左侧调试完成')
        
    except Exception as e:
        logger.error(f'❌ 调试失败: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
