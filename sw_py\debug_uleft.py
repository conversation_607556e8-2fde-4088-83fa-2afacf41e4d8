#!/usr/bin/env python3
"""
调试U型左侧高点验证逻辑
"""

import sys
import os
import logging

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置详细日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

logger = logging.getLogger(__name__)

try:
    from models.database import DatabaseManager, StockDataModel
    from config.analysis_config import U_PATTERN_CONFIG
    
    # 测试数据库连接
    db_manager = DatabaseManager()
    stock_model = StockDataModel(db_manager)
    
    # 获取前5只股票进行测试
    stock_list = stock_model.get_stock_list()[:5]

    total_success = 0
    total_stocks = 0

    for test_stock in stock_list:
        stock_code = test_stock['gp_num']
        stock_name = test_stock['gp_name']

        logger.info(f"\n🔍 调试股票 {stock_code} ({stock_name}) 的U型左侧高点验证")
    
        # 获取周线数据
        weekly_data = stock_model.get_weekly_data(stock_code)
        logger.info(f"周线数据条数: {len(weekly_data)}")

        if len(weekly_data) < 20:
            logger.warning(f"⚠️ 股票 {stock_code} 数据不足，跳过")
            continue

        total_stocks += 1

        # 从第11周开始检查（Oracle: > U_PREMONTH）
        pre_month = U_PATTERN_CONFIG['pre_month']  # 10
        left_diff_rate = U_PATTERN_CONFIG['left_diff_rate']  # 0.03

        logger.info(f"配置参数: pre_month={pre_month}, left_diff_rate={left_diff_rate}")

        success_count = 0
        total_checked = 0
    
        for i in range(pre_month, min(pre_month + 5, len(weekly_data))):  # 只检查前5个候选点
            current_data = weekly_data[i]
            current_high = float(current_data['high_price'])
            current_date = current_data['trade_date']

            total_checked += 1

            # 检查前10周的数据
            start_idx = max(0, i - pre_month)
            threshold = current_high * (1 + left_diff_rate)

            is_valid = True
            violation_count = 0

            for j in range(start_idx, i):
                check_data = weekly_data[j]
                check_high = float(check_data['high_price'])

                if check_high > threshold:
                    violation_count += 1
                    is_valid = False

            if is_valid:
                success_count += 1
                logger.info(f"✅ 第{i+1}周 ({current_date}) 通过U型左侧高点验证")
                break  # 找到一个就够了
    
        if success_count > 0:
            total_success += 1

        logger.info(f"股票 {stock_code}: 检查{total_checked}个点，通过{success_count}个")

    logger.info(f"\n=== 总结 ===")
    logger.info(f"总测试股票数: {total_stocks}")
    logger.info(f"有通过验证的股票数: {total_success}")
    logger.info(f"股票通过率: {total_success/total_stocks*100:.1f}%" if total_stocks > 0 else "无数据")
    
except Exception as e:
    logger.error(f"❌ 调试失败: {e}")
    import traceback
    traceback.print_exc()
