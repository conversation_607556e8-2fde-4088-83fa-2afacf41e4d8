#!/usr/bin/env python3
"""
调试交易量验证逻辑
"""

import sys
import os
import logging

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置详细日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

logger = logging.getLogger(__name__)

try:
    from models.database import DatabaseManager, StockDataModel
    from config.analysis_config import U_PATTERN_CONFIG
    
    # 测试数据库连接
    db_manager = DatabaseManager()
    stock_model = StockDataModel(db_manager)
    
    # 测试通过U型左侧高点验证的股票
    test_stocks = ['000004', '000007']
    
    for stock_code in test_stocks:
        logger.info(f"\n{'='*60}")
        logger.info(f"🔍 调试股票 {stock_code} 的交易量验证")
        logger.info(f"{'='*60}")
        
        # 获取数据
        weekly_data = stock_model.get_weekly_data(stock_code)
        avg_trade_data = stock_model.get_avg_trade_data(stock_code)
        
        logger.info(f"周线数据条数: {len(weekly_data)}")
        logger.info(f"平均交易量数据条数: {len(avg_trade_data)}")
        
        if len(weekly_data) < 20 or len(avg_trade_data) == 0:
            logger.warning(f"⚠️ 股票 {stock_code} 数据不足，跳过")
            continue
        
        # 检查平均交易量数据的结构
        logger.info("\n--- 平均交易量数据样本 ---")
        for i, data in enumerate(avg_trade_data[:5]):
            logger.info(f"  {i+1}: {data}")
        
        # 检查周线数据的交易量字段
        logger.info("\n--- 周线数据交易量样本 ---")
        for i, data in enumerate(weekly_data[:5]):
            logger.info(f"  {i+1}: 日期={data['trade_date']}, 交易量={data.get('jy_quantity', 'N/A')}")
        
        # 测试交易量验证逻辑
        logger.info("\n--- 测试交易量验证逻辑 ---")
        
        # 从第11周开始检查
        pre_month = U_PATTERN_CONFIG['pre_month']  # 10
        
        volume_pass_count = 0
        volume_fail_count = 0
        
        for i in range(pre_month, min(pre_month + 10, len(weekly_data))):
            current_data = weekly_data[i]
            current_volume = float(current_data.get('jy_quantity', 0))
            current_date = current_data['trade_date']
            
            # 查找对应的5日平均交易量
            avg_volume = None
            for avg_data in avg_trade_data:
                if (avg_data['trade_date'] == current_date and 
                    avg_data['avg_days'] == 5):
                    avg_volume = float(avg_data.get('avg_qty', 0))
                    break
            
            if avg_volume is not None:
                required_volume = avg_volume * 1.05
                passed = current_volume >= required_volume
                
                if passed:
                    volume_pass_count += 1
                    logger.info(f"  ✅ 第{i+1}周 ({current_date}): 当前量{current_volume:.0f} >= 要求量{required_volume:.0f}")
                else:
                    volume_fail_count += 1
                    logger.info(f"  ❌ 第{i+1}周 ({current_date}): 当前量{current_volume:.0f} < 要求量{required_volume:.0f}")
            else:
                volume_fail_count += 1
                logger.info(f"  ❌ 第{i+1}周 ({current_date}): 未找到5日平均交易量数据")
        
        logger.info(f"\n--- 交易量验证总结 ---")
        logger.info(f"通过验证: {volume_pass_count}")
        logger.info(f"未通过验证: {volume_fail_count}")
        logger.info(f"通过率: {volume_pass_count/(volume_pass_count+volume_fail_count)*100:.1f}%" if (volume_pass_count+volume_fail_count) > 0 else "无数据")
    
    logger.info(f"\n{'='*60}")
    logger.info("✅ 调试完成")
    
except Exception as e:
    logger.error(f"❌ 调试失败: {e}")
    import traceback
    traceback.print_exc()
