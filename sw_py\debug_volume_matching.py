#!/usr/bin/env python3
"""
调试交易量数据匹配问题
"""

import sys
import os
import logging

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models.database import DatabaseManager, StockDataModel

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def debug_volume_matching():
    """调试交易量数据匹配问题"""
    
    print('🔍 调试交易量数据匹配问题')
    
    db = DatabaseManager()
    stock_model = StockDataModel(db)
    
    stock_code = '000001'
    
    # 获取数据
    weekly_data = stock_model.get_weekly_data(stock_code)
    avg_trade_data = stock_model.get_avg_trade_data(stock_code)
    
    print(f'\n股票 {stock_code} 数据:')
    print(f'  周线数据: {len(weekly_data)}条')
    print(f'  平均交易量数据: {len(avg_trade_data)}条')
    
    # 检查2024年的数据匹配
    print(f'\n🔍 检查2024年数据匹配:')
    
    # 找到2024年的周线数据
    weekly_2024 = [d for d in weekly_data if '2024' in str(d['trade_date'])]
    print(f'2024年周线数据: {len(weekly_2024)}条')
    
    # 找到2024年的5日平均交易量数据
    avg_2024 = [d for d in avg_trade_data if '2024' in str(d['trade_date']) and d['avg_days'] == 5]
    print(f'2024年5日平均交易量数据: {len(avg_2024)}条')
    
    # 检查具体的匹配情况
    print(f'\n📊 具体匹配检查 (2024年前10周):')
    
    matched_count = 0
    for i, weekly in enumerate(weekly_2024[:10]):
        weekly_date = weekly['trade_date']
        weekly_volume = weekly['jy_quantity']
        
        # 查找匹配的5日平均数据
        matched_avg = None
        for avg in avg_trade_data:
            if avg['trade_date'] == weekly_date and avg['avg_days'] == 5:
                matched_avg = avg
                break
        
        if matched_avg:
            matched_count += 1
            avg_volume = matched_avg['avg_qty']
            required_volume = float(avg_volume) * 1.05
            is_valid = weekly_volume >= required_volume
            status = "✅" if is_valid else "❌"
            print(f'  {weekly_date}: {status} 当前{weekly_volume} vs 要求{required_volume:.0f} (5日均{avg_volume})')
        else:
            print(f'  {weekly_date}: ❌ 未找到5日平均数据')
    
    print(f'\n匹配率: {matched_count}/10 ({matched_count/10:.1%})')
    
    # 检查数据类型和格式
    print(f'\n🔍 检查数据类型和格式:')
    
    if weekly_2024:
        sample_weekly = weekly_2024[0]
        print(f'周线数据样本:')
        print(f'  trade_date: {sample_weekly["trade_date"]} (类型: {type(sample_weekly["trade_date"])})')
        print(f'  jy_quantity: {sample_weekly["jy_quantity"]} (类型: {type(sample_weekly["jy_quantity"])})')
    
    if avg_2024:
        sample_avg = avg_2024[0]
        print(f'平均交易量数据样本:')
        print(f'  trade_date: {sample_avg["trade_date"]} (类型: {type(sample_avg["trade_date"])})')
        print(f'  avg_qty: {sample_avg["avg_qty"]} (类型: {type(sample_avg["avg_qty"])})')
        print(f'  avg_days: {sample_avg["avg_days"]} (类型: {type(sample_avg["avg_days"])})')

def test_specific_dates():
    """测试特定日期的匹配"""
    
    print(f'\n🧪 测试特定日期的匹配:')
    
    db = DatabaseManager()
    
    # 测试DEBUG日志中提到的日期
    test_dates = ['2024-06-28', '2024-07-05', '2024-07-12']
    
    for test_date in test_dates:
        print(f'\n--- 测试日期 {test_date} ---')
        
        # 检查周线数据
        weekly_check = db.execute_query('''
            SELECT stock_code, trade_date, jy_quantity
            FROM stock_weekly_data 
            WHERE stock_code = '000001' AND trade_date = %s
        ''', (test_date,))
        
        if weekly_check:
            print(f'  周线数据: ✅ 找到')
            weekly_data = weekly_check[0]
            print(f'    交易量: {weekly_data["jy_quantity"]}')
        else:
            print(f'  周线数据: ❌ 未找到')
            continue
        
        # 检查5日平均交易量数据
        avg_check = db.execute_query('''
            SELECT stock_code, trade_date, avg_qty, avg_days
            FROM stock_avg_trade 
            WHERE stock_code = '000001' AND trade_date = %s AND avg_days = 5
        ''', (test_date,))
        
        if avg_check:
            print(f'  5日平均数据: ✅ 找到')
            avg_data = avg_check[0]
            print(f'    平均交易量: {avg_data["avg_qty"]}')
            
            # 计算验证结果
            current_volume = float(weekly_data["jy_quantity"])
            avg_volume = float(avg_data["avg_qty"])
            required_volume = avg_volume * 1.05
            is_valid = current_volume >= required_volume
            
            print(f'    验证结果: {"✅" if is_valid else "❌"} {current_volume} >= {required_volume:.0f}')
        else:
            print(f'  5日平均数据: ❌ 未找到')
            
            # 检查是否有其他avg_days的数据
            other_avg = db.execute_query('''
                SELECT avg_days, COUNT(*) as count
                FROM stock_avg_trade 
                WHERE stock_code = '000001' AND trade_date = %s
                GROUP BY avg_days
            ''', (test_date,))
            
            if other_avg:
                print(f'    其他平均天数数据:')
                for row in other_avg:
                    print(f'      {row["avg_days"]}日: {row["count"]}条')

def check_data_loading():
    """检查数据加载过程"""
    
    print(f'\n🔍 检查数据加载过程:')
    
    db = DatabaseManager()
    stock_model = StockDataModel(db)
    
    stock_code = '000001'
    
    # 直接查询数据库
    print(f'直接查询数据库:')
    
    weekly_direct = db.execute_query('''
        SELECT COUNT(*) as count
        FROM stock_weekly_data 
        WHERE stock_code = %s AND YEAR(trade_date) = 2024
    ''', (stock_code,))
    
    avg_direct = db.execute_query('''
        SELECT COUNT(*) as count
        FROM stock_avg_trade 
        WHERE stock_code = %s AND YEAR(trade_date) = 2024 AND avg_days = 5
    ''', (stock_code,))
    
    print(f'  2024年周线数据: {weekly_direct[0]["count"]}条')
    print(f'  2024年5日平均数据: {avg_direct[0]["count"]}条')
    
    # 通过StockDataModel查询
    print(f'\n通过StockDataModel查询:')
    
    weekly_model = stock_model.get_weekly_data(stock_code)
    avg_model = stock_model.get_avg_trade_data(stock_code)
    
    weekly_2024_model = [d for d in weekly_model if '2024' in str(d['trade_date'])]
    avg_2024_model = [d for d in avg_model if '2024' in str(d['trade_date']) and d['avg_days'] == 5]
    
    print(f'  2024年周线数据: {len(weekly_2024_model)}条')
    print(f'  2024年5日平均数据: {len(avg_2024_model)}条')
    
    # 检查是否有差异
    if weekly_direct[0]["count"] != len(weekly_2024_model):
        print(f'  ⚠️  周线数据数量不一致!')
    
    if avg_direct[0]["count"] != len(avg_2024_model):
        print(f'  ⚠️  5日平均数据数量不一致!')

def main():
    """主函数"""
    logger.info('🔍 开始调试交易量数据匹配问题')
    
    try:
        debug_volume_matching()
        test_specific_dates()
        check_data_loading()
        
        logger.info('✅ 交易量匹配调试完成')
        
    except Exception as e:
        logger.error(f'❌ 调试失败: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
