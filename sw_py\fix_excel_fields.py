#!/usr/bin/env python3
"""
修复Excel导出字段问题
1. 修复换手率计算（总换手率、平均换手率）
2. 修复字段映射与Oracle包一致性
"""

import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models.database import DatabaseManager, StockDataModel
from config.analysis_config import *

def analyze_excel_field_issues():
    """分析Excel字段问题"""
    
    print('🔍 分析Excel字段问题')
    print('=' * 80)
    
    # Oracle包中的字段映射（从第881-903行提取）
    oracle_excel_fields = [
        ('gp_num', '股票编号'),
        ('gp_name', '股票名称'),
        ('u_ldate', 'U型左侧高点周'),
        ('u_lhight', 'U型左侧高点价格'),
        ('u_rdate', 'U型右侧高点周'),
        ('u_rhight', 'U型右侧高点价格'),
        ('u_lowest_date', 'U型最低点周'),
        ('u_lowest', 'U型最低点价格'),
        ('v_lowest_date', 'V型最低点周'),
        ('v_lowest', 'V型最低点价格'),
        ('v_rdate', 'V型右侧高点周'),
        ('v_rhight', 'V型右侧高点价格'),
        ('sucess_date', '最高点周九十天'),      # 注意Oracle拼写错误：sucess
        ('sucess_price', '最高点价格九十天'),
        ('lower_date', '最低点周九十天'),
        ('lower_price', '内最低点价格九十天'),
        ('u_low1_date', '最低点周三分之一'),
        ('u_low1_price', '最低点价格三分之一'),
        ('u_low2_date', '最低点周四分之一'),
        ('u_low2_price', '最低点价格四分之一'),
        ('ma_num', 'MA支撑点'),
        ('allqty_rate', '累计换手率'),           # 关键字段
        ('avgqty_rate', '平均换手率'),           # 关键字段
        ('ATTRIBUTE3', '最近突破日'),            # 对应breakthrough_date
        ('ATTRIBUTE1', '突破日收盘价'),          # 对应breakthrough_price
        ('attribute2', '是否突破')               # 对应is_breakthrough
    ]
    
    # Python当前的字段映射
    python_excel_fields = [
        ('stock_code', '股票代码'),
        ('stock_name', '股票名称'),
        ('u_left_date', 'U型左侧日期'),
        ('u_left_price', 'U型左侧价格'),
        ('u_right_date', 'U型右侧日期'),
        ('u_right_price', 'U型右侧价格'),
        ('u_lowest_price', 'U型最低价'),
        ('u_lowest_date', 'U型最低日期'),
        ('v_right_date', 'V型右侧日期'),
        ('v_right_price', 'V型右侧价格'),
        ('v_lowest_price', 'V型最低价'),
        ('v_lowest_date', 'V型最低日期'),
        ('all_turnover_rate', '总换手率'),       # 对应Oracle allqty_rate
        ('avg_turnover_rate', '平均换手率'),     # 对应Oracle avgqty_rate
        ('success_price', '90天最高价'),
        ('success_date', '90天最高日期'),
        ('lower_price', '90天最低价'),
        ('lower_date', '90天最低日期'),
        ('u_low1_price', 'U型1/3段最低价'),
        ('u_low1_date', 'U型1/3段最低日期'),
        ('u_low2_price', 'U型1/4段最低价'),
        ('u_low2_date', 'U型1/4段最低日期'),
        ('ma_num', 'MA支撑点'),
        ('is_breakthrough', '是否突破'),
        ('breakthrough_price', '突破价格'),
        ('breakthrough_date', '突破日期')
    ]
    
    print('\n📊 字段对比分析:')
    print('-' * 80)
    
    # 分析字段差异
    oracle_fields = {field[1]: field[0] for field in oracle_excel_fields}
    python_fields = {field[1]: field[0] for field in python_excel_fields}
    
    print('\n🔍 Oracle字段 vs Python字段:')
    for oracle_display, oracle_field in oracle_excel_fields:
        python_field = python_fields.get(oracle_display, 'NOT_FOUND')
        if python_field == 'NOT_FOUND':
            # 查找相似字段
            similar_fields = [p for p in python_fields.keys() if oracle_display in p or p in oracle_display]
            if similar_fields:
                print(f'  {oracle_display:20s} | Oracle: {oracle_field:20s} | Python: {similar_fields[0]:20s} | ⚠️ 相似')
            else:
                print(f'  {oracle_display:20s} | Oracle: {oracle_field:20s} | Python: {"缺失":20s} | ❌ 缺失')
        else:
            print(f'  {oracle_display:20s} | Oracle: {oracle_field:20s} | Python: {python_field:20s} | ✅ 匹配')

def analyze_turnover_calculation():
    """分析换手率计算问题"""
    
    print('\n🧮 换手率计算分析:')
    print('=' * 80)
    
    print('\n📋 Oracle换手率计算逻辑:')
    print('  1. 使用GP_JY_D日线数据（不是周线）')
    print('  2. 时间范围：U_LEFT_DATE 到 U_RIGHT_DATE')
    print('  3. 计算公式：')
    print('     - 累计换手率 = SUM(JY_QUANTITY / get_gbsum(gp_num, jy_date))')
    print('     - 平均换手率 = AVG(JY_QUANTITY / get_gbsum(gp_num, jy_date))')
    print('  4. get_gbsum函数：获取股票在指定日期的总股本')
    print('     - 从GP_GB_INFO_V表获取TRADE_QTY（总股本）')
    print('     - 取小于指定日期的最新记录')
    
    print('\n📋 Python当前实现问题:')
    print('  ❌ 1. 换手率字段在pattern_analyzer.py中未计算')
    print('  ❌ 2. 缺少get_gbsum等价函数获取总股本')
    print('  ❌ 3. 使用周线数据而非日线数据')
    print('  ❌ 4. 时间范围可能不正确')

def check_current_results():
    """检查当前结果中的字段值"""
    
    print('\n🔍 检查当前分析结果:')
    print('=' * 80)
    
    try:
        db = DatabaseManager()
        
        # 获取最新的分析结果
        sql = """
        SELECT stock_code, stock_name, all_turnover_rate, avg_turnover_rate,
               ma_num, is_breakthrough, breakthrough_price, breakthrough_date,
               success_date, success_price
        FROM stock_analysis_result 
        ORDER BY id DESC 
        LIMIT 5
        """
        
        results = db.execute_query(sql)
        
        if results:
            print(f'\n📊 最新{len(results)}条结果字段值:')
            for i, result in enumerate(results, 1):
                print(f'\n  记录{i}: {result["stock_code"]} {result["stock_name"]}')
                print(f'    总换手率: {result.get("all_turnover_rate", "NULL")}')
                print(f'    平均换手率: {result.get("avg_turnover_rate", "NULL")}')
                print(f'    MA支撑点: {result.get("ma_num", "NULL")}')
                print(f'    是否突破: {result.get("is_breakthrough", "NULL")}')
                print(f'    突破价格: {result.get("breakthrough_price", "NULL")}')
                print(f'    突破日期: {result.get("breakthrough_date", "NULL")}')
                print(f'    成功日期: {result.get("success_date", "NULL")}')
                print(f'    成功价格: {result.get("success_price", "NULL")}')
        else:
            print('  ❌ 未找到分析结果')
            
    except Exception as e:
        print(f'  ❌ 查询失败: {e}')

def identify_fix_requirements():
    """确定修复需求"""
    
    print('\n🔧 修复需求分析:')
    print('=' * 80)
    
    fix_requirements = [
        {
            'issue': '换手率计算缺失',
            'description': 'all_turnover_rate和avg_turnover_rate字段值为0',
            'oracle_logic': '使用日线数据和总股本计算真实换手率',
            'fix_needed': '在pattern_analyzer.py中添加换手率计算逻辑',
            'priority': '高'
        },
        {
            'issue': 'MA支撑点值相同',
            'description': 'ma_num字段显示相同值',
            'oracle_logic': '基于移动平均线表GP_AVG_JYW计算最佳支撑',
            'fix_needed': '完善_find_best_moving_average_support方法',
            'priority': '中'
        },
        {
            'issue': '突破相关字段值相同',
            'description': 'is_breakthrough, breakthrough_price, breakthrough_date显示相同',
            'oracle_logic': '基于CHECK_PRICE存储过程判断V型右侧高点后的突破',
            'fix_needed': '完善check_price_logic方法，使用日线数据',
            'priority': '中'
        },
        {
            'issue': 'Excel字段名称不一致',
            'description': 'Python字段名与Oracle导出的中文名称不完全一致',
            'oracle_logic': '严格按照Oracle包第881-903行的字段映射',
            'fix_needed': '更新export_to_excel方法中的字段映射',
            'priority': '低'
        }
    ]
    
    for i, req in enumerate(fix_requirements, 1):
        print(f'\n📋 修复需求{i}: {req["issue"]}')
        print(f'   问题描述: {req["description"]}')
        print(f'   Oracle逻辑: {req["oracle_logic"]}')
        print(f'   修复方案: {req["fix_needed"]}')
        print(f'   优先级: {req["priority"]}')

def main():
    """主函数"""
    try:
        print('🔍 开始Excel字段问题分析')
        
        # 1. 字段映射分析
        analyze_excel_field_issues()
        
        # 2. 换手率计算分析
        analyze_turnover_calculation()
        
        # 3. 检查当前结果
        check_current_results()
        
        # 4. 确定修复需求
        identify_fix_requirements()
        
        print('\n' + '=' * 80)
        print('📋 分析总结:')
        print('=' * 80)
        print('✅ 已识别出4个主要问题')
        print('🔧 需要修复换手率计算逻辑（高优先级）')
        print('🔧 需要完善MA支撑点和突破判断逻辑（中优先级）')
        print('🔧 需要更新Excel字段映射（低优先级）')
        
        print('\n🎯 下一步：开始修复这些问题')
        
    except Exception as e:
        print(f'❌ 分析失败: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
