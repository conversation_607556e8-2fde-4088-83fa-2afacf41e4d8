#!/usr/bin/env python3
"""
大样本分析筛选失败原因
"""

import sys
import os
import logging
from collections import defaultdict

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models.database import DatabaseManager, StockDataModel
from analysis.pattern_analyzer import PatternAnalyzer
from config.analysis_config import U_PATTERN_CONFIG, V_PATTERN_CONFIG, VOLUME_CONFIG

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class FilterAnalyzer:
    """筛选条件分析器"""
    
    def __init__(self):
        self.db = DatabaseManager()
        self.stock_model = StockDataModel(self.db)
        self.analyzer = PatternAnalyzer(U_PATTERN_CONFIG, V_PATTERN_CONFIG, VOLUME_CONFIG)
        
        # 统计数据
        self.stats = {
            'total_stocks': 0,
            'insufficient_data': 0,
            'no_avg_trade_data': 0,
            'u_left_failed': 0,
            'u_right_failed': 0,
            'u_bottom_failed': 0,
            'v_pattern_failed': 0,
            'volume_failed': 0,
            'success': 0
        }
        
        # 详细失败原因
        self.failure_details = defaultdict(list)
        
    def analyze_single_stock(self, stock_code, stock_name):
        """分析单只股票的筛选过程"""
        try:
            # 获取数据
            weekly_data = self.stock_model.get_weekly_data(stock_code)
            avg_trade_data = self.stock_model.get_avg_trade_data(stock_code)
            
            # 检查数据充足性
            if len(weekly_data) < 20:
                self.stats['insufficient_data'] += 1
                self.failure_details['insufficient_data'].append({
                    'stock_code': stock_code,
                    'weekly_count': len(weekly_data),
                    'reason': f'周线数据不足: {len(weekly_data)} < 20'
                })
                return 'insufficient_data'
            
            if len(avg_trade_data) == 0:
                self.stats['no_avg_trade_data'] += 1
                self.failure_details['no_avg_trade_data'].append({
                    'stock_code': stock_code,
                    'reason': '无平均交易量数据'
                })
                return 'no_avg_trade_data'
            
            # 分析U型左侧高点
            u_left_candidates = []
            for i in range(U_PATTERN_CONFIG['pre_month'], len(weekly_data)):
                current_high = float(weekly_data[i]['high_price'])
                if self.analyzer.check_u_left_strict(weekly_data, i, current_high):
                    u_left_candidates.append((i, current_high, weekly_data[i]['trade_date']))
            
            if not u_left_candidates:
                self.stats['u_left_failed'] += 1
                # 分析为什么U型左侧高点失败
                self._analyze_u_left_failure(stock_code, weekly_data)
                return 'u_left_failed'
            
            # 分析U型右侧高点
            u_right_candidates = []
            for left_idx, left_price, left_date in u_left_candidates[:5]:  # 只测试前5个候选
                u_right_result = self.analyzer.get_u_right_strict(weekly_data, avg_trade_data, left_idx, left_price, stock_code)
                if u_right_result:
                    u_right_candidates.append((left_idx, left_price, left_date, u_right_result))
            
            if not u_right_candidates:
                self.stats['u_right_failed'] += 1
                self._analyze_u_right_failure(stock_code, u_left_candidates[:3])
                return 'u_right_failed'
            
            # 分析U型底部
            u_bottom_candidates = []
            for left_idx, left_price, left_date, (right_idx, right_date, right_price) in u_right_candidates:
                u_bottom_result = self.analyzer.check_u_bottom_strict(weekly_data, left_idx, right_idx, left_price)
                if u_bottom_result:
                    u_bottom_candidates.append((left_idx, right_idx, u_bottom_result))
            
            if not u_bottom_candidates:
                self.stats['u_bottom_failed'] += 1
                self._analyze_u_bottom_failure(stock_code, u_right_candidates[:2])
                return 'u_bottom_failed'
            
            # 如果到这里说明找到了完整的U型形态
            self.stats['success'] += 1
            logger.info(f'✅ {stock_code} 找到U型形态: {len(u_bottom_candidates)}个')
            return 'success'
            
        except Exception as e:
            logger.error(f'分析股票 {stock_code} 失败: {e}')
            return 'error'
    
    def _analyze_u_left_failure(self, stock_code, weekly_data):
        """分析U型左侧高点失败原因"""
        # 统计价格波动情况
        price_changes = []
        for i in range(1, min(len(weekly_data), 50)):
            prev_high = float(weekly_data[i-1]['high_price'])
            curr_high = float(weekly_data[i]['high_price'])
            change_rate = (curr_high - prev_high) / prev_high
            price_changes.append(abs(change_rate))
        
        avg_volatility = sum(price_changes) / len(price_changes) if price_changes else 0
        max_volatility = max(price_changes) if price_changes else 0
        
        # 检查是否有任何点能满足放宽条件
        relaxed_candidates = 0
        for threshold in [0.05, 0.08, 0.10]:  # 5%, 8%, 10%
            for i in range(10, min(len(weekly_data), 30)):
                current_high = float(weekly_data[i]['high_price'])
                start_idx = max(0, i - 10)
                threshold_price = current_high * (1 + threshold)
                
                is_valid = True
                for j in range(start_idx, i):
                    if j != i and float(weekly_data[j]['high_price']) > threshold_price:
                        is_valid = False
                        break
                
                if is_valid:
                    relaxed_candidates += 1
                    break
            
            if relaxed_candidates > 0:
                break
        
        self.failure_details['u_left_failed'].append({
            'stock_code': stock_code,
            'avg_volatility': f'{avg_volatility:.2%}',
            'max_volatility': f'{max_volatility:.2%}',
            'relaxed_threshold_needed': f'{threshold:.0%}' if relaxed_candidates > 0 else '>10%',
            'reason': f'价格波动过大，平均{avg_volatility:.1%}，最大{max_volatility:.1%}'
        })
    
    def _analyze_u_right_failure(self, stock_code, u_left_candidates):
        """分析U型右侧高点失败原因"""
        reasons = []
        for left_idx, left_price, left_date in u_left_candidates:
            # 这里可以添加更详细的右侧高点失败分析
            reasons.append(f'左侧{left_idx}({left_date})未找到匹配右侧高点')
        
        self.failure_details['u_right_failed'].append({
            'stock_code': stock_code,
            'left_candidates': len(u_left_candidates),
            'reasons': reasons[:2]  # 只记录前2个
        })
    
    def _analyze_u_bottom_failure(self, stock_code, u_right_candidates):
        """分析U型底部失败原因"""
        reasons = []
        for left_idx, left_price, left_date, (right_idx, right_date, right_price) in u_right_candidates:
            reasons.append(f'U型{left_idx}-{right_idx}底部不满足12%-50%条件')
        
        self.failure_details['u_bottom_failed'].append({
            'stock_code': stock_code,
            'right_candidates': len(u_right_candidates),
            'reasons': reasons[:2]
        })
    
    def analyze_large_sample(self, sample_size=500):
        """分析大样本股票"""
        logger.info(f'开始分析 {sample_size} 只股票的筛选失败原因')
        
        # 获取股票列表
        stock_list = self.stock_model.get_stock_list()
        logger.info(f'总股票数: {len(stock_list)}')
        
        # 限制样本大小
        sample_stocks = stock_list[:sample_size]
        
        for i, stock in enumerate(sample_stocks):
            stock_code = stock['gp_num']
            stock_name = stock['gp_name']
            
            self.stats['total_stocks'] += 1
            
            if i % 50 == 0:
                logger.info(f'进度: {i}/{len(sample_stocks)} ({i/len(sample_stocks):.1%})')
            
            result = self.analyze_single_stock(stock_code, stock_name)
            
            # 每100只股票输出一次中间统计
            if (i + 1) % 100 == 0:
                self._print_intermediate_stats()
    
    def _print_intermediate_stats(self):
        """打印中间统计结果"""
        total = self.stats['total_stocks']
        logger.info(f'中间统计 (已分析{total}只):')
        logger.info(f'  数据不足: {self.stats["insufficient_data"]} ({self.stats["insufficient_data"]/total:.1%})')
        logger.info(f'  无交易量数据: {self.stats["no_avg_trade_data"]} ({self.stats["no_avg_trade_data"]/total:.1%})')
        logger.info(f'  U型左侧失败: {self.stats["u_left_failed"]} ({self.stats["u_left_failed"]/total:.1%})')
        logger.info(f'  U型右侧失败: {self.stats["u_right_failed"]} ({self.stats["u_right_failed"]/total:.1%})')
        logger.info(f'  U型底部失败: {self.stats["u_bottom_failed"]} ({self.stats["u_bottom_failed"]/total:.1%})')
        logger.info(f'  成功: {self.stats["success"]} ({self.stats["success"]/total:.1%})')
    
    def print_final_analysis(self):
        """打印最终分析结果"""
        total = self.stats['total_stocks']
        
        print('\n' + '='*60)
        print('📊 大样本筛选失败原因分析报告')
        print('='*60)
        
        print(f'\n📈 总体统计 (样本数: {total}):')
        for key, value in self.stats.items():
            if key != 'total_stocks':
                percentage = value / total * 100 if total > 0 else 0
                print(f'  {key}: {value} ({percentage:.1f}%)')
        
        print(f'\n🔍 详细失败原因分析:')
        
        # U型左侧高点失败分析
        if self.failure_details['u_left_failed']:
            print(f'\n1. U型左侧高点失败 ({len(self.failure_details["u_left_failed"])}只):')
            volatility_high = sum(1 for item in self.failure_details['u_left_failed'] 
                                if float(item['avg_volatility'].rstrip('%')) > 5)
            print(f'   - 价格波动过大(>5%): {volatility_high}只')
            
            # 显示几个典型案例
            print('   典型案例:')
            for item in self.failure_details['u_left_failed'][:3]:
                print(f'     {item["stock_code"]}: 平均波动{item["avg_volatility"]}, 需要阈值{item["relaxed_threshold_needed"]}')
        
        # U型右侧高点失败分析
        if self.failure_details['u_right_failed']:
            print(f'\n2. U型右侧高点失败 ({len(self.failure_details["u_right_failed"])}只):')
            print('   典型案例:')
            for item in self.failure_details['u_right_failed'][:3]:
                print(f'     {item["stock_code"]}: 有{item["left_candidates"]}个左侧候选点')
        
        # U型底部失败分析
        if self.failure_details['u_bottom_failed']:
            print(f'\n3. U型底部失败 ({len(self.failure_details["u_bottom_failed"])}只):')
            print('   典型案例:')
            for item in self.failure_details['u_bottom_failed'][:3]:
                print(f'     {item["stock_code"]}: 有{item["right_candidates"]}个右侧候选点')
        
        print(f'\n💡 建议:')
        u_left_rate = self.stats['u_left_failed'] / total * 100
        if u_left_rate > 50:
            print(f'   - U型左侧高点失败率过高({u_left_rate:.1f}%)，建议放宽left_diff_rate从3%到5-8%')
        
        u_right_rate = self.stats['u_right_failed'] / total * 100
        if u_right_rate > 20:
            print(f'   - U型右侧高点失败率较高({u_right_rate:.1f}%)，建议检查交易量条件和价格匹配逻辑')
        
        u_bottom_rate = self.stats['u_bottom_failed'] / total * 100
        if u_bottom_rate > 10:
            print(f'   - U型底部失败率较高({u_bottom_rate:.1f}%)，建议检查12%-50%的价格区间设置')

def main():
    """主函数"""
    logger.info('🔍 开始大样本筛选失败原因分析')
    
    try:
        analyzer = FilterAnalyzer()
        
        # 分析500只股票的样本
        analyzer.analyze_large_sample(sample_size=500)
        
        # 打印最终分析报告
        analyzer.print_final_analysis()
        
        logger.info('✅ 大样本分析完成')
        
    except Exception as e:
        logger.error(f'❌ 分析失败: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
