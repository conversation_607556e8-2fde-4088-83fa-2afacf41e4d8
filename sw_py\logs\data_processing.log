2025-07-03 15:49:54,792 - [DEBUG] data_processing:debug:122 - 🔵 进入函数 process_weekly_data_multithread(stock_count=5)
2025-07-03 15:49:54,925 - [INFO] data_processing:info:126 - 🔄 增量处理 开始 weekly_data_cleanup: 5 条数据
2025-07-03 15:49:54,982 - [INFO] data_processing:info:126 - 🔄 增量处理 完成 weekly_data_cleanup: 0 条数据
2025-07-03 15:49:55,118 - [INFO] data_processing:info:126 - 📊 增量模式：将 5 只股票分成 5 个块进行处理
2025-07-03 15:49:55,123 - [DEBUG] data_processing:debug:122 - 🔄 块 1 开始处理 1 只股票的周线数据
2025-07-03 15:49:55,136 - [DEBUG] data_processing:debug:122 - 🔄 块 2 开始处理 1 只股票的周线数据
2025-07-03 15:49:55,149 - [DEBUG] data_processing:debug:122 - 🔄 块 3 开始处理 1 只股票的周线数据
2025-07-03 15:49:55,152 - [DEBUG] data_processing:debug:122 - 🔄 块 4 开始处理 1 只股票的周线数据
2025-07-03 15:49:55,153 - [DEBUG] data_processing:debug:122 - 🔄 块 5 开始处理 1 只股票的周线数据
2025-07-03 15:49:55,521 - [ERROR] data_processing:error:140 - ❌ 处理周线数据块 1 失败
异常详情: ProgressLogger.update() takes from 1 to 2 positional arguments but 3 were given
堆栈跟踪:
Traceback (most recent call last):
  File "D:\IDE\cursor\sharewin\sw_py\main.py", line 400, in process_weekly_data_multithread
    progress_logger.update(1, f"块{chunk_id + 1}完成({processed_count}只股票)")
TypeError: ProgressLogger.update() takes from 1 to 2 positional arguments but 3 were given

2025-07-03 15:49:56,131 - [ERROR] data_processing:error:140 - ❌ 处理周线数据块 5 失败
异常详情: ProgressLogger.update() takes from 1 to 2 positional arguments but 3 were given
堆栈跟踪:
Traceback (most recent call last):
  File "D:\IDE\cursor\sharewin\sw_py\main.py", line 400, in process_weekly_data_multithread
    progress_logger.update(1, f"块{chunk_id + 1}完成({processed_count}只股票)")
TypeError: ProgressLogger.update() takes from 1 to 2 positional arguments but 3 were given

2025-07-03 15:49:56,342 - [ERROR] data_processing:error:140 - ❌ 处理周线数据块 3 失败
异常详情: ProgressLogger.update() takes from 1 to 2 positional arguments but 3 were given
堆栈跟踪:
Traceback (most recent call last):
  File "D:\IDE\cursor\sharewin\sw_py\main.py", line 400, in process_weekly_data_multithread
    progress_logger.update(1, f"块{chunk_id + 1}完成({processed_count}只股票)")
TypeError: ProgressLogger.update() takes from 1 to 2 positional arguments but 3 were given

2025-07-03 15:49:56,349 - [ERROR] data_processing:error:140 - ❌ 处理周线数据块 4 失败
异常详情: ProgressLogger.update() takes from 1 to 2 positional arguments but 3 were given
堆栈跟踪:
Traceback (most recent call last):
  File "D:\IDE\cursor\sharewin\sw_py\main.py", line 400, in process_weekly_data_multithread
    progress_logger.update(1, f"块{chunk_id + 1}完成({processed_count}只股票)")
TypeError: ProgressLogger.update() takes from 1 to 2 positional arguments but 3 were given

2025-07-03 15:49:56,356 - [ERROR] data_processing:error:140 - ❌ 处理周线数据块 2 失败
异常详情: ProgressLogger.update() takes from 1 to 2 positional arguments but 3 were given
堆栈跟踪:
Traceback (most recent call last):
  File "D:\IDE\cursor\sharewin\sw_py\main.py", line 400, in process_weekly_data_multithread
    progress_logger.update(1, f"块{chunk_id + 1}完成({processed_count}只股票)")
TypeError: ProgressLogger.update() takes from 1 to 2 positional arguments but 3 were given

2025-07-03 15:49:56,361 - [INFO] data_processing:finish:76 - ✅ 周线数据处理完成: 5 项, 总耗时: 1.2秒
2025-07-03 15:49:56,365 - [DEBUG] data_processing:debug:122 - 🔴 退出函数 process_weekly_data_multithread
2025-07-03 15:51:50,863 - [DEBUG] data_processing:debug:122 - 🔵 进入函数 process_weekly_data_multithread(stock_count=5)
2025-07-03 15:51:51,009 - [INFO] data_processing:info:126 - 🔄 增量处理 开始 weekly_data_cleanup: 5 条数据
2025-07-03 15:51:51,080 - [INFO] data_processing:info:126 - 🔄 增量处理 完成 weekly_data_cleanup: 0 条数据
2025-07-03 15:51:51,231 - [INFO] data_processing:info:126 - 📊 增量模式：将 5 只股票分成 5 个块进行处理
2025-07-03 15:51:51,236 - [DEBUG] data_processing:debug:122 - 🔄 块 1 开始处理 1 只股票的周线数据
2025-07-03 15:51:51,281 - [DEBUG] data_processing:debug:122 - 🔄 块 2 开始处理 1 只股票的周线数据
2025-07-03 15:51:51,302 - [DEBUG] data_processing:debug:122 - 🔄 块 3 开始处理 1 只股票的周线数据
2025-07-03 15:51:51,305 - [DEBUG] data_processing:debug:122 - 🔄 块 4 开始处理 1 只股票的周线数据
2025-07-03 15:51:51,311 - [DEBUG] data_processing:debug:122 - 🔄 块 5 开始处理 1 只股票的周线数据
2025-07-03 15:51:51,702 - [ERROR] data_processing:error:140 - ❌ 处理周线数据块 1 失败
异常详情: ProgressLogger.update() takes from 1 to 2 positional arguments but 3 were given
堆栈跟踪:
Traceback (most recent call last):
  File "D:\IDE\cursor\sharewin\sw_py\main.py", line 400, in process_weekly_data_multithread
    progress_logger.update(1, f"块{chunk_id + 1}完成({processed_count}只股票)")
TypeError: ProgressLogger.update() takes from 1 to 2 positional arguments but 3 were given

2025-07-03 15:51:51,936 - [ERROR] data_processing:error:140 - ❌ 处理周线数据块 2 失败
异常详情: ProgressLogger.update() takes from 1 to 2 positional arguments but 3 were given
堆栈跟踪:
Traceback (most recent call last):
  File "D:\IDE\cursor\sharewin\sw_py\main.py", line 400, in process_weekly_data_multithread
    progress_logger.update(1, f"块{chunk_id + 1}完成({processed_count}只股票)")
TypeError: ProgressLogger.update() takes from 1 to 2 positional arguments but 3 were given

2025-07-03 15:51:52,467 - [ERROR] data_processing:error:140 - ❌ 处理周线数据块 5 失败
异常详情: ProgressLogger.update() takes from 1 to 2 positional arguments but 3 were given
堆栈跟踪:
Traceback (most recent call last):
  File "D:\IDE\cursor\sharewin\sw_py\main.py", line 400, in process_weekly_data_multithread
    progress_logger.update(1, f"块{chunk_id + 1}完成({processed_count}只股票)")
TypeError: ProgressLogger.update() takes from 1 to 2 positional arguments but 3 were given

2025-07-03 15:51:52,565 - [ERROR] data_processing:error:140 - ❌ 处理周线数据块 3 失败
异常详情: ProgressLogger.update() takes from 1 to 2 positional arguments but 3 were given
堆栈跟踪:
Traceback (most recent call last):
  File "D:\IDE\cursor\sharewin\sw_py\main.py", line 400, in process_weekly_data_multithread
    progress_logger.update(1, f"块{chunk_id + 1}完成({processed_count}只股票)")
TypeError: ProgressLogger.update() takes from 1 to 2 positional arguments but 3 were given

2025-07-03 15:51:52,569 - [ERROR] data_processing:error:140 - ❌ 处理周线数据块 4 失败
异常详情: ProgressLogger.update() takes from 1 to 2 positional arguments but 3 were given
堆栈跟踪:
Traceback (most recent call last):
  File "D:\IDE\cursor\sharewin\sw_py\main.py", line 400, in process_weekly_data_multithread
    progress_logger.update(1, f"块{chunk_id + 1}完成({processed_count}只股票)")
TypeError: ProgressLogger.update() takes from 1 to 2 positional arguments but 3 were given

2025-07-03 15:51:52,577 - [INFO] data_processing:finish:76 - ✅ 周线数据处理完成: 5 项, 总耗时: 1.3秒
2025-07-03 15:51:52,578 - [DEBUG] data_processing:debug:122 - 🔴 退出函数 process_weekly_data_multithread
2025-07-03 15:53:55,773 - [DEBUG] data_processing:debug:122 - 🔵 进入函数 process_weekly_data_multithread(stock_count=5)
2025-07-03 15:53:55,913 - [INFO] data_processing:info:126 - 🔄 增量处理 开始 weekly_data_cleanup: 5 条数据
2025-07-03 15:53:55,978 - [INFO] data_processing:info:126 - 🔄 增量处理 完成 weekly_data_cleanup: 0 条数据
2025-07-03 15:53:56,120 - [INFO] data_processing:info:126 - 📊 增量模式：将 5 只股票分成 5 个块进行处理
2025-07-03 15:53:56,127 - [DEBUG] data_processing:debug:122 - 🔄 块 1 开始处理 1 只股票的周线数据
2025-07-03 15:53:56,128 - [DEBUG] data_processing:debug:122 - 🔄 块 2 开始处理 1 只股票的周线数据
2025-07-03 15:53:56,131 - [DEBUG] data_processing:debug:122 - 🔄 块 3 开始处理 1 只股票的周线数据
2025-07-03 15:53:56,132 - [DEBUG] data_processing:debug:122 - 🔄 块 4 开始处理 1 只股票的周线数据
2025-07-03 15:53:56,134 - [DEBUG] data_processing:debug:122 - 🔄 块 5 开始处理 1 只股票的周线数据
2025-07-03 15:53:56,561 - [INFO] data_processing:update:67 - 📊 周线数据处理: 1/5 (20.0%), 已用时: 0.4秒, 预计剩余: 1.7秒
2025-07-03 15:53:56,565 - [DEBUG] data_processing:debug:122 - ✅ 周线数据处理块 1/5 完成，处理 0 只股票
2025-07-03 15:53:57,027 - [DEBUG] data_processing:debug:122 - ✅ 周线数据处理块 5/5 完成，处理 0 只股票
2025-07-03 15:53:57,138 - [DEBUG] data_processing:debug:122 - ✅ 周线数据处理块 3/5 完成，处理 0 只股票
2025-07-03 15:53:57,279 - [DEBUG] data_processing:debug:122 - ✅ 周线数据处理块 2/5 完成，处理 0 只股票
2025-07-03 15:53:57,427 - [INFO] data_processing:update:67 - 📊 周线数据处理: 5/5 (100.0%), 已用时: 1.3秒
2025-07-03 15:53:57,429 - [DEBUG] data_processing:debug:122 - ✅ 周线数据处理块 4/5 完成，处理 0 只股票
2025-07-03 15:53:57,432 - [INFO] data_processing:finish:76 - ✅ 周线数据处理完成: 5 项, 总耗时: 1.3秒
2025-07-03 15:53:57,437 - [DEBUG] data_processing:debug:122 - 🔴 退出函数 process_weekly_data_multithread
2025-07-03 15:57:22,883 - [DEBUG] data_processing:debug:122 - 🔵 进入函数 process_weekly_data_multithread(stock_count=5418)
2025-07-03 15:57:23,049 - [INFO] data_processing:info:126 - 🔄 增量处理 开始 weekly_data_cleanup: 5,418 条数据
2025-07-03 15:57:23,120 - [INFO] data_processing:info:126 - 🔄 增量处理 完成 weekly_data_cleanup: 0 条数据
2025-07-03 15:57:23,270 - [INFO] data_processing:info:126 - 📊 增量模式：将 5418 只股票分成 6 个块进行处理
2025-07-03 15:57:23,275 - [DEBUG] data_processing:debug:122 - 🔄 块 1 开始处理 903 只股票的周线数据
2025-07-03 15:57:23,277 - [DEBUG] data_processing:debug:122 - 🔄 块 2 开始处理 903 只股票的周线数据
2025-07-03 15:57:23,278 - [DEBUG] data_processing:debug:122 - 🔄 块 3 开始处理 903 只股票的周线数据
2025-07-03 15:57:23,280 - [DEBUG] data_processing:debug:122 - 🔄 块 4 开始处理 903 只股票的周线数据
2025-07-03 15:57:23,281 - [DEBUG] data_processing:debug:122 - 🔄 块 5 开始处理 903 只股票的周线数据
2025-07-03 15:57:23,282 - [DEBUG] data_processing:debug:122 - 🔄 块 6 开始处理 903 只股票的周线数据
2025-07-03 15:58:50,415 - [DEBUG] data_processing:debug:122 - 📈 块 2 已处理 100/903 只股票，耗时 87.1秒
2025-07-03 15:58:50,883 - [DEBUG] data_processing:debug:122 - 📈 块 5 已处理 100/903 只股票，耗时 87.6秒
2025-07-03 15:58:50,903 - [DEBUG] data_processing:debug:122 - 📈 块 3 已处理 100/903 只股票，耗时 87.6秒
2025-07-03 15:58:51,163 - [DEBUG] data_processing:debug:122 - 📈 块 1 已处理 100/903 只股票，耗时 87.9秒
2025-07-03 15:58:51,360 - [DEBUG] data_processing:debug:122 - 📈 块 6 已处理 100/903 只股票，耗时 88.1秒
2025-07-03 15:58:53,011 - [DEBUG] data_processing:debug:122 - 📈 块 4 已处理 100/903 只股票，耗时 89.7秒
2025-07-03 15:59:52,192 - [DEBUG] data_processing:debug:122 - 📈 块 2 已处理 200/903 只股票，耗时 148.9秒
2025-07-03 15:59:52,593 - [DEBUG] data_processing:debug:122 - 📈 块 1 已处理 200/903 只股票，耗时 149.3秒
2025-07-03 15:59:52,893 - [DEBUG] data_processing:debug:122 - 📈 块 5 已处理 200/903 只股票，耗时 149.6秒
2025-07-03 15:59:53,111 - [DEBUG] data_processing:debug:122 - 📈 块 3 已处理 200/903 只股票，耗时 149.8秒
2025-07-03 15:59:53,865 - [DEBUG] data_processing:debug:122 - 📈 块 6 已处理 200/903 只股票，耗时 150.6秒
2025-07-03 15:59:54,864 - [DEBUG] data_processing:debug:122 - 📈 块 4 已处理 200/903 只股票，耗时 151.6秒
2025-07-03 16:00:52,896 - [DEBUG] data_processing:debug:122 - 📈 块 5 已处理 300/903 只股票，耗时 209.6秒
2025-07-03 16:00:53,048 - [DEBUG] data_processing:debug:122 - 📈 块 3 已处理 300/903 只股票，耗时 209.8秒
2025-07-03 16:00:53,051 - [DEBUG] data_processing:debug:122 - 📈 块 2 已处理 300/903 只股票，耗时 209.8秒
2025-07-03 16:00:53,346 - [DEBUG] data_processing:debug:122 - 📈 块 1 已处理 300/903 只股票，耗时 210.1秒
2025-07-03 16:00:54,318 - [DEBUG] data_processing:debug:122 - 📈 块 6 已处理 300/903 只股票，耗时 211.0秒
2025-07-03 16:00:55,237 - [DEBUG] data_processing:debug:122 - 📈 块 4 已处理 300/903 只股票，耗时 212.0秒
2025-07-03 16:01:50,742 - [DEBUG] data_processing:debug:122 - 📈 块 5 已处理 400/903 只股票，耗时 267.5秒
2025-07-03 16:01:50,903 - [DEBUG] data_processing:debug:122 - 📈 块 2 已处理 400/903 只股票，耗时 267.6秒
2025-07-03 16:01:51,131 - [DEBUG] data_processing:debug:122 - 📈 块 3 已处理 400/903 只股票，耗时 267.9秒
2025-07-03 16:01:51,707 - [DEBUG] data_processing:debug:122 - 📈 块 1 已处理 400/903 只股票，耗时 268.4秒
2025-07-03 16:01:51,924 - [DEBUG] data_processing:debug:122 - 📈 块 6 已处理 400/903 只股票，耗时 268.6秒
2025-07-03 16:01:53,987 - [DEBUG] data_processing:debug:122 - 📈 块 4 已处理 400/903 只股票，耗时 270.7秒
2025-07-03 16:02:54,723 - [DEBUG] data_processing:debug:122 - 📈 块 3 已处理 500/903 只股票，耗时 331.4秒
2025-07-03 16:02:54,901 - [DEBUG] data_processing:debug:122 - 📈 块 2 已处理 500/903 只股票，耗时 331.6秒
2025-07-03 16:02:55,042 - [DEBUG] data_processing:debug:122 - 📈 块 6 已处理 500/903 只股票，耗时 331.8秒
2025-07-03 16:02:55,107 - [DEBUG] data_processing:debug:122 - 📈 块 1 已处理 500/903 只股票，耗时 331.8秒
2025-07-03 16:02:55,369 - [DEBUG] data_processing:debug:122 - 📈 块 5 已处理 500/903 只股票，耗时 332.1秒
2025-07-03 16:02:59,984 - [DEBUG] data_processing:debug:122 - 📈 块 4 已处理 500/903 只股票，耗时 336.7秒
2025-07-03 16:04:39,728 - [DEBUG] data_processing:debug:122 - 📈 块 3 已处理 600/903 只股票，耗时 436.4秒
2025-07-03 16:04:40,211 - [DEBUG] data_processing:debug:122 - 📈 块 6 已处理 600/903 只股票，耗时 436.9秒
2025-07-03 16:04:40,445 - [DEBUG] data_processing:debug:122 - 📈 块 1 已处理 600/903 只股票，耗时 437.2秒
2025-07-03 16:04:41,293 - [DEBUG] data_processing:debug:122 - 📈 块 2 已处理 600/903 只股票，耗时 438.0秒
2025-07-03 16:04:43,165 - [DEBUG] data_processing:debug:122 - 📈 块 5 已处理 600/903 只股票，耗时 439.9秒
2025-07-03 16:04:43,723 - [DEBUG] data_processing:debug:122 - 📈 块 4 已处理 600/903 只股票，耗时 440.4秒
2025-07-03 16:05:43,402 - [DEBUG] data_processing:debug:122 - 📈 块 3 已处理 700/903 只股票，耗时 500.1秒
2025-07-03 16:05:44,503 - [DEBUG] data_processing:debug:122 - 📈 块 1 已处理 700/903 只股票，耗时 501.2秒
2025-07-03 16:05:44,739 - [DEBUG] data_processing:debug:122 - 📈 块 6 已处理 700/903 只股票，耗时 501.5秒
2025-07-03 16:05:44,875 - [DEBUG] data_processing:debug:122 - 📈 块 2 已处理 700/903 只股票，耗时 501.6秒
2025-07-03 16:05:46,654 - [DEBUG] data_processing:debug:122 - 📈 块 4 已处理 700/903 只股票，耗时 503.4秒
2025-07-03 16:05:47,079 - [DEBUG] data_processing:debug:122 - 📈 块 5 已处理 700/903 只股票，耗时 503.8秒
2025-07-03 16:06:44,394 - [DEBUG] data_processing:debug:122 - 📈 块 1 已处理 800/903 只股票，耗时 561.1秒
2025-07-03 16:06:44,601 - [DEBUG] data_processing:debug:122 - 📈 块 2 已处理 800/903 只股票，耗时 561.3秒
2025-07-03 16:06:44,613 - [DEBUG] data_processing:debug:122 - 📈 块 3 已处理 800/903 只股票，耗时 561.3秒
2025-07-03 16:06:45,030 - [DEBUG] data_processing:debug:122 - 📈 块 6 已处理 800/903 只股票，耗时 561.7秒
2025-07-03 16:06:47,310 - [DEBUG] data_processing:debug:122 - 📈 块 4 已处理 800/903 只股票，耗时 564.0秒
2025-07-03 16:06:48,381 - [DEBUG] data_processing:debug:122 - 📈 块 5 已处理 800/903 只股票，耗时 565.1秒
2025-07-03 16:07:43,310 - [DEBUG] data_processing:debug:122 - 📈 块 1 已处理 900/903 只股票，耗时 620.0秒
2025-07-03 16:07:43,345 - [DEBUG] data_processing:debug:122 - 📈 块 2 已处理 900/903 只股票，耗时 620.1秒
2025-07-03 16:07:43,499 - [DEBUG] data_processing:debug:122 - 📈 块 6 已处理 900/903 只股票，耗时 620.2秒
2025-07-03 16:07:43,714 - [DEBUG] data_processing:debug:122 - 📈 块 3 已处理 900/903 只股票，耗时 620.4秒
2025-07-03 16:07:45,216 - [INFO] data_processing:update:67 - 📊 周线数据处理: 1/6 (16.7%), 已用时: 621.9秒, 预计剩余: 3109.7秒
2025-07-03 16:07:45,217 - [DEBUG] data_processing:debug:122 - ✅ 周线数据处理块 2/6 完成，处理 0 只股票
2025-07-03 16:07:45,217 - [DEBUG] data_processing:debug:122 - ✅ 周线数据处理块 1/6 完成，处理 0 只股票
2025-07-03 16:07:45,533 - [DEBUG] data_processing:debug:122 - ✅ 周线数据处理块 6/6 完成，处理 0 只股票
2025-07-03 16:07:45,616 - [DEBUG] data_processing:debug:122 - ✅ 周线数据处理块 3/6 完成，处理 0 只股票
2025-07-03 16:07:45,861 - [DEBUG] data_processing:debug:122 - 📈 块 4 已处理 900/903 只股票，耗时 622.6秒
2025-07-03 16:07:46,646 - [DEBUG] data_processing:debug:122 - 📈 块 5 已处理 900/903 只股票，耗时 623.4秒
2025-07-03 16:07:46,841 - [DEBUG] data_processing:debug:122 - ✅ 周线数据处理块 4/6 完成，处理 0 只股票
2025-07-03 16:07:47,538 - [INFO] data_processing:update:67 - 📊 周线数据处理: 6/6 (100.0%), 已用时: 624.3秒
2025-07-03 16:07:47,539 - [DEBUG] data_processing:debug:122 - ✅ 周线数据处理块 5/6 完成，处理 0 只股票
2025-07-03 16:07:47,540 - [INFO] data_processing:finish:76 - ✅ 周线数据处理完成: 6 项, 总耗时: 624.3秒
2025-07-03 16:07:47,541 - [DEBUG] data_processing:debug:122 - 🔴 退出函数 process_weekly_data_multithread
2025-07-03 17:58:02,974 - [DEBUG] data_processing:debug:122 - 🔵 进入函数 process_weekly_data_multithread(stock_count=5)
2025-07-03 17:58:03,116 - [INFO] data_processing:info:126 - 🔄 增量处理 开始 weekly_data_cleanup: 5 条数据
2025-07-03 17:58:03,177 - [INFO] data_processing:info:126 - 🔄 增量处理 完成 weekly_data_cleanup: 0 条数据
2025-07-03 17:58:03,319 - [INFO] data_processing:info:126 - 📊 增量模式：将 5 只股票分成 5 个块进行处理
2025-07-03 17:58:03,322 - [DEBUG] data_processing:debug:122 - 🔄 块 1 开始处理 1 只股票的周线数据
2025-07-03 17:58:03,323 - [DEBUG] data_processing:debug:122 - 🔄 块 2 开始处理 1 只股票的周线数据
2025-07-03 17:58:03,324 - [DEBUG] data_processing:debug:122 - 🔄 块 3 开始处理 1 只股票的周线数据
2025-07-03 17:58:03,325 - [DEBUG] data_processing:debug:122 - 🔄 块 4 开始处理 1 只股票的周线数据
2025-07-03 17:58:03,326 - [DEBUG] data_processing:debug:122 - 🔄 块 5 开始处理 1 只股票的周线数据
2025-07-03 17:58:03,608 - [INFO] data_processing:update:67 - 📊 周线数据处理: 1/5 (20.0%), 已用时: 0.3秒, 预计剩余: 1.1秒
2025-07-03 17:58:03,608 - [DEBUG] data_processing:debug:122 - ✅ 周线数据处理块 1/5 完成，处理 0 只股票
2025-07-03 17:58:03,741 - [DEBUG] data_processing:debug:122 - ✅ 周线数据处理块 4/5 完成，处理 0 只股票
2025-07-03 17:58:04,061 - [DEBUG] data_processing:debug:122 - ✅ 周线数据处理块 3/5 完成，处理 0 只股票
2025-07-03 17:58:04,122 - [DEBUG] data_processing:debug:122 - ✅ 周线数据处理块 5/5 完成，处理 0 只股票
2025-07-03 17:58:04,518 - [INFO] data_processing:update:67 - 📊 周线数据处理: 5/5 (100.0%), 已用时: 1.2秒
2025-07-03 17:58:04,519 - [DEBUG] data_processing:debug:122 - ✅ 周线数据处理块 2/5 完成，处理 0 只股票
2025-07-03 17:58:04,521 - [INFO] data_processing:finish:76 - ✅ 周线数据处理完成: 5 项, 总耗时: 1.2秒
2025-07-03 17:58:04,521 - [DEBUG] data_processing:debug:122 - 🔴 退出函数 process_weekly_data_multithread
