2025-07-03 15:49:57,477 - [DEBUG] ma_support:debug:122 - 🔵 进入函数 calculate_moving_average_multithread(stock_count=5)
2025-07-03 15:49:57,480 - [INFO] ma_support:info:126 - 📊 将 5 只股票分成 5 个块进行移动平均线计算
2025-07-03 15:49:57,504 - [DEBUG] ma_support:debug:122 - 📈 块 1 开始计算 1 只股票的移动平均线
2025-07-03 15:49:57,514 - [DEBUG] ma_support:debug:122 - 📈 块 2 开始计算 1 只股票的移动平均线
2025-07-03 15:49:57,520 - [DEBUG] ma_support:debug:122 - 📈 块 3 开始计算 1 只股票的移动平均线
2025-07-03 15:49:57,522 - [DEBUG] ma_support:debug:122 - 📈 块 4 开始计算 1 只股票的移动平均线
2025-07-03 15:49:57,525 - [DEBUG] ma_support:debug:122 - 📈 块 5 开始计算 1 只股票的移动平均线
2025-07-03 15:49:58,376 - [ERROR] ma_support:error:140 - ❌ 计算移动平均线块 2 失败
异常详情: ProgressLogger.update() takes from 1 to 2 positional arguments but 3 were given
堆栈跟踪:
Traceback (most recent call last):
  File "D:\IDE\cursor\sharewin\sw_py\main.py", line 1080, in calculate_moving_average_multithread
    progress_logger.update(1, f"块{chunk_id + 1}完成({processed_count}只股票)")
TypeError: ProgressLogger.update() takes from 1 to 2 positional arguments but 3 were given

2025-07-03 15:49:58,527 - [ERROR] ma_support:error:140 - ❌ 计算移动平均线块 1 失败
异常详情: ProgressLogger.update() takes from 1 to 2 positional arguments but 3 were given
堆栈跟踪:
Traceback (most recent call last):
  File "D:\IDE\cursor\sharewin\sw_py\main.py", line 1080, in calculate_moving_average_multithread
    progress_logger.update(1, f"块{chunk_id + 1}完成({processed_count}只股票)")
TypeError: ProgressLogger.update() takes from 1 to 2 positional arguments but 3 were given

2025-07-03 15:49:58,686 - [ERROR] ma_support:error:140 - ❌ 计算移动平均线块 4 失败
异常详情: ProgressLogger.update() takes from 1 to 2 positional arguments but 3 were given
堆栈跟踪:
Traceback (most recent call last):
  File "D:\IDE\cursor\sharewin\sw_py\main.py", line 1080, in calculate_moving_average_multithread
    progress_logger.update(1, f"块{chunk_id + 1}完成({processed_count}只股票)")
TypeError: ProgressLogger.update() takes from 1 to 2 positional arguments but 3 were given

2025-07-03 15:49:58,843 - [ERROR] ma_support:error:140 - ❌ 计算移动平均线块 5 失败
异常详情: ProgressLogger.update() takes from 1 to 2 positional arguments but 3 were given
堆栈跟踪:
Traceback (most recent call last):
  File "D:\IDE\cursor\sharewin\sw_py\main.py", line 1080, in calculate_moving_average_multithread
    progress_logger.update(1, f"块{chunk_id + 1}完成({processed_count}只股票)")
TypeError: ProgressLogger.update() takes from 1 to 2 positional arguments but 3 were given

2025-07-03 15:49:58,975 - [ERROR] ma_support:error:140 - ❌ 计算移动平均线块 3 失败
异常详情: ProgressLogger.update() takes from 1 to 2 positional arguments but 3 were given
堆栈跟踪:
Traceback (most recent call last):
  File "D:\IDE\cursor\sharewin\sw_py\main.py", line 1080, in calculate_moving_average_multithread
    progress_logger.update(1, f"块{chunk_id + 1}完成({processed_count}只股票)")
TypeError: ProgressLogger.update() takes from 1 to 2 positional arguments but 3 were given

2025-07-03 15:49:58,981 - [INFO] ma_support:finish:76 - ✅ 移动平均线计算完成: 5 项, 总耗时: 1.5秒
2025-07-03 15:49:58,983 - [DEBUG] ma_support:debug:122 - 🔴 退出函数 calculate_moving_average_multithread
2025-07-03 15:51:53,613 - [DEBUG] ma_support:debug:122 - 🔵 进入函数 calculate_moving_average_multithread(stock_count=5)
2025-07-03 15:51:53,614 - [INFO] ma_support:info:126 - 📊 将 5 只股票分成 5 个块进行移动平均线计算
2025-07-03 15:51:53,624 - [DEBUG] ma_support:debug:122 - 📈 块 1 开始计算 1 只股票的移动平均线
2025-07-03 15:51:53,628 - [DEBUG] ma_support:debug:122 - 📈 块 2 开始计算 1 只股票的移动平均线
2025-07-03 15:51:53,631 - [DEBUG] ma_support:debug:122 - 📈 块 3 开始计算 1 只股票的移动平均线
2025-07-03 15:51:53,635 - [DEBUG] ma_support:debug:122 - 📈 块 4 开始计算 1 只股票的移动平均线
2025-07-03 15:51:53,637 - [DEBUG] ma_support:debug:122 - 📈 块 5 开始计算 1 只股票的移动平均线
2025-07-03 15:51:54,611 - [ERROR] ma_support:error:140 - ❌ 计算移动平均线块 1 失败
异常详情: ProgressLogger.update() takes from 1 to 2 positional arguments but 3 were given
堆栈跟踪:
Traceback (most recent call last):
  File "D:\IDE\cursor\sharewin\sw_py\main.py", line 1080, in calculate_moving_average_multithread
    progress_logger.update(1, f"块{chunk_id + 1}完成({processed_count}只股票)")
TypeError: ProgressLogger.update() takes from 1 to 2 positional arguments but 3 were given

2025-07-03 15:51:54,754 - [ERROR] ma_support:error:140 - ❌ 计算移动平均线块 2 失败
异常详情: ProgressLogger.update() takes from 1 to 2 positional arguments but 3 were given
堆栈跟踪:
Traceback (most recent call last):
  File "D:\IDE\cursor\sharewin\sw_py\main.py", line 1080, in calculate_moving_average_multithread
    progress_logger.update(1, f"块{chunk_id + 1}完成({processed_count}只股票)")
TypeError: ProgressLogger.update() takes from 1 to 2 positional arguments but 3 were given

2025-07-03 15:51:54,904 - [ERROR] ma_support:error:140 - ❌ 计算移动平均线块 3 失败
异常详情: ProgressLogger.update() takes from 1 to 2 positional arguments but 3 were given
堆栈跟踪:
Traceback (most recent call last):
  File "D:\IDE\cursor\sharewin\sw_py\main.py", line 1080, in calculate_moving_average_multithread
    progress_logger.update(1, f"块{chunk_id + 1}完成({processed_count}只股票)")
TypeError: ProgressLogger.update() takes from 1 to 2 positional arguments but 3 were given

2025-07-03 15:51:55,047 - [ERROR] ma_support:error:140 - ❌ 计算移动平均线块 4 失败
异常详情: ProgressLogger.update() takes from 1 to 2 positional arguments but 3 were given
堆栈跟踪:
Traceback (most recent call last):
  File "D:\IDE\cursor\sharewin\sw_py\main.py", line 1080, in calculate_moving_average_multithread
    progress_logger.update(1, f"块{chunk_id + 1}完成({processed_count}只股票)")
TypeError: ProgressLogger.update() takes from 1 to 2 positional arguments but 3 were given

2025-07-03 15:51:55,196 - [ERROR] ma_support:error:140 - ❌ 计算移动平均线块 5 失败
异常详情: ProgressLogger.update() takes from 1 to 2 positional arguments but 3 were given
堆栈跟踪:
Traceback (most recent call last):
  File "D:\IDE\cursor\sharewin\sw_py\main.py", line 1080, in calculate_moving_average_multithread
    progress_logger.update(1, f"块{chunk_id + 1}完成({processed_count}只股票)")
TypeError: ProgressLogger.update() takes from 1 to 2 positional arguments but 3 were given

2025-07-03 15:51:55,200 - [INFO] ma_support:finish:76 - ✅ 移动平均线计算完成: 5 项, 总耗时: 1.6秒
2025-07-03 15:51:55,203 - [DEBUG] ma_support:debug:122 - 🔴 退出函数 calculate_moving_average_multithread
2025-07-03 15:53:58,274 - [DEBUG] ma_support:debug:122 - 🔵 进入函数 calculate_moving_average_multithread(stock_count=5)
2025-07-03 15:53:58,276 - [INFO] ma_support:info:126 - 📊 将 5 只股票分成 5 个块进行移动平均线计算
2025-07-03 15:53:58,280 - [DEBUG] ma_support:debug:122 - 📈 块 1 开始计算 1 只股票的移动平均线
2025-07-03 15:53:58,283 - [DEBUG] ma_support:debug:122 - 📈 块 2 开始计算 1 只股票的移动平均线
2025-07-03 15:53:58,284 - [DEBUG] ma_support:debug:122 - 📈 块 3 开始计算 1 只股票的移动平均线
2025-07-03 15:53:58,285 - [DEBUG] ma_support:debug:122 - 📈 块 4 开始计算 1 只股票的移动平均线
2025-07-03 15:53:58,286 - [DEBUG] ma_support:debug:122 - 📈 块 5 开始计算 1 只股票的移动平均线
2025-07-03 15:53:58,947 - [INFO] ma_support:update:67 - 📊 移动平均线计算: 1/5 (20.0%), 已用时: 0.7秒, 预计剩余: 2.7秒
2025-07-03 15:53:58,953 - [DEBUG] ma_support:debug:122 - ✅ 移动平均线计算块 1/5 完成，处理 1 只股票
2025-07-03 15:53:59,139 - [DEBUG] ma_support:debug:122 - ✅ 移动平均线计算块 2/5 完成，处理 1 只股票
2025-07-03 15:53:59,265 - [DEBUG] ma_support:debug:122 - ✅ 移动平均线计算块 3/5 完成，处理 1 只股票
2025-07-03 15:53:59,388 - [DEBUG] ma_support:debug:122 - ✅ 移动平均线计算块 4/5 完成，处理 1 只股票
2025-07-03 15:53:59,536 - [INFO] ma_support:update:67 - 📊 移动平均线计算: 5/5 (100.0%), 已用时: 1.3秒
2025-07-03 15:53:59,541 - [DEBUG] ma_support:debug:122 - ✅ 移动平均线计算块 5/5 完成，处理 1 只股票
2025-07-03 15:53:59,544 - [INFO] ma_support:finish:76 - ✅ 移动平均线计算完成: 5 项, 总耗时: 1.3秒
2025-07-03 15:53:59,544 - [DEBUG] ma_support:debug:122 - 🔴 退出函数 calculate_moving_average_multithread
2025-07-03 16:12:42,322 - [DEBUG] ma_support:debug:122 - 🔵 进入函数 calculate_moving_average_multithread(stock_count=5418)
2025-07-03 16:12:42,322 - [INFO] ma_support:info:126 - 📊 将 5418 只股票分成 6 个块进行移动平均线计算
2025-07-03 16:12:42,323 - [DEBUG] ma_support:debug:122 - 📈 块 1 开始计算 903 只股票的移动平均线
2025-07-03 16:12:42,324 - [DEBUG] ma_support:debug:122 - 📈 块 2 开始计算 903 只股票的移动平均线
2025-07-03 16:12:42,325 - [DEBUG] ma_support:debug:122 - 📈 块 3 开始计算 903 只股票的移动平均线
2025-07-03 16:12:42,325 - [DEBUG] ma_support:debug:122 - 📈 块 4 开始计算 903 只股票的移动平均线
2025-07-03 16:12:42,326 - [DEBUG] ma_support:debug:122 - 📈 块 5 开始计算 903 只股票的移动平均线
2025-07-03 16:12:42,327 - [DEBUG] ma_support:debug:122 - 📈 块 6 开始计算 903 只股票的移动平均线
2025-07-03 16:13:31,467 - [DEBUG] ma_support:debug:122 - 🔢 块 2 已计算 100/903 只股票，耗时 49.1秒
2025-07-03 16:13:31,643 - [DEBUG] ma_support:debug:122 - 🔢 块 5 已计算 100/903 只股票，耗时 49.3秒
2025-07-03 16:13:31,940 - [DEBUG] ma_support:debug:122 - 🔢 块 4 已计算 100/903 只股票，耗时 49.6秒
2025-07-03 16:13:32,203 - [DEBUG] ma_support:debug:122 - 🔢 块 1 已计算 100/903 只股票，耗时 49.9秒
2025-07-03 16:13:32,214 - [DEBUG] ma_support:debug:122 - 🔢 块 3 已计算 100/903 只股票，耗时 49.9秒
2025-07-03 16:13:33,369 - [DEBUG] ma_support:debug:122 - 🔢 块 6 已计算 100/903 只股票，耗时 51.0秒
2025-07-03 16:13:37,163 - [INFO] ma_support:info:126 - 🗄️ 批量插入移动平均线数据 stock_moving_average: 40,960 条记录
2025-07-03 16:13:42,563 - [INFO] ma_support:info:126 - 🗄️ 批量插入移动平均线数据 stock_moving_average: 41,000 条记录
2025-07-03 16:13:48,028 - [INFO] ma_support:info:126 - 🗄️ 批量插入移动平均线数据 stock_moving_average: 39,570 条记录
2025-07-03 16:13:54,396 - [INFO] ma_support:info:126 - 🗄️ 批量插入移动平均线数据 stock_moving_average: 40,900 条记录
2025-07-03 16:14:01,981 - [INFO] ma_support:info:126 - 🗄️ 批量插入移动平均线数据 stock_moving_average: 40,980 条记录
2025-07-03 16:14:10,337 - [INFO] ma_support:info:126 - 🗄️ 批量插入移动平均线数据 stock_moving_average: 40,980 条记录
2025-07-03 16:14:13,206 - [DEBUG] ma_support:debug:122 - 🔢 块 2 已计算 200/903 只股票，耗时 90.9秒
2025-07-03 16:14:21,110 - [DEBUG] ma_support:debug:122 - 🔢 块 5 已计算 200/903 只股票，耗时 98.8秒
2025-07-03 16:14:21,887 - [INFO] ma_support:info:126 - 🗄️ 批量插入移动平均线数据 stock_moving_average: 40,980 条记录
2025-07-03 16:14:29,867 - [INFO] ma_support:info:126 - 🗄️ 批量插入移动平均线数据 stock_moving_average: 40,920 条记录
2025-07-03 16:14:30,287 - [DEBUG] ma_support:debug:122 - 🔢 块 4 已计算 200/903 只股票，耗时 108.0秒
2025-07-03 16:14:36,706 - [INFO] ma_support:info:126 - 🗄️ 批量插入移动平均线数据 stock_moving_average: 31,540 条记录
2025-07-03 16:14:39,975 - [DEBUG] ma_support:debug:122 - 🔢 块 1 已计算 200/903 只股票，耗时 117.7秒
2025-07-03 16:14:48,188 - [INFO] ma_support:info:126 - 🗄️ 批量插入移动平均线数据 stock_moving_average: 40,860 条记录
2025-07-03 16:14:48,899 - [DEBUG] ma_support:debug:122 - 🔢 块 3 已计算 200/903 只股票，耗时 126.6秒
2025-07-03 16:14:57,001 - [INFO] ma_support:info:126 - 🗄️ 批量插入移动平均线数据 stock_moving_average: 40,940 条记录
2025-07-03 16:14:58,687 - [DEBUG] ma_support:debug:122 - 🔢 块 6 已计算 200/903 只股票，耗时 136.4秒
2025-07-03 16:15:07,369 - [INFO] ma_support:info:126 - 🗄️ 批量插入移动平均线数据 stock_moving_average: 40,780 条记录
2025-07-03 16:15:09,019 - [DEBUG] ma_support:debug:122 - 🔢 块 2 已计算 300/903 只股票，耗时 146.7秒
2025-07-03 16:15:17,638 - [DEBUG] ma_support:debug:122 - 🔢 块 5 已计算 300/903 只股票，耗时 155.3秒
2025-07-03 16:15:18,092 - [INFO] ma_support:info:126 - 🗄️ 批量插入移动平均线数据 stock_moving_average: 40,820 条记录
2025-07-03 16:15:25,269 - [DEBUG] ma_support:debug:122 - 🔢 块 4 已计算 300/903 只股票，耗时 162.9秒
2025-07-03 16:15:27,088 - [INFO] ma_support:info:126 - 🗄️ 批量插入移动平均线数据 stock_moving_average: 40,140 条记录
2025-07-03 16:15:35,979 - [INFO] ma_support:info:126 - 🗄️ 批量插入移动平均线数据 stock_moving_average: 40,940 条记录
2025-07-03 16:15:38,826 - [DEBUG] ma_support:debug:122 - 🔢 块 1 已计算 300/903 只股票，耗时 176.5秒
2025-07-03 16:15:47,603 - [INFO] ma_support:info:126 - 🗄️ 批量插入移动平均线数据 stock_moving_average: 40,980 条记录
2025-07-03 16:15:48,651 - [DEBUG] ma_support:debug:122 - 🔢 块 3 已计算 300/903 只股票，耗时 186.3秒
2025-07-03 16:15:57,856 - [DEBUG] ma_support:debug:122 - 🔢 块 6 已计算 300/903 只股票，耗时 195.5秒
2025-07-03 16:15:58,084 - [INFO] ma_support:info:126 - 🗄️ 批量插入移动平均线数据 stock_moving_average: 41,000 条记录
2025-07-03 16:16:06,916 - [INFO] ma_support:info:126 - 🗄️ 批量插入移动平均线数据 stock_moving_average: 40,920 条记录
2025-07-03 16:16:09,448 - [DEBUG] ma_support:debug:122 - 🔢 块 2 已计算 400/903 只股票，耗时 207.1秒
2025-07-03 16:16:17,469 - [INFO] ma_support:info:126 - 🗄️ 批量插入移动平均线数据 stock_moving_average: 40,920 条记录
2025-07-03 16:16:17,786 - [DEBUG] ma_support:debug:122 - 🔢 块 5 已计算 400/903 只股票，耗时 215.5秒
2025-07-03 16:16:25,420 - [DEBUG] ma_support:debug:122 - 🔢 块 4 已计算 400/903 只股票，耗时 223.1秒
2025-07-03 16:16:26,010 - [INFO] ma_support:info:126 - 🗄️ 批量插入移动平均线数据 stock_moving_average: 39,650 条记录
2025-07-03 16:16:34,527 - [INFO] ma_support:info:126 - 🗄️ 批量插入移动平均线数据 stock_moving_average: 40,580 条记录
2025-07-03 16:16:36,681 - [DEBUG] ma_support:debug:122 - 🔢 块 1 已计算 400/903 只股票，耗时 234.4秒
2025-07-03 16:16:45,584 - [INFO] ma_support:info:126 - 🗄️ 批量插入移动平均线数据 stock_moving_average: 40,900 条记录
2025-07-03 16:16:47,441 - [DEBUG] ma_support:debug:122 - 🔢 块 3 已计算 400/903 只股票，耗时 245.1秒
2025-07-03 16:16:56,039 - [DEBUG] ma_support:debug:122 - 🔢 块 6 已计算 400/903 只股票，耗时 253.7秒
2025-07-03 16:16:56,044 - [INFO] ma_support:info:126 - 🗄️ 批量插入移动平均线数据 stock_moving_average: 40,960 条记录
2025-07-03 16:17:05,183 - [INFO] ma_support:info:126 - 🗄️ 批量插入移动平均线数据 stock_moving_average: 40,860 条记录
2025-07-03 16:17:07,536 - [DEBUG] ma_support:debug:122 - 🔢 块 2 已计算 500/903 只股票，耗时 265.2秒
2025-07-03 16:17:17,298 - [INFO] ma_support:info:126 - 🗄️ 批量插入移动平均线数据 stock_moving_average: 41,000 条记录
2025-07-03 16:17:17,760 - [DEBUG] ma_support:debug:122 - 🔢 块 5 已计算 500/903 只股票，耗时 275.4秒
2025-07-03 16:17:25,895 - [INFO] ma_support:info:126 - 🗄️ 批量插入移动平均线数据 stock_moving_average: 38,460 条记录
2025-07-03 16:17:26,400 - [DEBUG] ma_support:debug:122 - 🔢 块 4 已计算 500/903 只股票，耗时 284.1秒
2025-07-03 16:17:36,729 - [INFO] ma_support:info:126 - 🗄️ 批量插入移动平均线数据 stock_moving_average: 40,960 条记录
2025-07-03 16:17:39,696 - [DEBUG] ma_support:debug:122 - 🔢 块 1 已计算 500/903 只股票，耗时 297.4秒
2025-07-03 16:17:47,616 - [INFO] ma_support:info:126 - 🗄️ 批量插入移动平均线数据 stock_moving_average: 39,940 条记录
2025-07-03 16:17:49,433 - [DEBUG] ma_support:debug:122 - 🔢 块 3 已计算 500/903 只股票，耗时 307.1秒
2025-07-03 16:17:58,672 - [DEBUG] ma_support:debug:122 - 🔢 块 6 已计算 500/903 只股票，耗时 316.3秒
2025-07-03 16:17:58,907 - [INFO] ma_support:info:126 - 🗄️ 批量插入移动平均线数据 stock_moving_average: 40,940 条记录
2025-07-03 16:18:06,857 - [INFO] ma_support:info:126 - 🗄️ 批量插入移动平均线数据 stock_moving_average: 39,730 条记录
2025-07-03 16:18:08,465 - [DEBUG] ma_support:debug:122 - 🔢 块 2 已计算 600/903 只股票，耗时 326.1秒
2025-07-03 16:18:16,442 - [DEBUG] ma_support:debug:122 - 🔢 块 5 已计算 600/903 只股票，耗时 334.1秒
2025-07-03 16:18:16,742 - [INFO] ma_support:info:126 - 🗄️ 批量插入移动平均线数据 stock_moving_average: 40,980 条记录
2025-07-03 16:18:25,401 - [INFO] ma_support:info:126 - 🗄️ 批量插入移动平均线数据 stock_moving_average: 39,770 条记录
2025-07-03 16:18:25,787 - [DEBUG] ma_support:debug:122 - 🔢 块 4 已计算 600/903 只股票，耗时 343.5秒
2025-07-03 16:18:34,073 - [INFO] ma_support:info:126 - 🗄️ 批量插入移动平均线数据 stock_moving_average: 40,900 条记录
2025-07-03 16:18:36,954 - [DEBUG] ma_support:debug:122 - 🔢 块 1 已计算 600/903 只股票，耗时 354.6秒
2025-07-03 16:18:45,457 - [INFO] ma_support:info:126 - 🗄️ 批量插入移动平均线数据 stock_moving_average: 38,930 条记录
2025-07-03 16:18:47,641 - [DEBUG] ma_support:debug:122 - 🔢 块 3 已计算 600/903 只股票，耗时 365.3秒
2025-07-03 16:18:56,076 - [DEBUG] ma_support:debug:122 - 🔢 块 6 已计算 600/903 只股票，耗时 373.7秒
2025-07-03 16:18:56,387 - [INFO] ma_support:info:126 - 🗄️ 批量插入移动平均线数据 stock_moving_average: 40,980 条记录
2025-07-03 16:19:05,290 - [INFO] ma_support:info:126 - 🗄️ 批量插入移动平均线数据 stock_moving_average: 39,980 条记录
2025-07-03 16:19:07,133 - [DEBUG] ma_support:debug:122 - 🔢 块 2 已计算 700/903 只股票，耗时 384.8秒
2025-07-03 16:19:16,086 - [DEBUG] ma_support:debug:122 - 🔢 块 5 已计算 700/903 只股票，耗时 393.8秒
2025-07-03 16:19:17,001 - [INFO] ma_support:info:126 - 🗄️ 批量插入移动平均线数据 stock_moving_average: 40,860 条记录
2025-07-03 16:19:24,322 - [DEBUG] ma_support:debug:122 - 🔢 块 4 已计算 700/903 只股票，耗时 402.0秒
2025-07-03 16:19:25,622 - [INFO] ma_support:info:126 - 🗄️ 批量插入移动平均线数据 stock_moving_average: 40,980 条记录
2025-07-03 16:19:34,404 - [INFO] ma_support:info:126 - 🗄️ 批量插入移动平均线数据 stock_moving_average: 40,980 条记录
2025-07-03 16:19:35,726 - [DEBUG] ma_support:debug:122 - 🔢 块 1 已计算 700/903 只股票，耗时 413.4秒
2025-07-03 16:19:44,447 - [INFO] ma_support:info:126 - 🗄️ 批量插入移动平均线数据 stock_moving_average: 40,980 条记录
2025-07-03 16:19:46,544 - [DEBUG] ma_support:debug:122 - 🔢 块 3 已计算 700/903 只股票，耗时 424.2秒
2025-07-03 16:19:54,260 - [DEBUG] ma_support:debug:122 - 🔢 块 6 已计算 700/903 只股票，耗时 431.9秒
2025-07-03 16:19:54,773 - [INFO] ma_support:info:126 - 🗄️ 批量插入移动平均线数据 stock_moving_average: 41,000 条记录
2025-07-03 16:20:02,935 - [INFO] ma_support:info:126 - 🗄️ 批量插入移动平均线数据 stock_moving_average: 38,240 条记录
2025-07-03 16:20:07,727 - [DEBUG] ma_support:debug:122 - 🔢 块 2 已计算 800/903 只股票，耗时 445.4秒
2025-07-03 16:20:16,033 - [DEBUG] ma_support:debug:122 - 🔢 块 5 已计算 800/903 只股票，耗时 453.7秒
2025-07-03 16:20:16,379 - [INFO] ma_support:info:126 - 🗄️ 批量插入移动平均线数据 stock_moving_average: 40,980 条记录
2025-07-03 16:20:24,732 - [INFO] ma_support:info:126 - 🗄️ 批量插入移动平均线数据 stock_moving_average: 40,940 条记录
2025-07-03 16:20:25,283 - [DEBUG] ma_support:debug:122 - 🔢 块 4 已计算 800/903 只股票，耗时 463.0秒
2025-07-03 16:20:34,986 - [INFO] ma_support:info:126 - 🗄️ 批量插入移动平均线数据 stock_moving_average: 40,960 条记录
2025-07-03 16:20:36,073 - [DEBUG] ma_support:debug:122 - 🔢 块 1 已计算 800/903 只股票，耗时 473.8秒
2025-07-03 16:20:45,569 - [INFO] ma_support:info:126 - 🗄️ 批量插入移动平均线数据 stock_moving_average: 40,900 条记录
2025-07-03 16:20:47,917 - [DEBUG] ma_support:debug:122 - 🔢 块 3 已计算 800/903 只股票，耗时 485.6秒
2025-07-03 16:20:56,722 - [DEBUG] ma_support:debug:122 - 🔢 块 6 已计算 800/903 只股票，耗时 494.4秒
2025-07-03 16:20:57,380 - [INFO] ma_support:info:126 - 🗄️ 批量插入移动平均线数据 stock_moving_average: 40,590 条记录
2025-07-03 16:21:09,219 - [INFO] ma_support:info:126 - 🗄️ 批量插入移动平均线数据 stock_moving_average: 40,980 条记录
2025-07-03 16:21:14,549 - [DEBUG] ma_support:debug:122 - 🔢 块 2 已计算 900/903 只股票，耗时 512.2秒
2025-07-03 16:21:25,554 - [INFO] ma_support:info:126 - 🗄️ 批量插入移动平均线数据 stock_moving_average: 40,920 条记录
2025-07-03 16:21:28,287 - [INFO] ma_support:update:67 - 📊 移动平均线计算: 1/6 (16.7%), 已用时: 526.0秒, 预计剩余: 2629.8秒
2025-07-03 16:21:28,288 - [DEBUG] ma_support:debug:122 - ✅ 移动平均线计算块 2/6 完成，处理 903 只股票
2025-07-03 16:21:29,581 - [DEBUG] ma_support:debug:122 - 🔢 块 5 已计算 900/903 只股票，耗时 527.3秒
2025-07-03 16:21:38,443 - [DEBUG] ma_support:debug:122 - 🔢 块 4 已计算 900/903 只股票，耗时 536.1秒
2025-07-03 16:21:38,921 - [INFO] ma_support:info:126 - 🗄️ 批量插入移动平均线数据 stock_moving_average: 40,920 条记录
2025-07-03 16:21:47,237 - [DEBUG] ma_support:debug:122 - 🔢 块 1 已计算 900/903 只股票，耗时 544.9秒
2025-07-03 16:21:47,330 - [INFO] ma_support:info:126 - 🗄️ 批量插入移动平均线数据 stock_moving_average: 40,940 条记录
2025-07-03 16:21:47,807 - [INFO] ma_support:update:67 - 📊 移动平均线计算: 2/6 (33.3%), 已用时: 545.5秒, 预计剩余: 1091.0秒
2025-07-03 16:21:47,808 - [DEBUG] ma_support:debug:122 - ✅ 移动平均线计算块 5/6 完成，处理 889 只股票
2025-07-03 16:21:52,458 - [DEBUG] ma_support:debug:122 - 🔢 块 3 已计算 900/903 只股票，耗时 550.1秒
2025-07-03 16:21:54,309 - [INFO] ma_support:info:126 - 🗄️ 批量插入移动平均线数据 stock_moving_average: 40,800 条记录
2025-07-03 16:21:54,600 - [INFO] ma_support:update:67 - 📊 移动平均线计算: 3/6 (50.0%), 已用时: 552.3秒, 预计剩余: 552.3秒
2025-07-03 16:21:54,602 - [DEBUG] ma_support:debug:122 - ✅ 移动平均线计算块 4/6 完成，处理 880 只股票
2025-07-03 16:21:56,603 - [DEBUG] ma_support:debug:122 - 🔢 块 6 已计算 900/903 只股票，耗时 554.3秒
2025-07-03 16:22:00,120 - [INFO] ma_support:info:126 - 🗄️ 批量插入移动平均线数据 stock_moving_average: 40,510 条记录
2025-07-03 16:22:00,425 - [INFO] ma_support:update:67 - 📊 移动平均线计算: 4/6 (66.7%), 已用时: 558.1秒, 预计剩余: 279.1秒
2025-07-03 16:22:00,426 - [DEBUG] ma_support:debug:122 - ✅ 移动平均线计算块 1/6 完成，处理 896 只股票
2025-07-03 16:22:05,196 - [INFO] ma_support:info:126 - 🗄️ 批量插入移动平均线数据 stock_moving_average: 35,770 条记录
2025-07-03 16:22:05,482 - [INFO] ma_support:update:67 - 📊 移动平均线计算: 5/6 (83.3%), 已用时: 563.2秒, 预计剩余: 112.6秒
2025-07-03 16:22:05,483 - [DEBUG] ma_support:debug:122 - ✅ 移动平均线计算块 3/6 完成，处理 901 只股票
2025-07-03 16:22:06,114 - [INFO] ma_support:update:67 - 📊 移动平均线计算: 6/6 (100.0%), 已用时: 563.8秒
2025-07-03 16:22:06,115 - [DEBUG] ma_support:debug:122 - ✅ 移动平均线计算块 6/6 完成，处理 881 只股票
2025-07-03 16:22:06,116 - [INFO] ma_support:finish:76 - ✅ 移动平均线计算完成: 6 项, 总耗时: 563.8秒
2025-07-03 16:22:06,117 - [DEBUG] ma_support:debug:122 - 🔴 退出函数 calculate_moving_average_multithread
