#!/usr/bin/env python3
"""
股票技术分析主程序 - v2.1
实现GP_ANALYSE_PKG.main的Python版本，修复所有日志和性能问题
优化数据库并发处理以避免死锁
"""

import sys
import os
import logging
import time
import argparse
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List, Dict, Any, Optional
import threading
import random
import pymysql

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    # 尝试sw_py前缀的导入
    try:
        from sw_py.config.database_config import MYSQL_CONFIG
        from sw_py.config.analysis_config import (
            U_PATTERN_CONFIG, V_PATTERN_CONFIG, VOLUME_CONFIG,
            THREADING_CONFIG, DATA_FILTER_CONFIG, DEBUG_CONFIG, EXECUTION_CONFIG,
            MOVING_AVERAGE_CONFIG, VOLATILITY_CONFIG
        )
        from sw_py.config.incremental_config import INCREMENTAL_CONFIG
        from sw_py.utils.incremental_manager import IncrementalDataManager
        from sw_py.config.export_config import EXPORT_CONFIG
        from sw_py.config.logging_config import LOGGING_CONFIG
        from sw_py.utils.enhanced_logger import get_enhanced_logger, EnhancedLogger

        # 导入邮件配置
        try:
            from sw_py.config.email_config import EMAIL_CONFIG
        except ImportError:
            print("注意：未找到 sw_py/config/email_config.py，邮件功能将被禁用")
            EMAIL_CONFIG = {'enable_email': False}

        from sw_py.models.database import DatabaseManager, StockDataModel
        from sw_py.utils.data_processor import DataProcessor
        from sw_py.analysis.pattern_analyzer import PatternAnalyzer
        from sw_py.utils.incremental_manager import IncrementalDataManager
    except ImportError:
        # 回退到相对导入
        from config.database_config import MYSQL_CONFIG
        from config.analysis_config import (
            U_PATTERN_CONFIG, V_PATTERN_CONFIG, VOLUME_CONFIG, 
            THREADING_CONFIG, DATA_FILTER_CONFIG, DEBUG_CONFIG, EXECUTION_CONFIG,
            MOVING_AVERAGE_CONFIG, VOLATILITY_CONFIG
        )
        from config.export_config import EXPORT_CONFIG
        from config.logging_config import LOGGING_CONFIG
        from utils.enhanced_logger import get_enhanced_logger, EnhancedLogger
        from config.incremental_config import INCREMENTAL_CONFIG
        
        # 导入邮件配置
        try:
            from config.email_config import EMAIL_CONFIG
        except ImportError:
            print("注意：未找到 config/email_config.py，邮件功能将被禁用")
            EMAIL_CONFIG = {'enable_email': False}
        
        from models.database import DatabaseManager, StockDataModel
        from utils.data_processor import DataProcessor
        from analysis.pattern_analyzer import PatternAnalyzer
        from utils.incremental_manager import IncrementalDataManager
except ImportError as e:
    print(f"导入模块失败: {e}")
    sys.exit(1)

# 全局锁，用于控制数据库写入顺序
db_write_lock = threading.Lock()

class StockAnalysisMain:
    """股票分析主程序"""
    
    def __init__(self):
        self.setup_logging()
        self.logger = logging.getLogger(__name__)

        # 初始化增强日志器
        self.enhanced_logger = get_enhanced_logger('main_process')
        self.data_logger = get_enhanced_logger('data_processing')
        self.ma_logger = get_enhanced_logger('ma_support')

        # 初始化组件
        self.db_manager = DatabaseManager()
        self.stock_model = StockDataModel(self.db_manager)
        self.data_processor = DataProcessor()
        self.pattern_analyzer = PatternAnalyzer(
            U_PATTERN_CONFIG, V_PATTERN_CONFIG, VOLUME_CONFIG
        )

        # 将stock_model传递给pattern_analyzer，用于CHECK_MA逻辑
        self.pattern_analyzer.stock_model = self.stock_model

        # 初始化增量数据管理器
        self.incremental_manager = IncrementalDataManager(self.db_manager, self.logger)

        self.batch_id = None
        self.connection = pymysql.connect(**MYSQL_CONFIG)
        
    def setup_logging(self):
        """设置日志"""
        log_dir = os.path.join(os.path.dirname(__file__), 'logs')
        if not os.path.exists(log_dir):
            os.makedirs(log_dir, exist_ok=True)
        
        log_file = os.path.join(log_dir, 'stock_analysis.log')
        
        # 优化日志级别设置 - 只有在明确的调试场景下才使用DEBUG级别
        test_single_stock = DEBUG_CONFIG.get('test_single_stock')
        enable_detailed_logging = DEBUG_CONFIG.get('enable_detailed_logging', False)
        
        # 只有在调试单只股票或明确启用详细日志时才使用DEBUG级别
        if test_single_stock:
            log_level = logging.DEBUG
            print(f"🔍 调试模式：启用详细日志输出 (调试股票: {test_single_stock})")
        elif enable_detailed_logging:
            log_level = logging.DEBUG
            print("🔍 调试模式：启用详细日志输出")
        else:
            log_level = logging.INFO
        
        # 创建自定义格式器，简化INFO级别日志格式
        class CustomFormatter(logging.Formatter):
            def format(self, record):
                if record.levelno == logging.INFO:
                    # INFO级别使用简化格式
                    return f"{record.getMessage()}"
                elif record.levelno >= logging.WARNING:
                    # WARNING、ERROR级别使用完整格式，确保错误信息详细
                    return f"{self.formatTime(record, '%Y-%m-%d %H:%M:%S')} - {record.levelname} - {record.getMessage()}"
                else:
                    # DEBUG级别使用完整格式
                    return f"{self.formatTime(record, '%Y-%m-%d %H:%M:%S')} - {record.name} - {record.levelname} - {record.getMessage()}"
        
        # 配置文件处理器
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)  # 文件中保留所有级别的日志
        file_handler.setFormatter(CustomFormatter())
        
        # 配置控制台处理器
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(log_level)  # 控制台只显示指定级别的日志
        console_handler.setFormatter(CustomFormatter())
        
        # 设置根日志器
        logging.basicConfig(
            level=logging.DEBUG,  # 根日志器保持DEBUG级别，由处理器控制输出
            handlers=[file_handler, console_handler],
            force=True  # 强制重新配置
        )
    
    def run(self, test_mode: bool = False, test_count: int = 10):
        """主函数"""
        start_time = time.time()
        mode_text = "测试模式" if test_mode else "生产模式"

        # 使用增强日志器记录程序开始
        self.enhanced_logger.info(f"🚀 === 股票技术分析程序开始（{mode_text}） ===")
        self.enhanced_logger.log_function_entry('run', test_mode=test_mode, test_count=test_count)

        try:
            # 获取股票列表
            stock_list = self.get_stock_list(test_mode, test_count)
            self.enhanced_logger.info(f"📊 获取股票列表完成: {len(stock_list)} 只股票")

            # 检查执行模式
            only_analysis = EXECUTION_CONFIG.get('only_analysis', False)

            if only_analysis:
                self.enhanced_logger.info("⚡ 执行模式：仅分析（跳过数据准备步骤）")

                # 确保分析结果表存在
                self.stock_model.create_analysis_result_table()

                # 获取批次ID
                self.batch_id = self.stock_model.get_next_batch_id()
                self.enhanced_logger.info(f"🔢 开始批次 {self.batch_id} 的分析")

                # 清空历史分析结果，只保留最新数据
                self.enhanced_logger.info("🧹 清空历史分析结果...")
                self.stock_model.clear_analysis_results()
                self.enhanced_logger.info("✅ 历史分析结果已清空")

                # 验证数据完整性（如果启用）
                if EXECUTION_CONFIG.get('validate_data_before_analysis', True):
                    self.enhanced_logger.info("🔍 验证数据完整性...")
                    self.validate_data_completeness(stock_list)
                    
            else:
                self.enhanced_logger.info("🔄 执行模式：完整流程")

                # 1. 初始化数据库表
                if not EXECUTION_CONFIG.get('skip_table_init', False):
                    self.enhanced_logger.info("📋 第1步：初始化数据库表...")
                    self.initialize_tables()
                else:
                    self.enhanced_logger.info("⏭️ 跳过第1步：数据库表初始化")
                    # 即使跳过表初始化，也要确保分析必需的表存在
                    self.stock_model.create_daily_data_table()  # 分析过程需要日线数据表
                    self.stock_model.create_analysis_result_table()

                # 获取批次ID（在表创建之后）
                self.batch_id = self.stock_model.get_next_batch_id()
                self.enhanced_logger.info(f"🔢 开始批次 {self.batch_id} 的分析")

                # 2. INSERT_JYW: 处理周线数据
                if not EXECUTION_CONFIG.get('skip_weekly_data', False):
                    self.enhanced_logger.info(f"📈 第2步：处理周线数据，使用 {THREADING_CONFIG.get('weekly_data_threads', 8)} 个线程...")
                    self.process_weekly_data_multithread(stock_list)
                else:
                    self.enhanced_logger.info("⏭️ 跳过第2步：周线数据处理")

                # 3. CALCUL_AVG_WQTY: 计算平均交易量
                if not EXECUTION_CONFIG.get('skip_avg_volume', False):
                    self.enhanced_logger.info(f"📊 第3步：计算平均交易量，使用 {THREADING_CONFIG.get('avg_volume_threads', 6)} 个线程...")
                    self.calculate_avg_trade_volume_multithread(stock_list)
                else:
                    self.enhanced_logger.info("⏭️ 跳过第3步：平均交易量计算")

                # 3.5. CALCULATION_AVG_JYM: 计算移动平均线
                if not EXECUTION_CONFIG.get('skip_moving_average', False):
                    self.enhanced_logger.info(f"📉 第3.5步：计算移动平均线，使用 {THREADING_CONFIG.get('moving_average_threads', 6)} 个线程...")
                    self.calculate_moving_average_multithread(stock_list)
                else:
                    self.enhanced_logger.info("⏭️ 跳过第3.5步：移动平均线计算")

                # 3.6. CALCU_FLUCT: 计算波动率
                if not EXECUTION_CONFIG.get('skip_volatility', False):
                    self.enhanced_logger.info(f"📊 第3.6步：计算波动率，使用 {THREADING_CONFIG.get('volatility_threads', 6)} 个线程...")
                    self.calculate_volatility_multithread(stock_list)
                else:
                    self.enhanced_logger.info("⏭️ 跳过第3.6步：波动率计算")
            
            # 4. MAIN_W: 执行形态分析
            self.logger.info("第4步：执行U型V型形态分析...")
            results = self.analyze_patterns_multithread(stock_list)
            
            # 5. 导出结果
            if not EXECUTION_CONFIG.get('skip_export', False):
                self.logger.info("第5步：导出结果到Excel...")
                excel_filepath = self.export_results(results)
            else:
                self.logger.info("跳过第5步：Excel导出")
                excel_filepath = None
            
            # 6. 发送邮件通知
            if not EXECUTION_CONFIG.get('skip_email', False):
                self.logger.info("第6步：发送邮件通知...")
                self.send_notification(excel_filepath, len(stock_list), len(results))
            else:
                self.logger.info("跳过第6步：邮件通知")
            
            elapsed_time = time.time() - start_time
            self.logger.info(f"=== 程序完成，耗时 {elapsed_time:.2f} 秒 ===")
            self.logger.info(f"分析股票总数：{len(stock_list)}")
            self.logger.info(f"发现形态数量：{len(results)}")

            # 输出筛选统计分析
            if hasattr(self.pattern_analyzer, 'print_filter_statistics'):
                self.pattern_analyzer.print_filter_statistics()

            # 记录增量处理性能统计
            self.incremental_manager.log_performance_stats()

            # 标记运行完成（用于下次增量处理检测）
            self.incremental_manager.mark_run_complete()
            
        except Exception as e:
            self.logger.error(f"程序执行失败: {e}")
            import traceback
            self.logger.error(f"详细错误信息: {traceback.format_exc()}")
            raise
    
    def initialize_tables(self):
        """初始化数据库表"""
        self.stock_model.create_daily_data_table()
        self.stock_model.create_weekly_data_table()
        self.stock_model.create_avg_trade_table()
        self.stock_model.create_moving_average_table()
        self.stock_model.create_volatility_table()
        self.stock_model.create_analysis_result_table()
        
        # 清空分析结果表，只保留最新的分析数据
        self.logger.info("清空历史分析结果...")
        self.stock_model.clear_analysis_results()
        self.logger.info("✅ 历史分析结果已清空")
    
    def validate_data_completeness(self, stock_list: List[Dict[str, Any]]):
        """验证数据完整性 - 当跳过数据准备步骤时调用"""
        self.logger.info("正在验证周线数据和平均交易量数据完整性...")
        
        missing_weekly_data = []
        missing_avg_data = []
        valid_stocks = []
        
        # 检查前100只股票的数据完整性（避免全量检查耗时过长）
        sample_stocks = stock_list[:min(100, len(stock_list))]
        
        for stock in sample_stocks:
            stock_code = stock['gp_num']
            
            # 检查周线数据
            weekly_data = self.stock_model.get_weekly_data(stock_code)
            weekly_count = len(weekly_data)
            
            # 检查平均交易量数据
            avg_data = self.stock_model.get_avg_trade_data(stock_code)
            avg_count = len(avg_data)
            
            # 详细输出数据情况
            self.logger.debug(f"股票 {stock_code}: 周线数据{weekly_count}条, 平均交易量数据{avg_count}条")
            
            if weekly_count < DATA_FILTER_CONFIG.get('min_weekly_data', 20):
                missing_weekly_data.append(f"{stock_code}({weekly_count}条)")
            elif avg_count < 5:  # 需要至少5条记录
                missing_avg_data.append(f"{stock_code}({avg_count}条)")
            else:
                valid_stocks.append(stock_code)
        
        # 报告验证结果
        self.logger.info(f"数据完整性验证结果:")
        self.logger.info(f"  检查股票数: {len(sample_stocks)}")
        self.logger.info(f"  数据完整: {len(valid_stocks)} 只")
        self.logger.info(f"  周线数据不足: {len(missing_weekly_data)} 只")
        self.logger.info(f"  平均数据不足: {len(missing_avg_data)} 只")
        
        if missing_weekly_data:
            self.logger.warning(f"周线数据不足的股票（示例前5只）: {missing_weekly_data[:5]}")
        
        if missing_avg_data:
            self.logger.warning(f"平均交易量数据不足的股票（示例前5只）: {missing_avg_data[:5]}")
        
        if valid_stocks:
            self.logger.info(f"数据完整的股票（示例前10只）: {valid_stocks[:10]}")
        
        if not missing_weekly_data and not missing_avg_data:
            self.logger.info("✅ 数据完整性验证通过")
        else:
            self.logger.warning("⚠️ 存在数据缺失，建议先运行完整的数据准备流程")
    
    def get_stock_list(self, test_mode: bool, test_count: int) -> List[Dict[str, Any]]:
        """获取股票列表"""
        stock_list = self.stock_model.get_stock_list()
        
        # 检查是否有指定测试股票
        test_single_stock = DEBUG_CONFIG.get('test_single_stock')
        if test_single_stock:
            # 只分析指定的单只股票
            filtered_list = [s for s in stock_list if s['gp_num'] == test_single_stock]
            if filtered_list:
                self.logger.info(f"🔍 调试模式：仅分析指定股票 {test_single_stock}")
                return filtered_list
            else:
                self.logger.warning(f"⚠️ 指定的测试股票 {test_single_stock} 不存在")
        
        if test_mode:
            stock_list = stock_list[:test_count]
            self.logger.info(f"测试模式：获取前 {test_count} 只股票")
        else:
            self.logger.info(f"生产模式：获取全部 {len(stock_list)} 只股票")
        return stock_list
    
    def process_weekly_data_multithread(self, stock_list: List[Dict[str, Any]]):
        """处理周线数据 - 多线程版本，支持增量处理"""
        self.data_logger.log_function_entry('process_weekly_data_multithread', stock_count=len(stock_list))

        # 增量处理：清理旧数据
        if self.incremental_manager.should_use_incremental():
            self.data_logger.log_incremental_processing("开始", "weekly_data_cleanup", len(stock_list))
            start_date, _ = self.incremental_manager.get_incremental_date_range('weekly_data')
            cleaned_count = self.incremental_manager.cleanup_old_data('weekly_data', start_date)
            self.data_logger.log_incremental_processing("完成", "weekly_data_cleanup", cleaned_count)

        thread_count = THREADING_CONFIG.get('weekly_data_threads', 8)
        chunk_size = max(1, len(stock_list) // thread_count)
        stock_chunks = [stock_list[i:i + chunk_size] for i in range(0, len(stock_list), chunk_size)]

        mode_text = "增量模式" if self.incremental_manager.should_use_incremental() else "全量模式"
        self.data_logger.info(f"📊 {mode_text}：将 {len(stock_list)} 只股票分成 {len(stock_chunks)} 个块进行处理")

        # 创建进度日志器
        progress_logger = self.data_logger.create_progress_logger(len(stock_chunks), "周线数据处理")

        with ThreadPoolExecutor(max_workers=thread_count) as executor:
            future_to_chunk = {
                executor.submit(self.process_weekly_data_chunk, chunk, idx): idx
                for idx, chunk in enumerate(stock_chunks)
            }

            for future in as_completed(future_to_chunk):
                chunk_id = future_to_chunk[future]
                try:
                    processed_count = future.result()
                    progress_logger.update(1)
                    self.data_logger.debug(f"✅ 周线数据处理块 {chunk_id + 1}/{len(stock_chunks)} 完成，处理 {processed_count} 只股票")
                except Exception as e:
                    self.data_logger.error(f"❌ 处理周线数据块 {chunk_id + 1} 失败", exception=e)

        progress_logger.finish()
        self.data_logger.log_function_exit('process_weekly_data_multithread')
    
    def process_weekly_data_chunk(self, stock_chunk: List[Dict[str, Any]], chunk_id: int) -> int:
        """处理一组股票的周线数据 - 优化分批插入版本"""
        processed_count = 0
        chunk_start_time = time.time()

        self.data_logger.debug(f"🔄 块 {chunk_id + 1} 开始处理 {len(stock_chunk)} 只股票的周线数据")

        # 分批收集和插入数据，避免内存占用过大
        batch_weekly_data = []
        batch_size = THREADING_CONFIG.get('data_processing_chunk_size', 50)  # 每50只股票插入一次

        for i, stock in enumerate(stock_chunk):
            stock_code = stock['gp_num']

            # 每处理50只股票输出一次进度并分批插入
            if i > 0 and i % batch_size == 0:
                elapsed = time.time() - chunk_start_time
                self.data_logger.debug(f"📈 块 {chunk_id + 1} 已处理 {i}/{len(stock_chunk)} 只股票，耗时 {elapsed:.1f}秒")

                # 分批插入数据
                if batch_weekly_data:
                    try:
                        with db_write_lock:
                            result = self.stock_model.insert_weekly_data(batch_weekly_data)
                            self.data_logger.log_database_operation('批量插入周线数据', 'stock_weekly_data', len(batch_weekly_data))
                        batch_weekly_data = []  # 清空缓存
                    except Exception as e:
                        self.data_logger.error(f"❌ 分批插入周线数据失败", exception=e)
                        batch_weekly_data = []  # 清空缓存继续处理
            
            try:
                # 获取日线数据并转换为周线 - 支持增量处理
                # 修复：对于没有最近数据的股票，使用全量数据进行分析
                if self.incremental_manager.should_use_incremental():
                    start_date, _ = self.incremental_manager.get_incremental_date_range('weekly_data')
                    start_date_str = start_date.strftime('%Y-%m-%d')

                    # 先尝试获取增量数据
                    daily_data = self.stock_model.get_stock_daily_data(stock_code, start_date_str)

                    # 如果增量数据不足，回退到全量数据
                    if len(daily_data) < DATA_FILTER_CONFIG.get('min_daily_data', 50):
                        fallback_start_date = DATA_FILTER_CONFIG.get('start_date', '2013-01-01')
                        daily_data = self.stock_model.get_stock_daily_data(stock_code, fallback_start_date)
                        if len(daily_data) >= DATA_FILTER_CONFIG.get('min_daily_data', 50):
                            self.logger.debug(f"股票 {stock_code} 增量数据不足，使用全量数据: {len(daily_data)}条")
                else:
                    start_date_str = DATA_FILTER_CONFIG.get('start_date', '2013-01-01')
                    daily_data = self.stock_model.get_stock_daily_data(stock_code, start_date_str)

                if len(daily_data) < DATA_FILTER_CONFIG.get('min_daily_data', 50):
                    continue
                
                weekly_data = self.data_processor.convert_daily_to_weekly(daily_data)
                if len(weekly_data) < DATA_FILTER_CONFIG.get('min_weekly_data', 10):
                    continue
                
                # 添加股票代码到每条记录中
                for data in weekly_data:
                    data['stock_code'] = stock_code
                
                # 收集到批量列表中
                batch_weekly_data.extend(weekly_data)
                processed_count += 1
                
            except Exception as e:
                self.logger.error(f"处理股票 {stock_code} 周线数据失败: {e}")
                # 在错误级别下也输出详细错误信息
                import traceback
                self.logger.error(f"详细错误信息: {traceback.format_exc()}")
                continue
        
        # 插入剩余的数据
        if batch_weekly_data:
            try:
                with db_write_lock:
                    result = self.stock_model.insert_weekly_data(batch_weekly_data)
                    self.logger.debug(f"块 {chunk_id + 1} 最终插入 {len(batch_weekly_data)} 条周线数据，结果: {result}")
            except Exception as e:
                self.logger.error(f"最终插入周线数据失败: {e}")
        
        elapsed = time.time() - chunk_start_time
        self.logger.info(f"块 {chunk_id + 1} 完成，处理 {processed_count}/{len(stock_chunk)} 只股票，耗时 {elapsed:.1f}秒")
        return processed_count
    
    def calculate_avg_trade_volume_multithread(self, stock_list: List[Dict[str, Any]]):
        """计算平均交易量 - 多线程版本"""
        thread_count = THREADING_CONFIG.get('avg_volume_threads', 6)
        chunk_size = max(1, len(stock_list) // thread_count)
        stock_chunks = [stock_list[i:i + chunk_size] for i in range(0, len(stock_list), chunk_size)]
        
        self.logger.info(f"将 {len(stock_list)} 只股票分成 {len(stock_chunks)} 个块进行平均交易量计算")
        
        with ThreadPoolExecutor(max_workers=thread_count) as executor:
            future_to_chunk = {
                executor.submit(self.calculate_avg_trade_volume_chunk, chunk, idx): idx 
                for idx, chunk in enumerate(stock_chunks)
            }
            
            for future in as_completed(future_to_chunk):
                chunk_id = future_to_chunk[future]
                try:
                    processed_count = future.result()
                    self.logger.info(f"平均交易量计算块 {chunk_id + 1}/{len(stock_chunks)} 完成，处理 {processed_count} 只股票")
                except Exception as e:
                    self.logger.error(f"计算平均交易量块 {chunk_id + 1} 失败: {e}")
    
    def calculate_avg_trade_volume_chunk(self, stock_chunk: List[Dict[str, Any]], chunk_id: int) -> int:
        """计算一组股票的平均交易量 - 优化分批插入版本"""
        processed_count = 0
        chunk_start_time = time.time()
        
        self.logger.info(f"块 {chunk_id + 1} 开始计算 {len(stock_chunk)} 只股票的平均交易量")
        
        # 分批收集和插入数据，避免内存占用过大
        batch_avg_data = []
        batch_size = THREADING_CONFIG.get('data_processing_chunk_size', 50)  # 每50只股票插入一次
        
        for i, stock in enumerate(stock_chunk):
            stock_code = stock['gp_num']
            
            # 每处理50只股票输出一次进度并分批插入
            if i > 0 and i % batch_size == 0:
                elapsed = time.time() - chunk_start_time
                self.logger.info(f"块 {chunk_id + 1} 已计算 {i}/{len(stock_chunk)} 只股票，耗时 {elapsed:.1f}秒")
                
                # 分批插入数据
                if batch_avg_data:
                    try:
                        with db_write_lock:
                            result = self.stock_model.insert_avg_trade_data(batch_avg_data)
                            self.logger.debug(f"块 {chunk_id + 1} 分批插入 {len(batch_avg_data)} 条平均交易量数据，结果: {result}")
                        batch_avg_data = []  # 清空缓存
                    except Exception as e:
                        self.logger.error(f"分批插入平均交易量数据失败: {e}")
                        batch_avg_data = []  # 清空缓存继续处理
            
            try:
                # 获取周线数据
                weekly_data = self.stock_model.get_weekly_data(stock_code)
                if len(weekly_data) < 5:
                    self.logger.debug(f"股票 {stock_code} 周线数据不足: {len(weekly_data)} < 5")
                    continue
                
                # 计算各期间的平均交易量
                avg_trade_data = self.calculate_avg_trade_volumes(weekly_data, stock_code)
                
                if avg_trade_data:
                    # 收集到批量列表中
                    batch_avg_data.extend(avg_trade_data)
                    processed_count += 1
                else:
                    self.logger.debug(f"股票 {stock_code} 平均交易量数据为空")
                
            except Exception as e:
                self.logger.error(f"计算股票 {stock_code} 平均交易量失败: {e}")
                # 在错误级别下也输出详细错误信息
                import traceback
                self.logger.error(f"详细错误信息: {traceback.format_exc()}")
                continue
        
        # 插入剩余的数据
        if batch_avg_data:
            try:
                with db_write_lock:
                    result = self.stock_model.insert_avg_trade_data(batch_avg_data)
                    self.logger.debug(f"块 {chunk_id + 1} 最终插入 {len(batch_avg_data)} 条平均交易量数据，结果: {result}")
            except Exception as e:
                self.logger.error(f"最终插入平均交易量数据失败: {e}")
        
        elapsed = time.time() - chunk_start_time
        self.logger.info(f"块 {chunk_id + 1} 完成，处理 {processed_count}/{len(stock_chunk)} 只股票，耗时 {elapsed:.1f}秒")
        return processed_count
    
    def calculate_avg_trade_volumes(self, weekly_data: List[Dict[str, Any]], stock_code: str) -> List[Dict[str, Any]]:
        """
        计算各种期间的平均交易量和平均交易额
        严格按照Oracle CALCUL_AVG_WQTY逻辑实现
        
        Oracle逻辑分析：
        - 90天时间过滤: where t.jy_date >= trunc(sysdate - 90)
        - 行号范围: d.row_num between rec.row_num - (i + 1) and (rec.row_num - 1)
        - 时间过滤: d.jy_date < rec.jy_date
        """
        try:
            from datetime import datetime, timedelta
            
            avg_trade_data = []
            avg_days_list = VOLUME_CONFIG.get('avg_days', [5, 10, 20, 30])
            
            # 修改：为了适应测试数据，暂时取消90天时间过滤
            # Oracle 90天时间过滤: where t.jy_date >= trunc(sysdate - 90)
            # current_date = datetime.now()
            # filter_date = current_date - timedelta(days=90)
            # filter_date_str = filter_date.strftime('%Y-%m-%d')

            # 使用数据的最早日期作为起始点，确保所有数据都能计算平均交易量
            if weekly_data:
                filter_date_str = weekly_data[0]['trade_date'].strftime('%Y-%m-%d') if hasattr(weekly_data[0]['trade_date'], 'strftime') else str(weekly_data[0]['trade_date'])
            else:
                filter_date_str = '2020-01-01'  # 默认起始日期
            
            # 过滤90天内的数据
            filtered_data = []
            for week_data in weekly_data:
                week_date = week_data['trade_date']
                # 统一转换为字符串进行比较
                if isinstance(week_date, str):
                    week_date_str = week_date
                else:
                    # 如果是datetime.date对象，转换为字符串
                    week_date_str = week_date.strftime('%Y-%m-%d')
                
                if week_date_str >= filter_date_str:
                    filtered_data.append(week_data)
            
            if not filtered_data:
                return []
            
            # Oracle的逻辑：为每个记录计算各种期间的平均值
            for current_idx, current_week in enumerate(filtered_data):
                for avg_days in avg_days_list:
                    # Oracle核心逻辑：
                    # d.row_num between rec.row_num - (i + 1) and (rec.row_num - 1)
                    # 其中rec.row_num是当前记录的行号（1-based），i是avg_days
                    
                    # 转换为0-based索引：
                    # current_row_num = current_idx + 1 (转为1-based)
                    # 范围: [current_row_num - (avg_days + 1), current_row_num - 1]
                    # 转为0-based: [current_idx - avg_days, current_idx - 1]
                    
                    if current_idx < avg_days:
                        # 历史数据不足，跳过
                        continue
                    
                    # Oracle逻辑：range [current - avg_days, current - 1] (0-based)
                    start_idx = current_idx - avg_days
                    end_idx = current_idx  # Python切片不包含end_idx，所以是current_idx-1的下一个位置
                    
                    period_data = filtered_data[start_idx:end_idx]
                    
                    # 确保取到正确的记录数
                    if len(period_data) != avg_days:
                        continue
                    
                    # Oracle字段对应：jy_quantity → jy_quantity, jy_amount → jy_amount
                    volumes = [d.get('jy_quantity', 0) for d in period_data]
                    amounts = [d.get('jy_amount', 0) for d in period_data]
                    
                    # Oracle使用AVG函数，包含NULL值处理：avg(nvl(d.jy_quantity, 0))
                    volume_count = len(volumes)
                    amount_count = len(amounts)
                    
                    avg_qty = sum(volumes) / volume_count if volume_count > 0 else 0.0
                    avg_amount = sum(amounts) / amount_count if amount_count > 0 else 0.0
                    
                    avg_trade_data.append({
                        'stock_code': stock_code,
                        'trade_date': current_week['trade_date'],
                        'avg_days': avg_days,
                        'avg_qty': avg_qty,
                        'avg_amount': avg_amount  # Oracle字段名：avg_amount
                    })
            
            return avg_trade_data
            
        except Exception as e:
            self.logger.error(f"计算平均交易量失败 {stock_code}: {e}")
            return []

    def analyze_patterns_multithread(self, stock_list: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """多线程分析股票形态 - 对应Oracle包MAIN_W"""
        self.enhanced_logger.log_function_entry('analyze_patterns_multithread', stock_count=len(stock_list))

        thread_count = THREADING_CONFIG.get('pattern_analysis_threads', 8)
        chunk_size = THREADING_CONFIG.get('chunk_size', 40)

        # 将股票列表分成chunks
        stock_chunks = [stock_list[i:i + chunk_size] for i in range(0, len(stock_list), chunk_size)]

        self.enhanced_logger.info(f"📈 开始形态分析，{len(stock_list)} 只股票分为 {len(stock_chunks)} 个批次，使用 {thread_count} 个线程")

        # 预加载所有股票的数据 - 性能优化
        self.enhanced_logger.info("🔄 预加载股票数据...")
        preload_start = time.time()
        all_stock_codes = [stock['gp_num'] for stock in stock_list]

        # 批量加载周线数据和平均交易量数据
        weekly_data_cache = self.stock_model.get_multiple_weekly_data(all_stock_codes)
        avg_trade_data_cache = self.stock_model.get_multiple_avg_trade_data(all_stock_codes)

        preload_time = time.time() - preload_start
        self.enhanced_logger.info(f"✅ 数据预加载完成，耗时 {preload_time:.2f}秒")

        all_results = []
        total_patterns = 0
        start_time = time.time()

        # 创建进度日志器
        progress_logger = self.enhanced_logger.create_progress_logger(len(stock_chunks), "形态分析")

        with ThreadPoolExecutor(max_workers=thread_count) as executor:
            future_to_chunk = {
                executor.submit(self.analyze_stock_chunk_optimized, chunk, idx, weekly_data_cache, avg_trade_data_cache): idx
                for idx, chunk in enumerate(stock_chunks)
            }

            for future in as_completed(future_to_chunk):
                chunk_id = future_to_chunk[future]
                try:
                    chunk_results, analyzed_count = future.result(timeout=THREADING_CONFIG.get('timeout', 900))
                    all_results.extend(chunk_results)
                    total_patterns += len(chunk_results)
                    progress_logger.update(1)
                    self.enhanced_logger.log_pattern_analysis(f"批次{chunk_id + 1}", "batch_complete", True,
                                                            {"patterns_found": len(chunk_results), "analyzed_count": analyzed_count})
                except Exception as e:
                    self.enhanced_logger.error(f"❌ 批次 {chunk_id + 1} 处理失败", exception=e)

        progress_logger.finish()
        elapsed = time.time() - start_time
        self.enhanced_logger.info(f"🎯 形态分析完成，共找到 {total_patterns} 个形态，耗时 {elapsed:.1f} 秒")
        self.enhanced_logger.log_function_exit('analyze_patterns_multithread')
        return all_results

    def analyze_stock_chunk_optimized(self, stock_chunk: List[Dict[str, Any]], chunk_id: int,
                                    weekly_data_cache: Dict[str, List[Dict[str, Any]]],
                                    avg_trade_data_cache: Dict[str, List[Dict[str, Any]]]) -> tuple:
        """分析一组股票的形态 - 优化版本，使用预加载的数据"""
        results = []
        analyzed_count = 0
        chunk_start_time = time.time()

        self.enhanced_logger.debug(f"📊 块 {chunk_id + 1} 开始分析 {len(stock_chunk)} 只股票（使用缓存数据）")

        # 创建分析器并设置stock_model用于MA支撑点计算
        analyzer = PatternAnalyzer(U_PATTERN_CONFIG, V_PATTERN_CONFIG, VOLUME_CONFIG)
        analyzer.stock_model = self.stock_model  # 关键修复：设置stock_model用于MA数据查询

        for stock in stock_chunk:
            stock_code = stock['gp_num']
            stock_name = stock['gp_name']

            try:
                # 从缓存获取数据，避免重复查询
                weekly_data = weekly_data_cache.get(stock_code, [])
                avg_trade_data = avg_trade_data_cache.get(stock_code, [])

                if len(weekly_data) < DATA_FILTER_CONFIG.get('min_weekly_data', 20):
                    self.enhanced_logger.debug(f"⚠️ {stock_code} 周线数据不足: {len(weekly_data)} < {DATA_FILTER_CONFIG.get('min_weekly_data', 20)}")
                    continue

                # 执行形态分析
                result = analyzer.analyze_stock_patterns_strict(stock_code, stock_name, weekly_data, avg_trade_data)

                if result:
                    results.append(result)
                    self.enhanced_logger.log_pattern_analysis(stock_code, result.get('pattern_type', 'UNKNOWN'), True,
                                                            {"stock_name": stock_name, "pattern_details": result})
                else:
                    self.enhanced_logger.debug(f"📉 {stock_code} 未发现符合条件的形态")

                analyzed_count += 1

            except Exception as e:
                self.enhanced_logger.error(f"❌ 分析股票 {stock_code} 失败", exception=e, stock_code=stock_code)

        elapsed = time.time() - chunk_start_time
        self.enhanced_logger.debug(f"✅ 块 {chunk_id + 1} 完成，分析 {analyzed_count} 只股票，找到 {len(results)} 个形态，耗时 {elapsed:.1f}秒")
        return results, analyzed_count

    def analyze_stock_chunk(self, stock_chunk: List[Dict[str, Any]], chunk_id: int) -> tuple:
        """分析一组股票的形态"""
        results = []
        analyzed_count = 0
        chunk_start_time = time.time()
        
        self.logger.info(f"块 {chunk_id + 1} 开始分析 {len(stock_chunk)} 只股票")
        
        for i, stock in enumerate(stock_chunk):
            stock_code = stock['gp_num']
            stock_name = stock['gp_name']
            
            # 每处理100只股票输出一次进度（减少频率）
            if i > 0 and i % 100 == 0:
                elapsed = time.time() - chunk_start_time
                self.logger.info(f"块 {chunk_id + 1} 已分析 {i}/{len(stock_chunk)} 只股票，耗时 {elapsed:.1f}秒，发现 {len(results)} 个形态")
            
            try:
                # 获取周线数据
                weekly_data = self.stock_model.get_weekly_data(stock_code)
                weekly_count = len(weekly_data)
                
                # 只有在调试模式下才输出每只股票的详细信息
                debug_enabled = self.logger.level <= logging.DEBUG
                if debug_enabled and (i % 10 == 0 or weekly_count > 0):  # 调试模式下每10只股票输出一次
                    self.logger.debug(f"股票 {stock_code} ({stock_name}): 周线数据{weekly_count}条")
                
                if weekly_count < DATA_FILTER_CONFIG.get('min_weekly_data', 20):
                    if debug_enabled and weekly_count > 0:
                        self.logger.debug(f"股票 {stock_code} 周线数据不足: {weekly_count} < {DATA_FILTER_CONFIG.get('min_weekly_data', 20)}")
                    continue
                
                # 获取平均交易量数据
                avg_trade_data = self.stock_model.get_avg_trade_data(stock_code)
                avg_count = len(avg_trade_data)
                
                # 只有在调试模式下才输出详细的分析开始信息
                if debug_enabled:
                    self.logger.debug(f"股票 {stock_code} 开始分析: 周线{weekly_count}条, 平均量{avg_count}条")
                    # 检查5日平均数据
                    avg_5d_data = [d for d in avg_trade_data if d.get('avg_days') == 5]
                    self.logger.debug(f"股票 {stock_code} 5日平均数据: {len(avg_5d_data)}条")
                
                analyzed_count += 1
                
                # 进行形态分析
                result = self.pattern_analyzer.analyze_stock_patterns_strict(
                    stock_code, stock_name, weekly_data, avg_trade_data
                )
                
                if result:
                    result['batch_id'] = self.batch_id
                    results.append(result)
                    # 使用锁控制数据库写入
                    with db_write_lock:
                        self.stock_model.insert_analysis_result(result)
                        time.sleep(0.001)  # 小延迟
                    self.logger.info(f"🎯 发现形态: {stock_code} {stock_name}")
                elif debug_enabled:
                    self.logger.debug(f"股票 {stock_code} 未发现符合条件的形态")
            
            except Exception as e:
                self.logger.error(f"分析股票 {stock_code} 失败: {e}")
                if debug_enabled:
                    import traceback
                    self.logger.debug(traceback.format_exc())
                continue
        
        elapsed = time.time() - chunk_start_time
        self.logger.info(f"块 {chunk_id + 1} 完成，分析 {analyzed_count}/{len(stock_chunk)} 只股票，发现 {len(results)} 个形态，耗时 {elapsed:.1f}秒")
        return results, analyzed_count
    
    def export_results(self, results: List[Dict[str, Any]]) -> str:
        """导出结果到Excel - 从数据库获取完整的补全数据"""
        try:
            # 从数据库获取当前批次的完整分析结果（包含补全字段）
            db_results = self.stock_model.get_analysis_results(self.batch_id)
            
            if db_results:
                self.logger.info(f"从数据库获取到 {len(db_results)} 条完整分析结果")
                return self.data_processor.export_to_excel(db_results, EXPORT_CONFIG)
            else:
                self.logger.warning("数据库中未找到分析结果，使用内存中的结果")
                return self.data_processor.export_to_excel(results, EXPORT_CONFIG)
                
        except Exception as e:
            self.logger.error(f"导出Excel失败: {e}")
            # 创建空结果文件
            try:
                import pandas as pd
                from datetime import datetime
                
                export_dir = EXPORT_CONFIG.get('export_dir', 'exports')
                if not os.path.exists(export_dir):
                    os.makedirs(export_dir)
                
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                filename = f"{timestamp}_stock_analysis_results.xlsx"
                filepath = os.path.join(export_dir, filename)
                
                df = pd.DataFrame([{'message': '本次分析未发现符合条件的形态'}])
                df.to_excel(filepath, index=False)
                
                self.logger.info(f"已创建空结果文件: {filepath}")
                return filepath
            except Exception as e2:
                self.logger.error(f"创建空结果文件也失败: {e2}")
                return ""
    
    def send_notification(self, excel_filepath: str, total_stocks: int, found_patterns: int):
        """发送邮件通知"""
        if EMAIL_CONFIG.get('enable_email', False):
            try:
                self.data_processor.send_email_notification(
                    excel_filepath, total_stocks, found_patterns, EMAIL_CONFIG
                )
            except Exception as e:
                self.logger.error(f"发送邮件失败: {e}")

    def load_stock_data(self):
        """
        第1步：加载股票基础数据
        严格按照Oracle包逻辑过滤数据
        """
        try:
            self.logger.info("开始加载股票基础数据...")
            
            # Oracle包逻辑：排除港股，只处理沪深A股
            # where jys_no <> 'hk'
            sql = """
            SELECT s.code as stock_code, s.name as stock_name,
                   CASE 
                       WHEN s.code LIKE '0%%' THEN 'sz'
                       WHEN s.code LIKE '3%%' THEN 'sz' 
                       WHEN s.code LIKE '6%%' THEN 'sh'
                       ELSE 'other'
                   END as exchange
            FROM stock_info_a_code_name s
            WHERE s.code LIKE '0%%' OR s.code LIKE '3%%' OR s.code LIKE '6%%'
            ORDER BY s.code
            """
            
            with self.connection.cursor(pymysql.cursors.DictCursor) as cursor:
                cursor.execute(sql)
                self.stock_list = cursor.fetchall()
            
            self.logger.info(f"成功加载 {len(self.stock_list)} 只股票数据")
            return True
            
        except Exception as e:
            self.logger.error(f"加载股票基础数据失败: {e}")
            return False

    def load_weekly_data_for_stock(self, stock_code: str) -> List[Dict[str, Any]]:
        """
        获取单只股票的周线数据
        严格按照Oracle包逻辑：jy_date >= '2013-01-01'
        """
        try:
            sql = """
            SELECT 
                stock_code,
                trade_date,
                open_price,
                high_price,
                close_price,
                low_price,
                volume,
                amount
            FROM stock_weekly_data
            WHERE stock_code = %s 
            AND trade_date >= %s  -- Oracle包逻辑：jy_date >= to_date('20130101', 'yyyymmdd')
            ORDER BY trade_date
            """
            
            with self.connection.cursor(pymysql.cursors.DictCursor) as cursor:
                cursor.execute(sql, (stock_code, DATA_FILTER_CONFIG['start_date']))
                results = cursor.fetchall()
            
            # 过滤掉数据不足的股票
            if len(results) < DATA_FILTER_CONFIG['min_weekly_data']:
                return []
            
            return results
        except Exception as e:
            self.logger.error(f"加载股票 {stock_code} 周线数据失败: {e}")
            return []

    def load_weekly_data_batch(self, stock_codes: List[str], start_date: str = None) -> Dict[str, List[Dict[str, Any]]]:
        """
        批量加载周线数据 - 严格按照Oracle包逻辑
        """
        if not start_date:
            start_date = DATA_FILTER_CONFIG['start_date']
        
        if not stock_codes:
            return {}
        
        try:
            # 构建SQL的IN子句
            placeholders = ','.join(['%s'] * len(stock_codes))
            sql = f"""
            SELECT 
                stock_code,
                trade_date,
                open_price,
                high_price,
                close_price,
                low_price,
                volume,
                amount
            FROM stock_weekly_data
            WHERE stock_code IN ({placeholders})
            AND trade_date >= %s  -- Oracle包逻辑：jy_date >= to_date('20130101', 'yyyymmdd')
            ORDER BY stock_code, trade_date
            """
            
            params = stock_codes + [start_date]
            
            with self.connection.cursor(pymysql.cursors.DictCursor) as cursor:
                cursor.execute(sql, params)
                results = cursor.fetchall()
            
            # 按股票代码分组
            stock_data = {}
            for row in results:
                stock_code = row['stock_code']
                if stock_code not in stock_data:
                    stock_data[stock_code] = []
                stock_data[stock_code].append(row)
            
            # 过滤掉数据不足的股票
            filtered_data = {}
            for stock_code, data in stock_data.items():
                if len(data) >= DATA_FILTER_CONFIG['min_weekly_data']:
                    filtered_data[stock_code] = data
                else:
                    self.logger.debug(f"股票 {stock_code} 周线数据不足: {len(data)} < {DATA_FILTER_CONFIG['min_weekly_data']}")
            
            return filtered_data
        except Exception as e:
            self.logger.error(f"批量加载周线数据失败: {e}")
            return {}

    def load_avg_trade_data_batch(self, stock_codes: List[str], start_date: str = None) -> Dict[str, List[Dict[str, Any]]]:
        """
        批量加载平均交易量数据 - 严格按照Oracle包逻辑
        """
        if not start_date:
            start_date = DATA_FILTER_CONFIG['start_date']
        
        if not stock_codes:
            return {}
        
        try:
            placeholders = ','.join(['%s'] * len(stock_codes))
            sql = f"""
            SELECT 
                stock_code,
                trade_date,
                avg_days,
                avg_qty,
                avg_amount
            FROM stock_avg_trade
            WHERE stock_code IN ({placeholders})
            AND trade_date >= %s  -- Oracle包逻辑：对应的时间过滤
            AND avg_days = 5  -- Oracle包中主要使用5日平均交易量
            ORDER BY stock_code, trade_date
            """
            
            params = stock_codes + [start_date]
            
            with self.connection.cursor(pymysql.cursors.DictCursor) as cursor:
                cursor.execute(sql, params)
                results = cursor.fetchall()
            
            # 按股票代码分组
            stock_data = {}
            for row in results:
                stock_code = row['stock_code']
                if stock_code not in stock_data:
                    stock_data[stock_code] = []
                stock_data[stock_code].append(row)
            
            return stock_data
        except Exception as e:
            self.logger.error(f"批量加载平均交易量数据失败: {e}")
            return {}

    def calculate_moving_average_multithread(self, stock_list: List[Dict[str, Any]]):
        """计算移动平均线 - 多线程版本 - 对应Oracle CALCULATION_AVG_JYM"""
        self.ma_logger.log_function_entry('calculate_moving_average_multithread', stock_count=len(stock_list))

        thread_count = THREADING_CONFIG.get('moving_average_threads', 6)
        chunk_size = max(1, len(stock_list) // thread_count)
        stock_chunks = [stock_list[i:i + chunk_size] for i in range(0, len(stock_list), chunk_size)]

        self.ma_logger.info(f"📊 将 {len(stock_list)} 只股票分成 {len(stock_chunks)} 个块进行移动平均线计算")

        # 创建进度日志器
        progress_logger = self.ma_logger.create_progress_logger(len(stock_chunks), "移动平均线计算")

        with ThreadPoolExecutor(max_workers=thread_count) as executor:
            future_to_chunk = {
                executor.submit(self.calculate_moving_average_chunk, chunk, idx): idx
                for idx, chunk in enumerate(stock_chunks)
            }

            for future in as_completed(future_to_chunk):
                chunk_id = future_to_chunk[future]
                try:
                    processed_count = future.result()
                    progress_logger.update(1)
                    self.ma_logger.debug(f"✅ 移动平均线计算块 {chunk_id + 1}/{len(stock_chunks)} 完成，处理 {processed_count} 只股票")
                except Exception as e:
                    self.ma_logger.error(f"❌ 计算移动平均线块 {chunk_id + 1} 失败", exception=e)

        progress_logger.finish()
        self.ma_logger.log_function_exit('calculate_moving_average_multithread')

    def calculate_moving_average_chunk(self, stock_chunk: List[Dict[str, Any]], chunk_id: int) -> int:
        """计算一组股票的移动平均线 - 优化分批插入版本"""
        processed_count = 0
        chunk_start_time = time.time()
        
        self.ma_logger.debug(f"📈 块 {chunk_id + 1} 开始计算 {len(stock_chunk)} 只股票的移动平均线")

        # 分批收集和插入数据，避免内存占用过大
        batch_ma_data = []
        batch_size = THREADING_CONFIG.get('data_processing_chunk_size', 50)  # 每50只股票插入一次

        for i, stock in enumerate(stock_chunk):
            stock_code = stock['gp_num']

            # 每处理50只股票输出一次进度并分批插入
            if i > 0 and i % batch_size == 0:
                elapsed = time.time() - chunk_start_time
                self.ma_logger.debug(f"🔢 块 {chunk_id + 1} 已计算 {i}/{len(stock_chunk)} 只股票，耗时 {elapsed:.1f}秒")

                # 分批插入数据
                if batch_ma_data:
                    try:
                        with db_write_lock:
                            result = self.stock_model.insert_moving_average_data(batch_ma_data)
                            self.ma_logger.log_database_operation('批量插入移动平均线数据', 'stock_moving_average', len(batch_ma_data))
                        batch_ma_data = []  # 清空缓存
                    except Exception as e:
                        self.ma_logger.error(f"❌ 分批插入移动平均线数据失败", exception=e)
                        batch_ma_data = []  # 清空缓存继续处理
            
            try:
                # 获取周线数据 - 支持增量处理
                # 修复：对于没有最近数据的股票，使用全量数据进行分析
                if self.incremental_manager.should_use_incremental():
                    start_date, _ = self.incremental_manager.get_incremental_date_range('moving_average')
                    start_date_str = start_date.strftime('%Y-%m-%d')
                    weekly_data = self.stock_model.get_weekly_data(stock_code, start_date_str)

                    # 如果增量数据不足，回退到全量数据
                    if len(weekly_data) < MOVING_AVERAGE_CONFIG.get('min_data_points', 20):
                        weekly_data = self.stock_model.get_weekly_data(stock_code)
                        if len(weekly_data) >= MOVING_AVERAGE_CONFIG.get('min_data_points', 20):
                            self.logger.debug(f"股票 {stock_code} 移动平均线增量数据不足，使用全量数据: {len(weekly_data)}条")
                else:
                    weekly_data = self.stock_model.get_weekly_data(stock_code)

                if len(weekly_data) < MOVING_AVERAGE_CONFIG.get('min_data_points', 20):  # 使用配置中的最小数据点
                    continue
                
                # 计算移动平均线
                ma_data = self.calculate_moving_averages(weekly_data, stock_code)
                
                if ma_data:
                    batch_ma_data.extend(ma_data)
                    processed_count += 1
                
            except Exception as e:
                self.logger.error(f"计算股票 {stock_code} 移动平均线失败: {e}")
                continue
        
        # 插入剩余的数据
        if batch_ma_data:
            try:
                with db_write_lock:
                    result = self.stock_model.insert_moving_average_data(batch_ma_data)
                    self.logger.debug(f"块 {chunk_id + 1} 最终插入 {len(batch_ma_data)} 条移动平均线数据，结果: {result}")
            except Exception as e:
                self.logger.error(f"最终插入移动平均线数据失败: {e}")
        
        elapsed = time.time() - chunk_start_time
        self.logger.info(f"块 {chunk_id + 1} 完成，处理 {processed_count}/{len(stock_chunk)} 只股票，耗时 {elapsed:.1f}秒")
        return processed_count

    def calculate_moving_averages(self, weekly_data: List[Dict[str, Any]], stock_code: str) -> List[Dict[str, Any]]:
        """
        计算移动平均线 - 严格按照Oracle CALCULATION_AVG_JYM逻辑
        
        Oracle逻辑：
        FOR I IN 1 .. 20 LOOP  -- 计算1-20周的移动平均
            SELECT round(AVG(NVL(CLOSE_PRICE, 0)), 2) INTO L_AVG_PRICE
            FROM GP_JY_W_TMP
            WHERE ROW_NUM >= REC.ROW_NUM - (I - 1) and ROW_NUM <= REC.ROW_NUM;
            
        Oracle表结构 GP_AVG_JYW:
        - gp_num (股票代码) → stock_code
        - gp_week_num (周编号) → week_num  
        - gp_jy_date (交易日期) → trade_date
        - avg_months (平均月数/周期) → avg_months
        - avg_value (平均价格) → avg_value
        """
        try:
            ma_data = []
            
            # Oracle逻辑：为每个记录计算1-20周期的移动平均
            for current_idx, current_week in enumerate(weekly_data):
                for avg_months in MOVING_AVERAGE_CONFIG.get('periods', range(1, 21)):  # Oracle: FOR I IN 1 .. 20 LOOP
                    if current_idx < avg_months - 1:
                        # 历史数据不足，跳过
                        continue
                    
                    # Oracle逻辑：ROW_NUM >= REC.ROW_NUM - (I - 1) and ROW_NUM <= REC.ROW_NUM
                    # 转换为0-based索引：[current_idx - (avg_months - 1), current_idx]
                    start_idx = current_idx - (avg_months - 1)
                    end_idx = current_idx + 1  # Python切片不包含end_idx
                    
                    period_data = weekly_data[start_idx:end_idx]
                    
                    # 确保取到正确的记录数
                    if len(period_data) != avg_months:
                        continue
                    
                    # Oracle逻辑：round(AVG(NVL(CLOSE_PRICE, 0)), 2)
                    close_prices = [float(d.get('close_price', 0)) for d in period_data]
                    avg_price = sum(close_prices) / len(close_prices) if close_prices else 0.0
                    avg_price = round(avg_price, MOVING_AVERAGE_CONFIG.get('precision', 2))  # Oracle: round(..., 2)
                    
                    if avg_price > 0:  # Oracle: IF L_AVG_PRICE > 0 THEN
                        # 生成week_num如果不存在
                        week_num = current_week.get('week_num')
                        if not week_num:
                            # 使用Oracle兼容的周编号格式生成
                            import pandas as pd
                            from datetime import datetime
                            trade_date = current_week['trade_date']
                            if isinstance(trade_date, str):
                                trade_date = datetime.strptime(trade_date, '%Y-%m-%d').date()
                            week_num = self.data_processor.get_oracle_compatible_week_num(pd.Timestamp(trade_date))
                        
                        ma_data.append({
                            'stock_code': stock_code,                    # Oracle: gp_num
                            'week_num': week_num,                        # Oracle: gp_week_num
                            'trade_date': current_week['trade_date'],    # Oracle: gp_jy_date
                            'avg_months': avg_months,                    # Oracle: avg_months (字段名完全一致)
                            'avg_value': avg_price                       # Oracle: avg_value
                        })
                    else:
                        # Oracle: GOTO AVGPOINT; - 如果平均价格为0，停止计算更大周期
                        break
            
            return ma_data
            
        except Exception as e:
            self.logger.error(f"计算移动平均线失败 {stock_code}: {e}")
            return []

    def calculate_volatility_multithread(self, stock_list: List[Dict[str, Any]]):
        """计算波动率 - 多线程版本 - 对应Oracle CALCU_FLUCT"""
        thread_count = THREADING_CONFIG.get('volatility_threads', 6)
        chunk_size = max(1, len(stock_list) // thread_count)
        stock_chunks = [stock_list[i:i + chunk_size] for i in range(0, len(stock_list), chunk_size)]
        
        self.logger.info(f"将 {len(stock_list)} 只股票分成 {len(stock_chunks)} 个块进行波动率计算")
        
        with ThreadPoolExecutor(max_workers=thread_count) as executor:
            future_to_chunk = {
                executor.submit(self.calculate_volatility_chunk, chunk, idx): idx 
                for idx, chunk in enumerate(stock_chunks)
            }
            
            for future in as_completed(future_to_chunk):
                chunk_id = future_to_chunk[future]
                try:
                    processed_count = future.result()
                    self.logger.info(f"波动率计算块 {chunk_id + 1}/{len(stock_chunks)} 完成，处理 {processed_count} 只股票")
                except Exception as e:
                    self.logger.error(f"计算波动率块 {chunk_id + 1} 失败: {e}")

    def calculate_volatility_chunk(self, stock_chunk: List[Dict[str, Any]], chunk_id: int) -> int:
        """计算一组股票的波动率 - 优化分批插入版本"""
        processed_count = 0
        chunk_start_time = time.time()
        
        self.logger.info(f"块 {chunk_id + 1} 开始计算 {len(stock_chunk)} 只股票的波动率")
        
        # 分批收集和插入数据，避免内存占用过大
        batch_volatility_data = []
        batch_size = THREADING_CONFIG.get('data_processing_chunk_size', 50)  # 每50只股票插入一次
        
        for i, stock in enumerate(stock_chunk):
            stock_code = stock['gp_num']
            
            # 每处理50只股票输出一次进度并分批插入
            if i > 0 and i % batch_size == 0:
                elapsed = time.time() - chunk_start_time
                self.logger.info(f"块 {chunk_id + 1} 已计算 {i}/{len(stock_chunk)} 只股票，耗时 {elapsed:.1f}秒")
                
                # 分批插入数据
                if batch_volatility_data:
                    try:
                        with db_write_lock:
                            result = self.stock_model.insert_volatility_data(batch_volatility_data)
                            self.logger.debug(f"块 {chunk_id + 1} 分批插入 {len(batch_volatility_data)} 条波动率数据，结果: {result}")
                        batch_volatility_data = []  # 清空缓存
                    except Exception as e:
                        self.logger.error(f"分批插入波动率数据失败: {e}")
                        batch_volatility_data = []  # 清空缓存继续处理
            
            try:
                # 获取日线数据
                start_date = DATA_FILTER_CONFIG.get('start_date', '2013-01-01')
                daily_data = self.stock_model.get_stock_daily_data(stock_code, start_date)
                if len(daily_data) < VOLATILITY_CONFIG.get('min_data_points', 20):  # 使用配置中的最小数据点
                    continue
                
                # 计算波动率
                volatility_data = self.calculate_volatility_indicators(daily_data, stock_code)
                
                if volatility_data:
                    batch_volatility_data.extend(volatility_data)
                    processed_count += 1
                
            except Exception as e:
                self.logger.error(f"计算股票 {stock_code} 波动率失败: {e}")
                continue
        
        # 插入剩余的数据
        if batch_volatility_data:
            try:
                with db_write_lock:
                    result = self.stock_model.insert_volatility_data(batch_volatility_data)
                    self.logger.debug(f"块 {chunk_id + 1} 最终插入 {len(batch_volatility_data)} 条波动率数据，结果: {result}")
            except Exception as e:
                self.logger.error(f"最终插入波动率数据失败: {e}")
        
        elapsed = time.time() - chunk_start_time
        self.logger.info(f"块 {chunk_id + 1} 完成，处理 {processed_count}/{len(stock_chunk)} 只股票，耗时 {elapsed:.1f}秒")
        return processed_count

    def calculate_volatility_indicators(self, daily_data: List[Dict[str, Any]], stock_code: str) -> List[Dict[str, Any]]:
        """
        计算波动率指标 - 严格按照Oracle CALCU_FLUCT逻辑
        
        Oracle逻辑：
        1. 计算每日真实波幅(TR): greatest(abs(high-low), abs(high-close_prev), abs(close_prev-low))
        2. 计算20日平均真实波幅(ATR): sum(TR_20) / 20
        """
        try:
            volatility_data = []
            
            # 为日线数据添加行号（模拟Oracle的ROW_NUM）
            for i, day_data in enumerate(daily_data):
                day_data['row_num'] = i + 1
            
            # 第一步：计算每日真实波幅(Daily TR)
            for i, current_day in enumerate(daily_data):
                high = float(current_day.get('high_price', 0))
                low = float(current_day.get('low_price', 0))
                close = float(current_day.get('close_price', 0))
                
                if i == 0:
                    # 第一天没有前收盘价，只计算高低价差
                    daily_tr = abs(high - low)
                else:
                    prev_close = float(daily_data[i-1].get('close_price', 0))
                    # Oracle: greatest(abs(high-low), abs(high-close_prev), abs(close_prev-low))
                    daily_tr = max(
                        abs(high - low),
                        abs(high - prev_close),
                        abs(prev_close - low)
                    )
                
                current_day['daily_tr'] = daily_tr
            
            # 第二步：计算20日平均真实波幅(ATR_20)
            for i, current_day in enumerate(daily_data):
                if i >= VOLATILITY_CONFIG.get('atr_period', 20) - 1:  # 使用配置中的ATR周期
                    # Oracle逻辑：row_num between rec_d.row_num - 19 and rec_d.row_num
                    start_idx = i - (VOLATILITY_CONFIG.get('atr_period', 20) - 1)  # 包含当前日，共20日
                    end_idx = i + 1
                    
                    period_data = daily_data[start_idx:end_idx]
                    
                    # 计算20日平均真实波幅
                    tr_values = [d.get('daily_tr', 0) for d in period_data]
                    atr_20 = sum(tr_values) / VOLATILITY_CONFIG.get('atr_period', 20) if len(tr_values) == VOLATILITY_CONFIG.get('atr_period', 20) else 0.0
                    
                    volatility_data.append({
                        'stock_code': stock_code,
                        'trade_date': current_day['trade_date'],
                        'daily_tr': current_day['daily_tr'],
                        'atr_20': atr_20
                    })
            
            return volatility_data
            
        except Exception as e:
            self.logger.error(f"计算波动率指标失败 {stock_code}: {e}")
            return []

def main():
    """命令行入口"""
    parser = argparse.ArgumentParser(description='股票技术分析程序')
    parser.add_argument('--test', action='store_true', help='测试模式')
    parser.add_argument('--count', type=int, default=10, help='测试模式下分析的股票数量')
    
    args = parser.parse_args()
    
    analyzer = StockAnalysisMain()
    analyzer.run(test_mode=args.test, test_count=args.count)

if __name__ == "__main__":
    main() 