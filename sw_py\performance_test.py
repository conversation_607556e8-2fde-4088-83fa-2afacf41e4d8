#!/usr/bin/env python3
"""
性能测试脚本 - 分析数据处理瓶颈
"""

import sys
import os
import time
import logging
import cProfile
import pstats
from io import StringIO

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(message)s',
    datefmt='%H:%M:%S'
)

logger = logging.getLogger(__name__)

def profile_function(func, *args, **kwargs):
    """性能分析函数"""
    pr = cProfile.Profile()
    pr.enable()
    
    start_time = time.time()
    result = func(*args, **kwargs)
    end_time = time.time()
    
    pr.disable()
    
    # 分析结果
    s = StringIO()
    ps = pstats.Stats(pr, stream=s).sort_stats('cumulative')
    ps.print_stats(20)  # 显示前20个最耗时的函数
    
    logger.info(f"函数 {func.__name__} 执行时间: {end_time - start_time:.2f}秒")
    logger.info("性能分析结果:")
    logger.info(s.getvalue())
    
    return result

def test_database_operations():
    """测试数据库操作性能"""
    logger.info("=== 测试数据库操作性能 ===")
    
    from models.database import DatabaseManager, StockDataModel
    
    # 测试数据库连接
    start_time = time.time()
    db = DatabaseManager()
    stock_model = StockDataModel(db)
    logger.info(f"数据库初始化耗时: {time.time() - start_time:.3f}秒")
    
    # 测试获取股票列表
    start_time = time.time()
    stock_list = stock_model.get_stock_list()
    logger.info(f"获取股票列表({len(stock_list)}只)耗时: {time.time() - start_time:.3f}秒")
    
    # 测试获取单只股票的周线数据
    test_stock = stock_list[0]['gp_num']
    start_time = time.time()
    weekly_data = stock_model.get_weekly_data(test_stock)
    logger.info(f"获取单只股票周线数据({len(weekly_data)}条)耗时: {time.time() - start_time:.3f}秒")
    
    # 测试获取平均交易量数据
    start_time = time.time()
    avg_data = stock_model.get_avg_trade_data(test_stock)
    logger.info(f"获取单只股票平均交易量数据({len(avg_data)}条)耗时: {time.time() - start_time:.3f}秒")
    
    return stock_list, weekly_data, avg_data

def test_calculation_performance():
    """测试计算性能"""
    logger.info("=== 测试计算性能 ===")
    
    from models.database import DatabaseManager, StockDataModel
    from main import StockAnalysisMain
    
    db = DatabaseManager()
    stock_model = StockDataModel(db)
    analyzer = StockAnalysisMain()
    
    # 获取测试数据
    stock_list = stock_model.get_stock_list()[:3]  # 只测试前3只股票
    test_stock = stock_list[0]
    stock_code = test_stock['gp_num']
    
    # 获取周线数据
    weekly_data = stock_model.get_weekly_data(stock_code)
    
    # 测试平均交易量计算
    logger.info(f"测试股票 {stock_code} 的平均交易量计算...")
    start_time = time.time()
    avg_data = analyzer.calculate_avg_trade_volumes(weekly_data, stock_code)
    calc_time = time.time() - start_time
    logger.info(f"平均交易量计算耗时: {calc_time:.3f}秒，生成 {len(avg_data)} 条数据")
    
    # 分析计算复杂度
    data_points = len(weekly_data)
    avg_periods = 4  # [5, 10, 20, 30]
    expected_operations = data_points * avg_periods
    logger.info(f"数据点: {data_points}, 平均周期: {avg_periods}, 预期操作数: {expected_operations}")
    logger.info(f"每操作耗时: {calc_time/expected_operations*1000:.3f}毫秒")
    
    return avg_data

def test_batch_operations():
    """测试批量操作性能"""
    logger.info("=== 测试批量操作性能 ===")
    
    from models.database import DatabaseManager, StockDataModel
    
    db = DatabaseManager()
    stock_model = StockDataModel(db)
    
    # 创建测试数据
    test_data = []
    for i in range(1000):
        test_data.append({
            'stock_code': f'TEST{i:06d}',
            'trade_date': '2023-01-01',
            'avg_days': 5,
            'avg_qty': 100000.0,
            'avg_amount': 1000000.0
        })
    
    # 测试批量插入
    logger.info(f"测试批量插入 {len(test_data)} 条数据...")
    start_time = time.time()
    try:
        result = stock_model.insert_avg_trade_data(test_data)
        insert_time = time.time() - start_time
        logger.info(f"批量插入耗时: {insert_time:.3f}秒，插入 {result} 条")
        logger.info(f"每条数据插入耗时: {insert_time/len(test_data)*1000:.3f}毫秒")
    except Exception as e:
        logger.error(f"批量插入失败: {e}")
    
    # 清理测试数据
    try:
        cleanup_sql = "DELETE FROM stock_avg_trade WHERE stock_code LIKE 'TEST%'"
        db.execute_update(cleanup_sql)
        logger.info("清理测试数据完成")
    except Exception as e:
        logger.error(f"清理测试数据失败: {e}")

def main():
    """主测试函数"""
    logger.info("🔍 开始性能测试")
    
    try:
        # 1. 测试数据库操作
        stock_list, weekly_data, avg_data = test_database_operations()
        
        # 2. 测试计算性能
        profile_function(test_calculation_performance)
        
        # 3. 测试批量操作
        test_batch_operations()
        
        logger.info("✅ 性能测试完成")
        
    except Exception as e:
        logger.error(f"❌ 性能测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
