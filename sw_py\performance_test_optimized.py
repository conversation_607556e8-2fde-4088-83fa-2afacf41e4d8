#!/usr/bin/env python3
"""
优化后的性能测试脚本
"""

import sys
import os
import time
import logging

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(message)s',
    datefmt='%H:%M:%S'
)

logger = logging.getLogger(__name__)

def test_database_optimization():
    """测试数据库优化效果"""
    logger.info("=== 测试数据库优化效果 ===")
    
    from models.database import DatabaseManager, StockDataModel
    
    db = DatabaseManager()
    stock_model = StockDataModel(db)
    
    # 获取测试股票列表
    stock_list = stock_model.get_stock_list()[:20]  # 测试前20只股票
    stock_codes = [stock['gp_num'] for stock in stock_list]
    
    logger.info(f"测试 {len(stock_codes)} 只股票的数据获取性能")
    
    # 测试单独查询 vs 批量查询
    logger.info("--- 单独查询性能 ---")
    start_time = time.time()
    individual_weekly = {}
    individual_avg = {}
    
    for stock_code in stock_codes[:5]:  # 只测试前5只，避免耗时过长
        individual_weekly[stock_code] = stock_model.get_weekly_data(stock_code)
        individual_avg[stock_code] = stock_model.get_avg_trade_data(stock_code)
    
    individual_time = time.time() - start_time
    logger.info(f"单独查询5只股票耗时: {individual_time:.3f}秒")
    
    # 测试批量查询
    logger.info("--- 批量查询性能 ---")
    start_time = time.time()
    batch_weekly = stock_model.get_multiple_weekly_data(stock_codes)
    batch_avg = stock_model.get_multiple_avg_trade_data(stock_codes)
    batch_time = time.time() - start_time
    logger.info(f"批量查询{len(stock_codes)}只股票耗时: {batch_time:.3f}秒")
    
    # 计算性能提升
    if individual_time > 0:
        estimated_individual_time = individual_time * len(stock_codes) / 5
        speedup = estimated_individual_time / batch_time if batch_time > 0 else 0
        logger.info(f"预估单独查询{len(stock_codes)}只股票需要: {estimated_individual_time:.3f}秒")
        logger.info(f"性能提升倍数: {speedup:.2f}x")
    
    return batch_weekly, batch_avg

def test_calculation_optimization():
    """测试计算优化效果"""
    logger.info("=== 测试计算优化效果 ===")
    
    from models.database import DatabaseManager, StockDataModel
    from main import StockAnalysisMain
    
    db = DatabaseManager()
    stock_model = StockDataModel(db)
    analyzer = StockAnalysisMain()
    
    # 获取测试数据
    stock_list = stock_model.get_stock_list()[:10]  # 测试前10只股票
    
    logger.info(f"测试 {len(stock_list)} 只股票的形态分析性能")
    
    # 测试优化后的分析流程
    start_time = time.time()
    results = analyzer.analyze_patterns_multithread(stock_list)
    analysis_time = time.time() - start_time
    
    logger.info(f"形态分析耗时: {analysis_time:.3f}秒")
    logger.info(f"找到形态数量: {len(results)}")
    logger.info(f"平均每只股票分析耗时: {analysis_time/len(stock_list):.3f}秒")
    
    return results

def test_memory_usage():
    """测试内存使用情况"""
    logger.info("=== 测试内存使用情况 ===")
    
    import psutil
    import os
    
    process = psutil.Process(os.getpid())
    
    # 获取初始内存使用
    initial_memory = process.memory_info().rss / 1024 / 1024  # MB
    logger.info(f"初始内存使用: {initial_memory:.2f} MB")
    
    # 执行数据加载
    weekly_data, avg_data = test_database_optimization()
    
    # 获取数据加载后的内存使用
    after_load_memory = process.memory_info().rss / 1024 / 1024  # MB
    logger.info(f"数据加载后内存使用: {after_load_memory:.2f} MB")
    logger.info(f"数据加载内存增长: {after_load_memory - initial_memory:.2f} MB")
    
    # 执行分析
    results = test_calculation_optimization()
    
    # 获取分析后的内存使用
    final_memory = process.memory_info().rss / 1024 / 1024  # MB
    logger.info(f"分析完成后内存使用: {final_memory:.2f} MB")
    logger.info(f"总内存增长: {final_memory - initial_memory:.2f} MB")

def test_concurrent_performance():
    """测试并发性能"""
    logger.info("=== 测试并发性能 ===")
    
    from models.database import DatabaseManager
    import threading
    import concurrent.futures
    
    def query_worker(worker_id):
        """工作线程函数"""
        db = DatabaseManager()
        start_time = time.time()
        
        # 执行一些查询
        for i in range(10):
            result = db.execute_query("SELECT COUNT(*) as count FROM stock_info_a_code_name")
        
        elapsed = time.time() - start_time
        logger.info(f"工作线程 {worker_id} 完成，耗时: {elapsed:.3f}秒")
        return elapsed
    
    # 测试不同并发级别
    for thread_count in [1, 4, 8, 12]:
        logger.info(f"--- 测试 {thread_count} 个并发线程 ---")
        start_time = time.time()
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=thread_count) as executor:
            futures = [executor.submit(query_worker, i) for i in range(thread_count)]
            results = [future.result() for future in concurrent.futures.as_completed(futures)]
        
        total_time = time.time() - start_time
        avg_worker_time = sum(results) / len(results)
        
        logger.info(f"{thread_count} 线程总耗时: {total_time:.3f}秒")
        logger.info(f"平均工作线程耗时: {avg_worker_time:.3f}秒")
        logger.info(f"并发效率: {avg_worker_time/total_time:.2f}")

def main():
    """主测试函数"""
    logger.info("🚀 开始优化后的性能测试")
    
    try:
        # 1. 测试数据库优化
        test_database_optimization()
        
        # 2. 测试计算优化
        test_calculation_optimization()
        
        # 3. 测试内存使用
        test_memory_usage()
        
        # 4. 测试并发性能
        test_concurrent_performance()
        
        logger.info("✅ 优化后的性能测试完成")
        
    except Exception as e:
        logger.error(f"❌ 性能测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
