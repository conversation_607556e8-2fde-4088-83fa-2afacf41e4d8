#!/usr/bin/env python3
"""
重新生成平均交易量数据
"""

import sys
import os
import logging

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

logger = logging.getLogger(__name__)

try:
    from models.database import DatabaseManager, StockDataModel
    from main import StockAnalysisMain
    
    logger.info("🔄 开始重新生成平均交易量数据")
    
    # 初始化数据库
    db_manager = DatabaseManager()
    stock_model = StockDataModel(db_manager)
    
    # 清空现有的平均交易量数据
    logger.info("1. 清空现有平均交易量数据...")
    stock_model.clear_avg_trade_data()
    logger.info("✅ 清空完成")
    
    # 获取股票列表（只测试前5只）
    stock_list = stock_model.get_stock_list()[:5]
    logger.info(f"2. 获取股票列表: {len(stock_list)} 只股票")
    
    # 创建主程序实例
    main_instance = StockAnalysisMain()
    
    # 重新计算平均交易量
    logger.info("3. 重新计算平均交易量...")
    main_instance.calculate_avg_trade_volume_multithread(stock_list)
    logger.info("✅ 平均交易量计算完成")
    
    # 验证结果
    logger.info("4. 验证结果...")
    for stock in stock_list[:2]:  # 只验证前2只
        stock_code = stock['gp_num']
        stock_name = stock['gp_name']
        
        avg_trade_data = stock_model.get_avg_trade_data(stock_code)
        logger.info(f"  股票 {stock_code} ({stock_name}): {len(avg_trade_data)} 条平均交易量数据")
        
        if avg_trade_data:
            # 显示数据时间范围
            dates = [data['trade_date'] for data in avg_trade_data]
            min_date = min(dates)
            max_date = max(dates)
            logger.info(f"    时间范围: {min_date} 到 {max_date}")
            
            # 显示前3条数据
            logger.info("    前3条数据:")
            for i, data in enumerate(avg_trade_data[:3]):
                logger.info(f"      {i+1}: {data['trade_date']}, 5日均量: {data.get('avg_qty', 'N/A')}")
    
    logger.info("🎉 平均交易量数据重新生成完成！")
    
except Exception as e:
    logger.error(f"❌ 重新生成失败: {e}")
    import traceback
    traceback.print_exc()
