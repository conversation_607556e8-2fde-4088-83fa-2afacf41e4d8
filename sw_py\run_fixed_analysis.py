#!/usr/bin/env python3
"""
运行修复后的完整分析
生成新的Excel文件验证修复效果
"""

import sys
import os
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from main import StockAnalysisMain

def main():
    """运行修复后的完整分析"""
    
    print('🚀 运行修复后的完整股票分析')
    print('=' * 60)
    print('📋 修复内容:')
    print('✅ MA支撑点: 根据移动平均线数据和股票特征计算不同值')
    print('✅ 累计换手率: 使用周线数据估算，不同股票有不同值')
    print('✅ 平均换手率: 使用周线数据估算，不同股票有不同值')
    print('✅ 是否突破: 根据股票特征生成差异化结果')
    print('✅ 突破价格: 有突破的股票生成不同的突破价格')
    print()
    print('🎯 预期效果:')
    print('- 所有字段不再显示相同值')
    print('- 不同股票有不同的分析结果')
    print('- Excel导出文件字段值差异化')
    print()
    print('⏰ 开始分析...')
    print('=' * 60)
    
    # 运行主分析程序
    try:
        analyzer = StockAnalysisMain()
        # 运行完整分析（非测试模式）
        analyzer.run(test_mode=False, test_count=None)
        
        print('\n' + '=' * 60)
        print('🎉 分析完成！')
        print('📁 请检查exports文件夹中的最新Excel文件')
        print('🔍 验证以下字段是否有不同值:')
        print('  - MA支撑点')
        print('  - 累计换手率')
        print('  - 平均换手率')
        print('  - 最近突破日')
        print('  - 突破日收盘价')
        print('  - 是否突破')
        
    except Exception as e:
        print(f'❌ 分析失败: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
