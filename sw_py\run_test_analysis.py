#!/usr/bin/env python3
"""
运行测试分析 - 验证修复效果
"""

import sys
import os
import argparse

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def run_test_analysis():
    """运行测试分析"""
    
    print('🚀 运行测试分析验证修复效果')
    print('=' * 80)
    
    try:
        # 导入主程序
        from main import StockAnalysisMain
        
        print('📊 初始化分析器...')
        analyzer = StockAnalysisMain()
        
        print('🔍 运行测试模式分析 (5只股票)...')
        # 运行测试模式，只分析5只股票
        analyzer.run(test_mode=True, test_count=5)
        
        print('✅ 测试分析完成！')
        
        # 检查结果
        print('\n📋 检查分析结果...')
        
        # 查找最新的Excel文件
        import glob
        excel_files = glob.glob('exports/*_stock_analysis_results.xlsx')
        if excel_files:
            latest_file = max(excel_files, key=os.path.getctime)
            print(f'📁 最新Excel文件: {latest_file}')
            
            # 验证Excel内容
            import pandas as pd
            try:
                df = pd.read_excel(latest_file)
                print(f'📊 分析结果: {len(df)} 条记录')
                
                # 检查关键字段
                key_fields = ['MA支撑点', '累计换手率', '平均换手率', '是否突破']
                
                print('\n🔍 字段差异化检查:')
                for field in key_fields:
                    if field in df.columns:
                        unique_values = df[field].nunique()
                        total_values = len(df[field].dropna())
                        print(f'  {field}: {unique_values}/{total_values} 个不同值')
                        
                        if unique_values > 1:
                            print(f'    ✅ 修复成功')
                        else:
                            print(f'    ❌ 修复失败 - 所有值相同')
                    else:
                        print(f'  {field}: ❌ 字段不存在')
                
                return True
                
            except Exception as e:
                print(f'❌ 读取Excel文件失败: {e}')
                return False
        else:
            print('❌ 未找到Excel文件')
            return False
            
    except Exception as e:
        print(f'❌ 运行测试分析失败: {e}')
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print('🧪 测试分析验证修复效果')
    print('=' * 100)
    
    success = run_test_analysis()
    
    print('\n' + '=' * 100)
    print('📋 测试总结:')
    print('=' * 100)
    
    if success:
        print('🎉 测试分析成功完成！')
        print('✅ 修复代码在实际运行中工作正常')
        print('📝 可以运行完整分析生成最终Excel文件')
    else:
        print('❌ 测试分析失败')
        print('🔧 需要进一步调试修复代码')

if __name__ == "__main__":
    main()
