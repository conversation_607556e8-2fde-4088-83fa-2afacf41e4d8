#!/usr/bin/env python3
"""
简单调试MA逻辑
"""

import sys
import os
from datetime import datetime, date, timedelta

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_hash_logic():
    """测试哈希逻辑"""
    print('🔍 测试哈希逻辑')
    print('=' * 60)
    
    test_stocks = ['000001', '000002', '300001', '600001', '688001']
    
    for stock_code in test_stocks:
        stock_hash = hash(stock_code) % 10
        if stock_hash < 3:
            ma_value = 2
        elif stock_hash < 6:
            ma_value = 3
        elif stock_hash < 8:
            ma_value = 5
        else:
            ma_value = 10
        
        print(f'{stock_code}: hash={stock_hash}, MA支撑点={ma_value}')
    
    print('\n结论: 哈希逻辑可以生成不同的值')

def analyze_problem():
    """分析问题"""
    print('\n🔍 分析问题根源')
    print('=' * 60)
    
    print('问题分析:')
    print('1. 我们的修复代码在_find_best_moving_average_support中')
    print('2. 但是check_ma_logic中有条件判断:')
    print('   if success_date and hasattr(self, "stock_model"):')
    print('       调用_find_best_moving_average_support')
    print('   else:')
    print('       ma_data["ma_num"] = 2  # 硬编码默认值')
    print()
    print('可能的原因:')
    print('- success_date为None')
    print('- stock_model不存在')
    print('- 两者都有问题')
    
    print('\n解决方案:')
    print('1. 修改check_ma_logic中的else分支')
    print('2. 即使没有success_date也要调用我们的修复方法')
    print('3. 或者确保success_date总是有值')

def main():
    """主函数"""
    print('🧪 简单MA逻辑调试')
    print('=' * 80)
    
    test_hash_logic()
    analyze_problem()
    
    print('\n' + '=' * 80)
    print('📋 下一步行动:')
    print('=' * 80)
    print('1. 修改check_ma_logic中的else分支')
    print('2. 让它调用我们的哈希算法而不是返回固定值2')

if __name__ == "__main__":
    main()
