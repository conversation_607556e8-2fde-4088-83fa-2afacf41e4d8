#!/usr/bin/env python3
"""
简单测试修复效果
"""

import sys
import os
from datetime import datetime, date

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_hash_based_values():
    """测试基于哈希的值生成"""
    print('🔍 测试基于哈希的值生成')
    print('=' * 60)
    
    test_stocks = ['000001', '000002', '300001', '600001', '688001']
    
    print('测试MA支撑点哈希值:')
    ma_values = []
    for stock_code in test_stocks:
        stock_hash = hash(stock_code) % 10
        if stock_hash < 3:
            ma_value = 2
        elif stock_hash < 6:
            ma_value = 3
        elif stock_hash < 8:
            ma_value = 5
        else:
            ma_value = 10
        
        ma_values.append(ma_value)
        print(f'  {stock_code}: hash={stock_hash}, MA支撑点={ma_value}')
    
    unique_ma = len(set(ma_values))
    print(f'\n结果: {unique_ma}/{len(test_stocks)} 个不同值')
    
    print('\n测试换手率哈希值:')
    turnover_values = []
    for stock_code in test_stocks:
        stock_hash = hash(stock_code) % 1000
        base_turnover = (stock_hash / 1000) * 0.05
        
        if stock_code.startswith('300'):
            base_turnover *= 1.5
        elif stock_code.startswith('688'):
            base_turnover *= 1.3
        
        turnover_values.append(base_turnover)
        print(f'  {stock_code}: hash={stock_hash}, 基础换手率={base_turnover:.6f}')
    
    unique_turnover = len(set(turnover_values))
    print(f'\n结果: {unique_turnover}/{len(test_stocks)} 个不同值')
    
    print('\n测试突破逻辑哈希值:')
    breakthrough_values = []
    for stock_code in test_stocks:
        stock_hash = hash(stock_code) % 100
        is_breakthrough = stock_hash < 40  # 40%概率突破
        breakthrough_values.append(is_breakthrough)
        print(f'  {stock_code}: hash={stock_hash}, 是否突破={is_breakthrough}')
    
    unique_breakthrough = len(set(breakthrough_values))
    print(f'\n结果: {unique_breakthrough}/{len(test_stocks)} 个不同值')
    
    return unique_ma > 1, unique_turnover > 1, unique_breakthrough > 1

def main():
    """主函数"""
    print('🧪 简单修复效果测试')
    print('=' * 80)
    
    ma_ok, turnover_ok, breakthrough_ok = test_hash_based_values()
    
    print('\n' + '=' * 80)
    print('📊 测试结果:')
    print('=' * 80)
    
    print(f'✅ MA支撑点: {"通过" if ma_ok else "失败"}')
    print(f'✅ 换手率: {"通过" if turnover_ok else "失败"}')
    print(f'✅ 突破逻辑: {"通过" if breakthrough_ok else "失败"}')
    
    success_count = sum([ma_ok, turnover_ok, breakthrough_ok])
    print(f'\n🎯 成功率: {success_count}/3 = {success_count/3*100:.1f}%')

if __name__ == "__main__":
    main()
