#!/usr/bin/env python3
"""
简化版U型右侧高点失败分析
"""

import sys
import os
import logging

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models.database import DatabaseManager, StockDataModel
from analysis.pattern_analyzer import <PERSON><PERSON><PERSON>nalyzer
from config.analysis_config import U_PATTERN_CONFIG, V_PATTERN_CONFIG, VOLUME_CONFIG

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def analyze_u_right_simple():
    """简化分析U型右侧高点失败原因"""
    
    db = DatabaseManager()
    stock_model = StockDataModel(db)
    analyzer = PatternAnalyzer(U_PATTERN_CONFIG, V_PATTERN_CONFIG, VOLUME_CONFIG)
    
    # 分析结果统计
    results = {
        'total_left_points': 0,
        'price_range_no_match': 0,
        'price_range_match_but_volume_fail': 0,
        'found_right_points': 0,
        'avg_candidates_per_left': 0,
        'avg_price_matches_per_left': 0,
        'avg_volume_matches_per_left': 0
    }
    
    detailed_examples = []
    
    # 分析前50只股票
    stock_list = stock_model.get_stock_list()[:50]
    
    for i, stock in enumerate(stock_list):
        stock_code = stock['gp_num']
        
        try:
            weekly_data = stock_model.get_weekly_data(stock_code)
            avg_trade_data = stock_model.get_avg_trade_data(stock_code)
            
            if len(weekly_data) < 20:
                continue
            
            # 找到U型左侧高点
            for j in range(U_PATTERN_CONFIG['pre_month'], min(len(weekly_data), 30)):
                current_high = float(weekly_data[j]['high_price'])
                if analyzer.check_u_left_strict(weekly_data, j, current_high):
                    results['total_left_points'] += 1
                    
                    # 分析这个左侧高点为什么找不到右侧高点
                    analysis = analyze_single_left_point(
                        stock_code, weekly_data, avg_trade_data, j, current_high
                    )
                    
                    # 累计统计
                    results['avg_candidates_per_left'] += analysis['candidates_checked']
                    results['avg_price_matches_per_left'] += analysis['price_matches']
                    results['avg_volume_matches_per_left'] += analysis['volume_matches']
                    
                    if analysis['price_matches'] == 0:
                        results['price_range_no_match'] += 1
                    elif analysis['volume_matches'] == 0:
                        results['price_range_match_but_volume_fail'] += 1
                    else:
                        results['found_right_points'] += 1
                    
                    # 收集详细案例
                    if len(detailed_examples) < 10:
                        detailed_examples.append({
                            'stock_code': stock_code,
                            'left_idx': j,
                            'left_price': current_high,
                            'analysis': analysis
                        })
                    
                    break  # 每只股票只分析第一个左侧高点
        
        except Exception as e:
            logger.error(f'分析股票 {stock_code} 失败: {e}')
            continue
        
        if i % 10 == 0:
            logger.info(f'进度: {i}/50')
    
    # 计算平均值
    if results['total_left_points'] > 0:
        results['avg_candidates_per_left'] /= results['total_left_points']
        results['avg_price_matches_per_left'] /= results['total_left_points']
        results['avg_volume_matches_per_left'] /= results['total_left_points']
    
    # 打印分析结果
    print_simple_analysis_results(results, detailed_examples)

def analyze_single_left_point(stock_code, weekly_data, avg_trade_data, left_idx, left_price):
    """分析单个左侧高点的右侧匹配情况"""
    
    left_price_float = float(left_price)
    price_range_low = left_price_float * (1 - U_PATTERN_CONFIG['lr_diff_rate'])
    price_range_high = left_price_float * (1 + U_PATTERN_CONFIG['lr_diff_rate'])
    
    analysis = {
        'candidates_checked': 0,
        'price_matches': 0,
        'volume_matches': 0,
        'volume_data_available': 0
    }
    
    # 检查左侧高点之后的20周数据
    for i in range(left_idx + 1, min(left_idx + 21, len(weekly_data))):
        current_data = weekly_data[i]
        high_price = float(current_data['high_price'])
        max_price = max(float(current_data['open_price']), float(current_data['close_price']))
        
        analysis['candidates_checked'] += 1
        
        # 检查价格是否在范围内
        price_in_range = (price_range_low <= high_price <= price_range_high) or \
                        (price_range_low <= max_price <= price_range_high)
        
        if price_in_range:
            analysis['price_matches'] += 1
            
            # 检查交易量条件
            current_date = current_data['trade_date']
            current_volume = current_data['jy_quantity']
            
            # 查找对应的5日平均交易量
            avg_volume = None
            for avg_data in avg_trade_data:
                if avg_data['trade_date'] == current_date and avg_data['avg_days'] == 5:
                    avg_volume = float(avg_data['avg_qty'])
                    analysis['volume_data_available'] += 1
                    break
            
            if avg_volume:
                volume_threshold = avg_volume * U_PATTERN_CONFIG['volume_amplify_rate']
                if current_volume >= volume_threshold:
                    analysis['volume_matches'] += 1
    
    return analysis

def print_simple_analysis_results(results, detailed_examples):
    """打印简化分析结果"""
    
    total = results['total_left_points']
    
    print('\n' + '='*60)
    print('📊 U型右侧高点失败原因简化分析')
    print('='*60)
    
    print(f'\n📈 总体统计 (分析了{total}个左侧高点):')
    print(f'  价格范围完全不匹配: {results["price_range_no_match"]} ({results["price_range_no_match"]/total:.1%})')
    print(f'  价格匹配但交易量失败: {results["price_range_match_but_volume_fail"]} ({results["price_range_match_but_volume_fail"]/total:.1%})')
    print(f'  成功找到右侧高点: {results["found_right_points"]} ({results["found_right_points"]/total:.1%})')
    
    print(f'\n📊 平均统计:')
    print(f'  每个左侧高点平均检查候选: {results["avg_candidates_per_left"]:.1f}个')
    print(f'  每个左侧高点平均价格匹配: {results["avg_price_matches_per_left"]:.1f}个')
    print(f'  每个左侧高点平均交易量匹配: {results["avg_volume_matches_per_left"]:.1f}个')
    
    print(f'\n🔍 详细案例分析:')
    for i, example in enumerate(detailed_examples[:5]):
        analysis = example['analysis']
        print(f'\n案例{i+1}: {example["stock_code"]} (左侧第{example["left_idx"]}周, 价格{example["left_price"]:.2f})')
        print(f'  检查候选: {analysis["candidates_checked"]}个')
        print(f'  价格匹配: {analysis["price_matches"]}个')
        print(f'  交易量匹配: {analysis["volume_matches"]}个')
        print(f'  有交易量数据: {analysis["volume_data_available"]}个')
    
    # 分析主要问题
    price_fail_rate = results['price_range_no_match'] / total * 100
    volume_fail_rate = results['price_range_match_but_volume_fail'] / total * 100
    
    print(f'\n💡 关键发现:')
    if price_fail_rate > 50:
        print(f'   - 价格范围匹配失败是主要问题({price_fail_rate:.1f}%)，±3%的范围过于严格')
    if volume_fail_rate > 30:
        print(f'   - 交易量条件是重要瓶颈({volume_fail_rate:.1f}%)，1.05倍要求可能过严')
    
    avg_price_match_rate = results['avg_price_matches_per_left'] / results['avg_candidates_per_left'] * 100
    avg_volume_match_rate = results['avg_volume_matches_per_left'] / max(results['avg_price_matches_per_left'], 1) * 100
    
    print(f'   - 价格匹配率: {avg_price_match_rate:.1f}%')
    print(f'   - 交易量匹配率(在价格匹配基础上): {avg_volume_match_rate:.1f}%')

def test_relaxed_conditions_simple():
    """测试放宽条件的简单版本"""
    
    print(f'\n🧪 测试放宽条件的效果:')
    
    db = DatabaseManager()
    stock_model = StockDataModel(db)
    
    test_configs = [
        {'lr_diff_rate': 0.05, 'volume_amplify_rate': 1.05, 'name': '价格±5%'},
        {'lr_diff_rate': 0.08, 'volume_amplify_rate': 1.05, 'name': '价格±8%'},
        {'lr_diff_rate': 0.03, 'volume_amplify_rate': 1.02, 'name': '交易量1.02倍'},
        {'lr_diff_rate': 0.05, 'volume_amplify_rate': 1.02, 'name': '价格±5%+交易量1.02倍'},
    ]
    
    test_stocks = ['000001', '000002', '600000', '600036', '000858']
    
    for config in test_configs:
        print(f'\n--- 测试配置: {config["name"]} ---')
        
        # 创建修改后的配置
        test_u_config = U_PATTERN_CONFIG.copy()
        test_u_config['lr_diff_rate'] = config['lr_diff_rate']
        test_u_config['volume_amplify_rate'] = config['volume_amplify_rate']
        
        analyzer = PatternAnalyzer(test_u_config, V_PATTERN_CONFIG, VOLUME_CONFIG)
        
        success_count = 0
        for stock_code in test_stocks:
            weekly_data = stock_model.get_weekly_data(stock_code)
            avg_trade_data = stock_model.get_avg_trade_data(stock_code)
            
            if len(weekly_data) < 20:
                continue
            
            result = analyzer.analyze_stock_patterns_strict(stock_code, f'股票{stock_code}', weekly_data, avg_trade_data)
            if result:
                success_count += 1
                print(f'    ✅ {stock_code}: 找到形态')
        
        print(f'  成功率: {success_count}/{len(test_stocks)} ({success_count/len(test_stocks):.1%})')

def main():
    """主函数"""
    logger.info('🔍 开始简化版U型右侧高点失败分析')
    
    try:
        analyze_u_right_simple()
        test_relaxed_conditions_simple()
        
        logger.info('✅ 简化分析完成')
        
    except Exception as e:
        logger.error(f'❌ 分析失败: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
