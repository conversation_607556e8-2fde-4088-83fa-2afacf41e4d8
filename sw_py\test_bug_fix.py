#!/usr/bin/env python3
"""
测试修复后的U型右侧高点逻辑
"""

import sys
import os
import logging

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models.database import DatabaseManager, StockDataModel
from analysis.pattern_analyzer import PatternAnalyzer
from config.analysis_config import U_PATTERN_CONFIG, V_PATTERN_CONFIG, VOLUME_CONFIG

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_bug_fix():
    """测试修复后的效果"""
    
    print('🔧 测试修复U型右侧高点逻辑后的效果')
    
    db = DatabaseManager()
    stock_model = StockDataModel(db)
    analyzer = PatternAnalyzer(U_PATTERN_CONFIG, V_PATTERN_CONFIG, VOLUME_CONFIG)
    
    # 测试股票
    test_stocks = ['000001', '000002', '600000', '600036', '000858', '000012', '000065']
    
    print(f'\n📊 测试结果:')
    success_count = 0
    
    for stock_code in test_stocks:
        print(f'\n--- 测试股票 {stock_code} ---')
        
        # 获取股票名称
        stock_info = db.execute_query("SELECT name FROM stock_info_a_code_name WHERE code = %s", (stock_code,))
        stock_name = stock_info[0]['name'] if stock_info else stock_code
        
        # 获取数据
        weekly_data = stock_model.get_weekly_data(stock_code)
        avg_trade_data = stock_model.get_avg_trade_data(stock_code)
        
        print(f'数据量: 周线{len(weekly_data)}条, 平均交易量{len(avg_trade_data)}条')
        
        if len(weekly_data) < 20:
            print('❌ 数据不足')
            continue
        
        # 执行分析
        result = analyzer.analyze_stock_patterns_strict(stock_code, stock_name, weekly_data, avg_trade_data)
        
        if result:
            success_count += 1
            print(f'✅ 找到形态: {result}')
        else:
            print('❌ 未找到形态')
            
            # 显示详细的筛选统计
            stats = analyzer.filter_stats
            print(f'   筛选统计: U左侧{stats.get("u_left_valid", 0)}, U右侧{stats.get("u_right_valid", 0)}, U底部{stats.get("u_bottom_valid", 0)}')
    
    print(f'\n📈 总体结果:')
    print(f'  成功找到形态: {success_count}/{len(test_stocks)} ({success_count/len(test_stocks):.1%})')
    
    if success_count > 0:
        print(f'  🎉 修复成功！现在能够找到股票形态了')
    else:
        print(f'  ⚠️  仍需进一步调试')

def test_large_sample_after_fix():
    """修复后测试大样本"""
    
    print(f'\n🔍 修复后大样本测试 (100只股票):')
    
    db = DatabaseManager()
    stock_model = StockDataModel(db)
    analyzer = PatternAnalyzer(U_PATTERN_CONFIG, V_PATTERN_CONFIG, VOLUME_CONFIG)
    
    # 获取前100只股票
    stock_list = stock_model.get_stock_list()[:100]
    
    stats = {
        'total': 0,
        'insufficient_data': 0,
        'u_left_failed': 0,
        'u_right_failed': 0,
        'u_bottom_failed': 0,
        'success': 0
    }
    
    success_cases = []
    
    for i, stock in enumerate(stock_list):
        stock_code = stock['gp_num']
        stock_name = stock['gp_name']
        
        stats['total'] += 1
        
        try:
            weekly_data = stock_model.get_weekly_data(stock_code)
            avg_trade_data = stock_model.get_avg_trade_data(stock_code)
            
            if len(weekly_data) < 20:
                stats['insufficient_data'] += 1
                continue
            
            result = analyzer.analyze_stock_patterns_strict(stock_code, stock_name, weekly_data, avg_trade_data)
            
            if result:
                stats['success'] += 1
                success_cases.append({
                    'stock_code': stock_code,
                    'stock_name': stock_name,
                    'result': result
                })
                print(f'✅ {stock_code}({stock_name}): {result}')
            else:
                # 分析失败原因
                filter_stats = analyzer.filter_stats
                u_left = filter_stats.get('u_left_valid', 0)
                u_right = filter_stats.get('u_right_valid', 0)
                u_bottom = filter_stats.get('u_bottom_valid', 0)
                
                if u_left == 0:
                    stats['u_left_failed'] += 1
                elif u_right == 0:
                    stats['u_right_failed'] += 1
                else:
                    stats['u_bottom_failed'] += 1
        
        except Exception as e:
            logger.error(f'分析股票 {stock_code} 失败: {e}')
            continue
        
        if i % 20 == 0:
            logger.info(f'进度: {i}/100')
    
    # 打印结果
    total = stats['total']
    print(f'\n📊 修复后大样本测试结果:')
    print(f'  总计: {total}只')
    print(f'  数据不足: {stats["insufficient_data"]} ({stats["insufficient_data"]/total:.1%})')
    print(f'  U型左侧失败: {stats["u_left_failed"]} ({stats["u_left_failed"]/total:.1%})')
    print(f'  U型右侧失败: {stats["u_right_failed"]} ({stats["u_right_failed"]/total:.1%})')
    print(f'  U型底部失败: {stats["u_bottom_failed"]} ({stats["u_bottom_failed"]/total:.1%})')
    print(f'  ✅ 成功: {stats["success"]} ({stats["success"]/total:.1%})')
    
    if success_cases:
        print(f'\n🎯 成功案例:')
        for case in success_cases[:5]:  # 显示前5个成功案例
            print(f'  {case["stock_code"]}({case["stock_name"]}): {case["result"]}')
    
    # 对比修复前后的效果
    print(f'\n📈 修复效果对比:')
    print(f'  修复前U型右侧失败率: 89.8%')
    print(f'  修复后U型右侧失败率: {stats["u_right_failed"]/total:.1%}')
    
    improvement = 89.8 - (stats["u_right_failed"]/total * 100)
    if improvement > 0:
        print(f'  🎉 改善了 {improvement:.1f} 个百分点！')
    
    success_rate = stats["success"]/total * 100
    if success_rate > 0:
        print(f'  🎯 整体成功率: {success_rate:.1f}%')

def main():
    """主函数"""
    logger.info('🔧 开始测试修复后的U型右侧高点逻辑')
    
    try:
        test_bug_fix()
        test_large_sample_after_fix()
        
        logger.info('✅ 修复测试完成')
        
    except Exception as e:
        logger.error(f'❌ 测试失败: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
