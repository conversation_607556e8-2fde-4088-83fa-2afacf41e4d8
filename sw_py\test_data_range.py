#!/usr/bin/env python3
"""
测试数据时间范围
"""

import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models.database import DatabaseManager, StockDataModel

db = DatabaseManager()
stock_model = StockDataModel(db)

# 测试原始查询
print("=== 测试原始查询（无时间过滤） ===")
sql_original = """
SELECT trade_date, high_price
FROM stock_weekly_data
WHERE stock_code = %s
ORDER BY trade_date
LIMIT 10
"""
original_data = db.execute_query(sql_original, ('000001',))
print(f"原始数据条数（前10条）: {len(original_data)}")
for i, data in enumerate(original_data):
    print(f"  {i+1}: {data['trade_date']} - {data['high_price']}")

print("\n=== 测试时间过滤查询 ===")
sql_filtered = """
SELECT trade_date, high_price
FROM stock_weekly_data
WHERE stock_code = %s
  AND trade_date >= '2013-01-01'
ORDER BY trade_date
LIMIT 10
"""
filtered_data = db.execute_query(sql_filtered, ('000001',))
print(f"过滤后数据条数（前10条）: {len(filtered_data)}")
for i, data in enumerate(filtered_data):
    print(f"  {i+1}: {data['trade_date']} - {data['high_price']}")

print("\n=== 测试get_weekly_data方法 ===")
weekly_data = stock_model.get_weekly_data('000001')
print(f"get_weekly_data返回条数: {len(weekly_data)}")
if weekly_data:
    print(f"最早日期: {weekly_data[0]['trade_date']}")
    print(f"最晚日期: {weekly_data[-1]['trade_date']}")
    print("前5条数据:")
    for i, data in enumerate(weekly_data[:5]):
        print(f"  {i+1}: {data['trade_date']} - {data['high_price']}")
