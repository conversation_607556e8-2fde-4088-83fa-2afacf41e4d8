#!/usr/bin/env python3

from models.database import DatabaseManager
from analysis.pattern_analyzer import PatternAnalyzer
from config.analysis_config import U_PATTERN_CONFIG, V_PATTERN_CONFIG, VOLUME_CONFIG
import logging

logging.basicConfig(level=logging.INFO)

# 获取数据
db = DatabaseManager()
weekly_data = db.execute_query('SELECT * FROM stock_weekly_data WHERE stock_code = %s ORDER BY trade_date LIMIT 30', ('000001',))

print('=== 周线数据样本 ===')
for i, data in enumerate(weekly_data[:5]):
    print(f'{i}: 日期{data["trade_date"]}, 高价{data["high_price"]}, 低价{data["low_price"]}')

# 测试U型左侧高点检查
analyzer = PatternAnalyzer(U_PATTERN_CONFIG, V_PATTERN_CONFIG, VOLUME_CONFIG)

print('\n=== 测试U型左侧高点检查 ===')
print(f'前段周期: {U_PATTERN_CONFIG["pre_month"]}')
print(f'左侧涨幅限制: {U_PATTERN_CONFIG["left_diff_rate"]}')

# 手动测试第15个数据点
if len(weekly_data) > 15:
    test_idx = 15
    current_high = float(weekly_data[test_idx]['high_price'])
    print(f'\n测试第{test_idx}个数据点: 高价{current_high}')
    
    # 检查前10周是否有超过阈值的
    start_idx = max(0, test_idx - 10)
    threshold = current_high * (1 + U_PATTERN_CONFIG['left_diff_rate'])
    print(f'阈值 = {current_high} * {1 + U_PATTERN_CONFIG["left_diff_rate"]} = {threshold}')
    
    exceeds_count = 0
    for i in range(start_idx, test_idx):
        high_price = float(weekly_data[i]['high_price'])
        if high_price > threshold:
            exceeds_count += 1
            print(f'  第{i}周超过阈值: {high_price} > {threshold}')
    
    print(f'前10周中有{exceeds_count}周超过阈值')
    
    result = analyzer.check_u_left_strict(weekly_data, test_idx, current_high)
    print(f'U型左侧高点检查结果: {result}')

# 测试多个数据点
print('\n=== 测试多个数据点 ===')
valid_left_points = 0
for i in range(10, min(len(weekly_data), 25)):
    current_high = float(weekly_data[i]['high_price'])
    result = analyzer.check_u_left_strict(weekly_data, i, current_high)
    if result:
        valid_left_points += 1
        print(f'✓ 第{i}周是有效U型左侧高点: {weekly_data[i]["trade_date"]} 价格{current_high}')

print(f'\n总共找到{valid_left_points}个有效U型左侧高点')

# 检查价格范围
print('\n=== 价格范围分析 ===')
prices = [float(data['high_price']) for data in weekly_data]
print(f'价格范围: {min(prices):.2f} ~ {max(prices):.2f}')
print(f'平均价格: {sum(prices)/len(prices):.2f}')

# 检查价格变化幅度
for i in range(1, min(len(weekly_data), 10)):
    prev_price = float(weekly_data[i-1]['high_price'])
    curr_price = float(weekly_data[i]['high_price'])
    change_rate = (curr_price - prev_price) / prev_price
    print(f'第{i}周价格变化: {prev_price:.2f} -> {curr_price:.2f} ({change_rate:.2%})')
