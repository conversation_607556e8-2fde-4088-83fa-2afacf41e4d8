#!/usr/bin/env python3
"""
最终测试 - 验证修复效果
"""

import sys
import os
import logging

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(message)s',
    datefmt='%H:%M:%S'
)

logger = logging.getLogger(__name__)

try:
    from models.database import DatabaseManager, StockDataModel
    from analysis.pattern_analyzer import PatternAnalyzer
    from config.analysis_config import U_PATTERN_CONFIG, V_PATTERN_CONFIG, VOLUME_CONFIG
    
    logger.info("🔍 最终测试开始")
    
    # 初始化
    db = DatabaseManager()
    stock_model = StockDataModel(db)
    
    # 测试前3只股票
    stock_list = stock_model.get_stock_list()[:3]
    
    logger.info(f"测试股票数量: {len(stock_list)}")
    
    # 创建分析器
    analyzer = PatternAnalyzer(U_PATTERN_CONFIG, V_PATTERN_CONFIG, VOLUME_CONFIG)
    
    success_count = 0
    
    for stock in stock_list:
        stock_code = stock['gp_num']
        stock_name = stock['gp_name']
        
        logger.info(f"分析股票: {stock_code} ({stock_name})")
        
        # 获取数据
        weekly_data = stock_model.get_weekly_data(stock_code)
        avg_trade_data = stock_model.get_avg_trade_data(stock_code)
        
        if len(weekly_data) < 20 or len(avg_trade_data) == 0:
            logger.info(f"  ⚠️ 数据不足，跳过")
            continue
        
        # 执行分析
        result = analyzer.analyze_uv_pattern_combined(stock_code, stock_name, weekly_data, avg_trade_data)
        
        if result:
            logger.info(f"  ✅ 发现形态: {result.get('pattern_type', '未知')}")
            success_count += 1
        else:
            logger.info(f"  ❌ 未发现形态")
    
    logger.info(f"\n=== 测试结果 ===")
    logger.info(f"成功发现形态的股票数: {success_count}")
    logger.info(f"总测试股票数: {len(stock_list)}")
    
    # 打印详细统计
    stats = analyzer.filter_stats
    logger.info(f"\n=== 详细统计 ===")
    logger.info(f"总分析次数: {stats.get('total_analyzed', 0)}")
    logger.info(f"U型形态尝试: {stats.get('u_pattern_attempts', 0)}")
    logger.info(f"V型形态尝试: {stats.get('v_pattern_attempts', 0)}")
    logger.info(f"成功次数: {stats.get('success_count', 0)}")
    
    # 检查失败原因
    u_left_failed = 0
    u_right_failed = 0
    for key, value in stats.items():
        if 'u_left' in key and 'failed' in key:
            u_left_failed += value
        elif 'u_right' in key and 'failed' in key:
            u_right_failed += value
    
    logger.info(f"U型左侧高点失败: {u_left_failed}")
    logger.info(f"U型右侧高点失败: {u_right_failed}")
    
    if success_count > 0:
        logger.info("🎉 测试成功！发现了形态")
    else:
        logger.info("⚠️ 未发现任何形态，但程序运行正常")
    
    logger.info("✅ 测试完成")
    
except Exception as e:
    logger.error(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()
