#!/usr/bin/env python3
"""
验证数据丢失修复的测试脚本
测试之前丢失的股票现在是否能被正确处理
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models.database import DatabaseManager, StockDataModel
from utils.incremental_manager import IncrementalDataManager
from config.analysis_config import DATA_FILTER_CONFIG
from analysis.pattern_analyzer import PatternAnalyzer
from config.analysis_config import U_PATTERN_CONFIG, V_PATTERN_CONFIG, VOLUME_CONFIG

def test_lost_stocks_recovery():
    """测试之前丢失的股票是否能被恢复"""
    
    # 之前丢失的股票代码（前10个）
    lost_codes = [
        '000582', '000626', '000712', '000876', '000998',
        '001322', '002204', '002237', '002293', '002299'
    ]
    
    print("=== 验证数据丢失修复 ===")
    print(f"测试股票: {', '.join(lost_codes)}")
    
    db = DatabaseManager()
    stock_model = StockDataModel(db)
    incremental_manager = IncrementalDataManager(db)
    analyzer = PatternAnalyzer(U_PATTERN_CONFIG, V_PATTERN_CONFIG, VOLUME_CONFIG)
    
    print(f"\n增量处理状态: {incremental_manager.should_use_incremental()}")
    if incremental_manager.should_use_incremental():
        start_date, end_date = incremental_manager.get_incremental_date_range('weekly_data')
        print(f"增量处理范围: {start_date} ~ {end_date}")
    
    recovered_count = 0
    failed_count = 0
    
    for stock_code in lost_codes:
        print(f"\n测试股票: {stock_code}")
        
        try:
            # 模拟修复后的数据获取逻辑
            if incremental_manager.should_use_incremental():
                start_date, _ = incremental_manager.get_incremental_date_range('weekly_data')
                start_date_str = start_date.strftime('%Y-%m-%d')
                
                # 先尝试获取增量数据
                daily_data = stock_model.get_stock_daily_data(stock_code, start_date_str)
                print(f"  增量数据: {len(daily_data)}条")
                
                # 如果增量数据不足，回退到全量数据
                if len(daily_data) < DATA_FILTER_CONFIG.get('min_daily_data', 50):
                    fallback_start_date = DATA_FILTER_CONFIG.get('start_date', '2013-01-01')
                    daily_data = stock_model.get_stock_daily_data(stock_code, fallback_start_date)
                    print(f"  回退到全量数据: {len(daily_data)}条")
            else:
                start_date_str = DATA_FILTER_CONFIG.get('start_date', '2013-01-01')
                daily_data = stock_model.get_stock_daily_data(stock_code, start_date_str)
                print(f"  全量数据: {len(daily_data)}条")
            
            # 检查数据是否充足
            min_required = DATA_FILTER_CONFIG.get('min_daily_data', 50)
            if len(daily_data) >= min_required:
                print(f"  ✅ 数据充足: {len(daily_data)} >= {min_required}")
                
                # 进一步测试周线数据和平均交易量数据
                weekly_data = stock_model.get_weekly_data(stock_code)
                avg_trade_data = stock_model.get_avg_trade_data(stock_code)
                
                print(f"  周线数据: {len(weekly_data)}条")
                print(f"  平均交易量数据: {len(avg_trade_data)}条")
                
                # 检查是否满足分析条件
                if (len(weekly_data) >= DATA_FILTER_CONFIG.get('min_weekly_data', 20) and 
                    len(avg_trade_data) >= 5):
                    print(f"  ✅ 满足分析条件")
                    recovered_count += 1
                else:
                    print(f"  ❌ 不满足分析条件")
                    failed_count += 1
            else:
                print(f"  ❌ 数据不足: {len(daily_data)} < {min_required}")
                failed_count += 1
                
        except Exception as e:
            print(f"  ❌ 处理失败: {e}")
            failed_count += 1
    
    print(f"\n=== 测试结果 ===")
    print(f"总测试股票: {len(lost_codes)}")
    print(f"成功恢复: {recovered_count}")
    print(f"仍然失败: {failed_count}")
    print(f"恢复率: {recovered_count/len(lost_codes)*100:.1f}%")
    
    if recovered_count > 0:
        print(f"\n✅ 修复成功！{recovered_count}个之前丢失的股票现在可以正常处理")
    else:
        print(f"\n❌ 修复失败！所有股票仍然无法处理")
    
    return recovered_count, failed_count

if __name__ == "__main__":
    test_lost_stocks_recovery()
