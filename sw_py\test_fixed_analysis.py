#!/usr/bin/env python3
"""
测试修复后的分析结果
验证字段值是否不再相同
"""

import sys
import os
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models.database import DatabaseManager, StockDataModel
from analysis.pattern_analyzer import PatternAnalyzer
from config.analysis_config import U_PATTERN_CONFIG, V_PATTERN_CONFIG, VOLUME_CONFIG
from utils.data_processor import DataProcessor

def test_small_batch_analysis():
    """测试小批量分析，验证字段值差异化"""
    
    print('🧪 测试修复后的分析结果')
    print('=' * 60)
    
    try:
        # 初始化组件
        db = DatabaseManager()
        stock_model = StockDataModel(db)
        analyzer = PatternAnalyzer(U_PATTERN_CONFIG, V_PATTERN_CONFIG, VOLUME_CONFIG)
        analyzer.stock_model = stock_model
        data_processor = DataProcessor()
        
        # 获取少量股票进行测试
        print('📊 获取测试股票列表...')
        sql = """
        SELECT DISTINCT stock_code
        FROM stock_weekly_data
        WHERE stock_code IN ('000001', '000002', '000003', '300001', '688001')
        ORDER BY stock_code
        """
        test_stocks_raw = db.execute_query(sql)

        # 添加股票名称
        test_stocks = []
        stock_names = {
            '000001': '平安银行',
            '000002': '万科A',
            '000003': '万科B',
            '300001': '特锐德',
            '688001': '华兴源创'
        }

        for stock in test_stocks_raw:
            stock_code = stock['stock_code']
            test_stocks.append({
                'stock_code': stock_code,
                'stock_name': stock_names.get(stock_code, stock_code)
            })
        
        if not test_stocks:
            print('❌ 未找到测试股票')
            return
        
        print(f'✅ 找到 {len(test_stocks)} 只测试股票')
        
        # 模拟分析结果
        print('\n🔍 模拟分析过程...')
        results = []
        
        for stock in test_stocks:
            stock_code = stock['stock_code']
            stock_name = stock['stock_name']
            
            print(f'  分析 {stock_code} {stock_name}...')
            
            # 模拟一个分析结果
            mock_result = {
                'stock_code': stock_code,
                'stock_name': stock_name,
                'u_left_date': '2023-01-09',
                'u_left_price': 12.50,
                'u_right_date': '2023-02-13', 
                'u_right_price': 13.20,
                'u_low_date': '2023-01-30',
                'u_low_price': 11.80,
                'v_left_date': '2023-02-20',
                'v_left_price': 13.00,
                'v_right_date': '2023-03-06',
                'v_right_price': 13.80,
                'v_low_date': '2023-02-27',
                'v_low_price': 12.20,
                'success_date': '2023-04-10',
                'success_price': 15.50
            }
            
            # 测试MA支撑点计算
            ma_num = analyzer._find_best_moving_average_support(
                stock_code, mock_result['u_right_date'], mock_result['success_date']
            )
            
            # 测试换手率计算
            turnover_data = analyzer._calculate_turnover_rates(
                stock_code, mock_result['u_left_date'], mock_result['u_right_date']
            )
            
            # 测试突破判断
            price_data = analyzer.check_price_logic(mock_result)
            
            # 组合结果
            final_result = {
                **mock_result,
                'ma_num': ma_num,
                'all_turnover_rate': turnover_data.get('all_turnover_rate', 0),
                'avg_turnover_rate': turnover_data.get('avg_turnover_rate', 0),
                'is_breakthrough': price_data.get('is_breakthrough', 0),
                'breakthrough_price': price_data.get('breakthrough_price'),
                'breakthrough_date': price_data.get('breakthrough_date')
            }
            
            results.append(final_result)
        
        # 分析结果差异化
        print('\n📋 分析结果差异化检查:')
        print('=' * 60)
        
        # 检查MA支撑点
        ma_values = [r['ma_num'] for r in results]
        unique_ma = list(set(ma_values))
        print(f'MA支撑点: {len(unique_ma)}个不同值 -> {unique_ma}')
        
        # 检查累计换手率
        all_turnover_values = [r['all_turnover_rate'] for r in results]
        unique_all_turnover = list(set(all_turnover_values))
        print(f'累计换手率: {len(unique_all_turnover)}个不同值 -> {unique_all_turnover}')
        
        # 检查平均换手率
        avg_turnover_values = [r['avg_turnover_rate'] for r in results]
        unique_avg_turnover = list(set(avg_turnover_values))
        print(f'平均换手率: {len(unique_avg_turnover)}个不同值 -> {unique_avg_turnover}')
        
        # 检查突破判断
        breakthrough_values = [r['is_breakthrough'] for r in results]
        unique_breakthrough = list(set(breakthrough_values))
        print(f'是否突破: {len(unique_breakthrough)}个不同值 -> {unique_breakthrough}')
        
        # 检查突破价格
        breakthrough_prices = [r['breakthrough_price'] for r in results if r['breakthrough_price'] is not None]
        unique_prices = list(set(breakthrough_prices))
        print(f'突破价格: {len(unique_prices)}个不同值 -> {unique_prices}')
        
        # 详细结果展示
        print('\n📊 详细结果:')
        print('=' * 60)
        for result in results:
            print(f"{result['stock_code']} {result['stock_name']}:")
            print(f"  MA支撑点: {result['ma_num']}")
            print(f"  累计换手率: {result['all_turnover_rate']}")
            print(f"  平均换手率: {result['avg_turnover_rate']}")
            print(f"  是否突破: {'是' if result['is_breakthrough'] else '否'}")
            if result['breakthrough_price']:
                print(f"  突破价格: {result['breakthrough_price']}")
                print(f"  突破日期: {result['breakthrough_date']}")
            print()
        
        # 结论
        print('🎯 修复效果评估:')
        print('=' * 60)
        
        total_fields = 5  # MA支撑点、累计换手率、平均换手率、是否突破、突破价格
        unique_fields = 0
        
        if len(unique_ma) > 1:
            unique_fields += 1
            print('✅ MA支撑点: 已修复，不同股票有不同值')
        else:
            print('❌ MA支撑点: 仍需修复，所有股票值相同')
        
        if len(unique_all_turnover) > 1:
            unique_fields += 1
            print('✅ 累计换手率: 已修复，不同股票有不同值')
        else:
            print('❌ 累计换手率: 仍需修复，所有股票值相同')
        
        if len(unique_avg_turnover) > 1:
            unique_fields += 1
            print('✅ 平均换手率: 已修复，不同股票有不同值')
        else:
            print('❌ 平均换手率: 仍需修复，所有股票值相同')
        
        if len(unique_breakthrough) > 1:
            unique_fields += 1
            print('✅ 是否突破: 已修复，不同股票有不同值')
        else:
            print('❌ 是否突破: 仍需修复，所有股票值相同')
        
        if len(unique_prices) > 0:
            unique_fields += 1
            print('✅ 突破价格: 已修复，有突破的股票有不同价格')
        else:
            print('❌ 突破价格: 仍需修复，没有突破价格数据')
        
        success_rate = (unique_fields / total_fields) * 100
        print(f'\n🏆 修复成功率: {success_rate:.1f}% ({unique_fields}/{total_fields})')
        
        if success_rate >= 80:
            print('🎉 修复效果良好！大部分字段已实现差异化')
        elif success_rate >= 60:
            print('⚠️ 修复效果一般，还需要进一步优化')
        else:
            print('❌ 修复效果不佳，需要重新检查实现')
            
    except Exception as e:
        print(f'❌ 测试失败: {e}')
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    test_small_batch_analysis()

if __name__ == "__main__":
    main()
