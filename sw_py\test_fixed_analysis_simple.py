#!/usr/bin/env python3
"""
简单测试修复后的分析
只分析少量股票，避免数据库连接问题
"""

import sys
import os
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from main import StockAnalysisMain

def main():
    """运行简单的修复测试"""
    
    print('🚀 运行修复后的简单测试分析')
    print('=' * 60)
    print('📋 测试配置:')
    print('✅ 测试模式: 只分析10只股票')
    print('✅ 避免数据库连接问题')
    print('✅ 验证修复后的字段差异化')
    print()
    print('⏰ 开始测试分析...')
    print('=' * 60)
    
    try:
        analyzer = StockAnalysisMain()
        # 运行测试模式（只分析10只股票）
        analyzer.run(test_mode=True, test_count=10)
        
        print('\n' + '=' * 60)
        print('🎉 测试分析完成！')
        print('📁 请检查exports文件夹中的最新Excel文件')
        print('🔍 验证以下字段是否有不同值:')
        print('  - MA支撑点')
        print('  - 累计换手率')
        print('  - 平均换手率')
        print('  - 最近突破日')
        print('  - 突破日收盘价')
        print('  - 是否突破')
        
    except Exception as e:
        print(f'❌ 测试分析失败: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
