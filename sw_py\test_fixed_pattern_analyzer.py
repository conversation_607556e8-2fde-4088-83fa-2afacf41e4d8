#!/usr/bin/env python3
"""
测试修复后的PatternAnalyzer
"""

import sys
import os
from datetime import datetime, date, timedelta

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from analysis.pattern_analyzer import PatternAnalyzer
from config.analysis_config import U_PATTERN_CONFIG, V_PATTERN_CONFIG, VOLUME_CONFIG

def test_fixed_analyzer():
    """测试修复后的分析器"""
    
    print('🧪 测试修复后的PatternAnalyzer')
    print('=' * 80)
    
    analyzer = PatternAnalyzer(U_PATTERN_CONFIG, V_PATTERN_CONFIG, VOLUME_CONFIG)
    
    # 测试多个不同的股票
    test_stocks = [
        {'stock_code': '000001', 'stock_name': '平安银行'},
        {'stock_code': '000002', 'stock_name': '万科A'},
        {'stock_code': '300001', 'stock_name': '特锐德'},
        {'stock_code': '600001', 'stock_name': '邯郸钢铁'},
        {'stock_code': '688001', 'stock_name': '华兴源创'}
    ]
    
    # 创建模拟周线数据
    weekly_data = []
    base_date = date(2023, 1, 1)
    for i in range(50):
        weekly_data.append({
            'trade_date': base_date + timedelta(days=i*7),
            'high_price': 10 + i * 0.1,
            'low_price': 9 + i * 0.1,
            'close_price': 9.5 + i * 0.1,
            'volume': 1000000 + i * 10000
        })
    
    print('📊 测试结果:')
    print('=' * 80)
    
    results = []
    
    for stock in test_stocks:
        print(f'\n🔍 测试股票: {stock["stock_code"]} - {stock["stock_name"]}')
        print('-' * 60)
        
        # 创建测试数据
        test_result = {
            'stock_code': stock['stock_code'],
            'stock_name': stock['stock_name'],
            'u_left_date': date(2023, 5, 1),
            'u_right_date': date(2023, 7, 1),
            'v_right_date': date(2023, 8, 1),
            'v_right_price': 12.5,
            'success_date': None  # 测试没有success_date的情况
        }
        
        # 执行完整分析
        complete_result = analyzer.complete_analysis_result(test_result, weekly_data)
        
        # 提取关键字段
        ma_num = complete_result.get('ma_num', '未设置')
        all_turnover = complete_result.get('all_turnover_rate', '未设置')
        avg_turnover = complete_result.get('avg_turnover_rate', '未设置')
        is_breakthrough = complete_result.get('is_breakthrough', '未设置')
        breakthrough_price = complete_result.get('breakthrough_price', '未设置')
        
        print(f'  MA支撑点: {ma_num}')
        print(f'  累计换手率: {all_turnover}')
        print(f'  平均换手率: {avg_turnover}')
        print(f'  是否突破: {is_breakthrough}')
        print(f'  突破价格: {breakthrough_price}')
        
        results.append({
            'stock_code': stock['stock_code'],
            'ma_num': ma_num,
            'all_turnover_rate': all_turnover,
            'avg_turnover_rate': avg_turnover,
            'is_breakthrough': is_breakthrough,
            'breakthrough_price': breakthrough_price
        })
    
    # 分析差异化效果
    print('\n📈 差异化分析:')
    print('=' * 80)
    
    fields = ['ma_num', 'all_turnover_rate', 'avg_turnover_rate', 'is_breakthrough', 'breakthrough_price']
    
    for field in fields:
        values = [r[field] for r in results if r[field] != '未设置']
        unique_values = len(set(str(v) for v in values))
        total_values = len(values)
        
        print(f'{field}:')
        print(f'  不同值数量: {unique_values}/{total_values}')
        print(f'  所有值: {values}')
        
        if unique_values > 1:
            print(f'  ✅ 修复成功 - 有 {unique_values} 个不同值')
        elif unique_values == 1 and total_values > 1:
            print(f'  ❌ 修复失败 - 所有值相同: {values[0]}')
        else:
            print(f'  ⚠️ 数据不足')
        print()
    
    # 计算总体修复成功率
    success_count = 0
    total_fields = len(fields)
    
    for field in fields:
        values = [r[field] for r in results if r[field] != '未设置']
        unique_values = len(set(str(v) for v in values))
        if unique_values > 1:
            success_count += 1
    
    success_rate = (success_count / total_fields) * 100
    
    print('🎯 修复效果总结:')
    print('=' * 80)
    print(f'修复成功率: {success_rate:.1f}% ({success_count}/{total_fields})')
    
    if success_rate >= 80:
        print('🎉 修复非常成功！')
    elif success_rate >= 60:
        print('👍 修复基本成功')
    elif success_rate >= 40:
        print('⚠️ 修复部分成功')
    else:
        print('❌ 修复效果不佳')
    
    return success_rate

def main():
    """主函数"""
    print('🔧 PatternAnalyzer修复效果测试')
    print('=' * 100)
    
    success_rate = test_fixed_analyzer()
    
    print('\n' + '=' * 100)
    print('📋 测试总结:')
    print('=' * 100)
    
    if success_rate >= 80:
        print('✅ 修复代码工作正常，可以运行完整分析')
    elif success_rate >= 60:
        print('⚠️ 修复代码基本工作，但可能需要进一步优化')
    else:
        print('❌ 修复代码存在问题，需要进一步调试')
    
    print(f'最终修复成功率: {success_rate:.1f}%')

if __name__ == "__main__":
    main()
