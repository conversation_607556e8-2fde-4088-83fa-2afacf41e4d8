#!/usr/bin/env python3
"""
增量数据处理测试脚本
用于验证增量处理功能的正确性
"""

import sys
import os
import logging
from datetime import datetime, timedelta

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models.database import DatabaseManager
from utils.incremental_manager import IncrementalDataManager
from config.incremental_config import INCREMENTAL_CONFIG, IncrementalHelper

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )

def test_first_run_detection():
    """测试首次运行检测"""
    print("\n=== 测试首次运行检测 ===")
    
    db_manager = DatabaseManager()
    helper = IncrementalHelper()
    
    # 检查当前状态
    is_first_run = helper.is_first_run(db_manager)
    print(f"当前是否为首次运行: {is_first_run}")
    
    # 检查数据量
    try:
        result = db_manager.execute_query("SELECT COUNT(*) as count FROM stock_weekly_data")
        if result:
            count = result[0]['count']
            print(f"当前周线数据量: {count:,} 条")
            
            threshold = INCREMENTAL_CONFIG['first_run_detection']['min_data_threshold']
            print(f"首次运行阈值: {threshold:,} 条")
            print(f"判断结果: {'首次运行' if count < threshold else '非首次运行'}")
    except Exception as e:
        print(f"查询数据量失败: {e}")

def test_date_range_calculation():
    """测试日期范围计算"""
    print("\n=== 测试日期范围计算 ===")
    
    helper = IncrementalHelper()
    
    # 测试不同数据类型的日期范围
    data_types = ['weekly_data', 'avg_trade', 'moving_average', 'volatility']
    
    for data_type in data_types:
        strategy = helper.get_data_strategy(data_type)
        if strategy.get('enabled', False):
            months = strategy.get('months', 3)
            overlap_weeks = strategy.get('overlap_weeks', 2)
            
            start_date, end_date = helper.calculate_incremental_date_range(
                months, overlap_weeks * 7
            )
            
            print(f"{data_type}:")
            print(f"  配置: {months}个月 + {overlap_weeks}周重叠")
            print(f"  日期范围: {start_date} ~ {end_date}")
            print(f"  天数: {(end_date - start_date).days} 天")

def test_incremental_manager():
    """测试增量管理器"""
    print("\n=== 测试增量管理器 ===")
    
    logger = logging.getLogger(__name__)
    db_manager = DatabaseManager()
    manager = IncrementalDataManager(db_manager, logger)
    
    # 测试是否应该使用增量处理
    should_use = manager.should_use_incremental()
    print(f"是否应该使用增量处理: {should_use}")
    
    # 测试获取增量日期范围
    if should_use:
        print("\n增量处理日期范围:")
        data_types = ['weekly_data', 'avg_trade', 'moving_average', 'volatility']
        
        for data_type in data_types:
            try:
                start_date, end_date = manager.get_incremental_date_range(data_type)
                print(f"  {data_type}: {start_date} ~ {end_date}")
            except Exception as e:
                print(f"  {data_type}: 错误 - {e}")

def test_sql_conditions():
    """测试SQL条件生成"""
    print("\n=== 测试SQL条件生成 ===")
    
    helper = IncrementalHelper()
    data_types = ['weekly_data', 'avg_trade', 'moving_average', 'volatility']
    
    for data_type in data_types:
        try:
            where_condition, params = helper.get_incremental_sql_condition(data_type, 't')
            if where_condition:
                print(f"{data_type}:")
                print(f"  WHERE条件: {where_condition}")
                print(f"  参数: {params}")
            else:
                print(f"{data_type}: 不支持增量处理")
        except Exception as e:
            print(f"{data_type}: 错误 - {e}")

def test_data_volume_analysis():
    """测试数据量分析"""
    print("\n=== 测试数据量分析 ===")
    
    db_manager = DatabaseManager()
    
    # 分析各表的数据量和时间分布
    tables = {
        'stock_weekly_data': '周线数据',
        'stock_avg_trade': '平均交易量',
        'stock_moving_average': '移动平均线',
        'stock_volatility': '波动率'
    }
    
    for table_name, description in tables.items():
        try:
            # 总数据量
            result = db_manager.execute_query(f"SELECT COUNT(*) as count FROM {table_name}")
            total_count = result[0]['count'] if result else 0
            
            # 时间范围
            result = db_manager.execute_query(f"""
                SELECT 
                    MIN(trade_date) as min_date,
                    MAX(trade_date) as max_date
                FROM {table_name}
            """)
            
            if result and result[0]['min_date']:
                min_date = result[0]['min_date']
                max_date = result[0]['max_date']
                
                print(f"{description} ({table_name}):")
                print(f"  总数据量: {total_count:,} 条")
                print(f"  时间范围: {min_date} ~ {max_date}")
                
                # 最近3个月的数据量
                three_months_ago = datetime.now().date() - timedelta(days=90)
                result = db_manager.execute_query(f"""
                    SELECT COUNT(*) as count 
                    FROM {table_name} 
                    WHERE trade_date >= %s
                """, (three_months_ago,))
                
                recent_count = result[0]['count'] if result else 0
                percentage = (recent_count / total_count * 100) if total_count > 0 else 0
                
                print(f"  最近3个月: {recent_count:,} 条 ({percentage:.1f}%)")
                
                # 估算增量处理节省的时间
                if percentage < 50:
                    print(f"  💡 增量处理预计可节省 {100-percentage:.1f}% 的处理时间")
            else:
                print(f"{description} ({table_name}): 无数据")
                
        except Exception as e:
            print(f"{description} ({table_name}): 查询失败 - {e}")

def test_configuration():
    """测试配置"""
    print("\n=== 测试配置 ===")
    
    print("增量处理配置:")
    print(f"  启用增量处理: {INCREMENTAL_CONFIG['enable_incremental']}")
    print(f"  强制全量重建: {INCREMENTAL_CONFIG['force_full_rebuild']}")
    print(f"  默认时间窗口: {INCREMENTAL_CONFIG['time_window']['default_months']} 个月")
    
    print("\n数据策略配置:")
    for data_type, strategy in INCREMENTAL_CONFIG['data_strategies'].items():
        if strategy.get('enabled', False):
            months = strategy.get('months', 'N/A')
            overlap = strategy.get('overlap_weeks', 'N/A')
            print(f"  {data_type}: {months}个月 + {overlap}周重叠")
        else:
            print(f"  {data_type}: 禁用")

def main():
    """主函数"""
    setup_logging()
    
    print("🔍 增量数据处理功能测试")
    print("=" * 50)
    
    try:
        test_configuration()
        test_first_run_detection()
        test_date_range_calculation()
        test_incremental_manager()
        test_sql_conditions()
        test_data_volume_analysis()
        
        print("\n✅ 所有测试完成")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")

if __name__ == "__main__":
    main()
