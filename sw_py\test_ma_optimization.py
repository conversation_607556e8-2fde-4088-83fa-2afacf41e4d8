#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试MA支撑点优化效果
运行小规模股票分析，验证MA支撑点计算改进
"""

import sys
import os
from datetime import datetime, date

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from models.database import DatabaseManager, StockDataModel
    from utils.enhanced_logger import get_enhanced_logger
    from analysis.pattern_analyzer import PatternAnalyzer
    from config.analysis_config import U_PATTERN_CONFIG, V_PATTERN_CONFIG, VOLUME_CONFIG
    from utils.data_processor import DataProcessor
except ImportError as e:
    print(f"导入失败: {e}")
    sys.exit(1)

def test_ma_optimization():
    """测试MA支撑点优化效果"""
    print("=== 测试MA支撑点优化效果 ===")
    
    # 设置日志
    logger = get_enhanced_logger('ma_test')
    
    # 创建组件
    db_manager = DatabaseManager()
    stock_model = StockDataModel(db_manager)
    data_processor = DataProcessor()
    
    # 创建分析器并设置stock_model
    analyzer = PatternAnalyzer(U_PATTERN_CONFIG, V_PATTERN_CONFIG, VOLUME_CONFIG)
    analyzer.stock_model = stock_model  # 关键：设置stock_model
    
    # 测试股票列表
    test_stocks = ['000001', '000002', '300001', '600001', '688001']
    
    print(f"\n📊 测试 {len(test_stocks)} 只股票的MA支撑点计算")
    
    ma_results = {}
    
    for stock_code in test_stocks:
        print(f"\n--- 分析股票 {stock_code} ---")
        
        try:
            # 获取股票基本信息
            stock_list = stock_model.get_stock_list()
            stock_info = next((s for s in stock_list if s['gp_num'] == stock_code), None)

            if stock_info:
                stock_name = stock_info.get('gp_name', stock_code)
                print(f"📈 股票名称: {stock_name}")
            else:
                stock_name = stock_code
                print(f"📈 股票代码: {stock_code} (未找到名称)")
            
            # 获取周线数据
            weekly_data = stock_model.get_weekly_data(stock_code)
            if not weekly_data:
                print(f"❌ {stock_code} 无周线数据")
                continue
            
            print(f"📊 周线数据: {len(weekly_data)} 条")
            
            # 获取平均成交量数据
            avg_trade_data = stock_model.get_avg_trade_data(stock_code)
            print(f"📊 平均成交量数据: {len(avg_trade_data)} 条")
            
            # 执行形态分析（这会触发MA支撑点计算）
            result = analyzer.analyze_stock_patterns_strict(
                stock_code, stock_name, weekly_data, avg_trade_data
            )
            
            if result:
                ma_support = result.get('ma_support_point', 'N/A')
                print(f"✅ 形态分析成功，MA支撑点: MA{ma_support}")
                ma_results[stock_code] = {
                    'success': True,
                    'ma_support': ma_support,
                    'stock_name': stock_name
                }
            else:
                print(f"⚠️ 形态分析未通过筛选")
                
                # 直接测试MA支撑点计算
                test_start = date(2023, 7, 1)
                test_end = date(2023, 8, 15)
                ma_num = analyzer._find_best_moving_average_support(
                    stock_code, test_start, test_end
                )
                print(f"🔍 直接计算MA支撑点: MA{ma_num}")
                
                ma_results[stock_code] = {
                    'success': False,
                    'ma_support': ma_num,
                    'stock_name': stock_name
                }
                
        except Exception as e:
            print(f"❌ 分析股票 {stock_code} 失败: {e}")
            ma_results[stock_code] = {
                'success': False,
                'ma_support': 'ERROR',
                'error': str(e)
            }
    
    # 汇总结果
    print("\n=== MA支撑点计算结果汇总 ===")
    print(f"{'股票代码':<10} {'股票名称':<15} {'MA支撑点':<10} {'状态':<10}")
    print("-" * 50)
    
    for stock_code, result in ma_results.items():
        stock_name = result.get('stock_name', 'N/A')[:12]  # 限制长度
        ma_support = result.get('ma_support', 'N/A')
        status = "✅成功" if result.get('success') else "⚠️未筛选"
        
        if 'error' in result:
            status = "❌错误"
            
        print(f"{stock_code:<10} {stock_name:<15} MA{ma_support:<9} {status:<10}")
    
    # 分析MA支撑点分布
    ma_values = [result.get('ma_support') for result in ma_results.values() 
                 if isinstance(result.get('ma_support'), (int, float))]
    
    if ma_values:
        print(f"\n📊 MA支撑点分布:")
        from collections import Counter
        ma_counter = Counter(ma_values)
        for ma_val, count in sorted(ma_counter.items()):
            print(f"  MA{ma_val}: {count} 只股票")
        
        print(f"\n📈 统计信息:")
        print(f"  平均MA周期: {sum(ma_values) / len(ma_values):.1f}")
        print(f"  最常用MA周期: MA{ma_counter.most_common(1)[0][0]}")
        print(f"  MA周期范围: MA{min(ma_values)} ~ MA{max(ma_values)}")
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    test_ma_optimization()
