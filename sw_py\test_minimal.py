#!/usr/bin/env python3
"""
最小化测试
"""

import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from models.database import DatabaseManager, StockDataModel
    from config.analysis_config import U_PATTERN_CONFIG
    
    print("1. 初始化数据库...")
    db = DatabaseManager()
    stock_model = StockDataModel(db)
    
    print("2. 获取数据...")
    stock_code = '000004'
    weekly_data = stock_model.get_weekly_data(stock_code)
    avg_trade_data = stock_model.get_avg_trade_data(stock_code)
    
    print(f"   周线数据: {len(weekly_data)} 条")
    print(f"   平均交易量数据: {len(avg_trade_data)} 条")
    
    print("3. 测试U型左侧高点验证...")
    
    # 手动实现简单的U型左侧高点验证
    pre_month = U_PATTERN_CONFIG['pre_month']  # 10
    left_diff_rate = U_PATTERN_CONFIG['left_diff_rate']  # 0.03
    
    found_valid = False
    
    # 从第11周开始检查
    for i in range(pre_month, min(pre_month + 5, len(weekly_data))):
        current_high = float(weekly_data[i]['high_price'])
        current_date = weekly_data[i]['trade_date']
        
        # 检查前10周
        is_valid = True
        for j in range(max(0, i - pre_month), i):
            check_high = float(weekly_data[j]['high_price'])
            if check_high > current_high * (1 + left_diff_rate):
                is_valid = False
                break
        
        if is_valid:
            print(f"   ✅ 找到有效U型左侧高点: 第{i+1}周 ({current_date}) - {current_high}")
            found_valid = True
            break
        else:
            print(f"   ❌ 第{i+1}周 ({current_date}) 无效")
    
    if not found_valid:
        print("   ❌ 未找到有效的U型左侧高点")
    
    print("4. 测试完成")
    
except Exception as e:
    print(f"❌ 错误: {e}")
    import traceback
    traceback.print_exc()
