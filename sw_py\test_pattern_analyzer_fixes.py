#!/usr/bin/env python3
"""
测试PatternAnalyzer修复效果
直接测试修复后的方法是否生成不同的值
"""

import sys
import os
from datetime import datetime, date

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from analysis.pattern_analyzer import PatternAnalyzer
    print('✅ PatternAnalyzer导入成功')
except Exception as e:
    print(f'❌ PatternAnalyzer导入失败: {e}')
    sys.exit(1)

def test_ma_support_fixes():
    """测试MA支撑点修复"""
    print('🔍 测试MA支撑点修复效果')
    print('=' * 60)
    
    analyzer = PatternAnalyzer()
    
    # 测试不同股票代码的MA支撑点
    test_stocks = ['000001', '000002', '300001', '600001', '688001']
    
    print('测试 _find_best_moving_average_support 方法:')
    ma_values = []
    for stock_code in test_stocks:
        ma_value = analyzer._find_best_moving_average_support(
            stock_code, 
            date(2023, 6, 1), 
            date(2023, 8, 1)
        )
        ma_values.append(ma_value)
        print(f'  {stock_code}: MA支撑点 = {ma_value}')
    
    unique_ma_count = len(set(ma_values))
    print(f'\n结果: {unique_ma_count}/{len(test_stocks)} 个不同值')
    
    if unique_ma_count > 1:
        print('✅ MA支撑点修复成功 - 不同股票有不同值')
    else:
        print('❌ MA支撑点修复失败 - 所有股票值相同')
    
    return unique_ma_count > 1

def test_turnover_fixes():
    """测试换手率修复"""
    print('\n🔍 测试换手率修复效果')
    print('=' * 60)
    
    analyzer = PatternAnalyzer()
    
    # 测试不同股票代码的换手率
    test_stocks = ['000001', '000002', '300001', '600001', '688001']
    
    print('测试 _calculate_turnover_rates 方法:')
    turnover_results = []
    for stock_code in test_stocks:
        turnover_data = analyzer._calculate_turnover_rates(
            stock_code, 
            date(2023, 6, 1), 
            date(2023, 8, 1)
        )
        turnover_results.append(turnover_data)
        print(f'  {stock_code}: 累计={turnover_data.get("all_turnover_rate", 0):.6f}, 平均={turnover_data.get("avg_turnover_rate", 0):.6f}')
    
    # 检查累计换手率差异
    all_turnover_values = [r.get('all_turnover_rate', 0) for r in turnover_results]
    avg_turnover_values = [r.get('avg_turnover_rate', 0) for r in turnover_results]
    
    unique_all_count = len(set(all_turnover_values))
    unique_avg_count = len(set(avg_turnover_values))
    
    print(f'\n累计换手率: {unique_all_count}/{len(test_stocks)} 个不同值')
    print(f'平均换手率: {unique_avg_count}/{len(test_stocks)} 个不同值')
    
    if unique_all_count > 1 and unique_avg_count > 1:
        print('✅ 换手率修复成功 - 不同股票有不同值')
        return True
    else:
        print('❌ 换手率修复失败 - 存在相同值')
        return False

def test_breakthrough_fixes():
    """测试突破逻辑修复"""
    print('\n🔍 测试突破逻辑修复效果')
    print('=' * 60)
    
    analyzer = PatternAnalyzer()
    
    # 测试不同股票代码的突破逻辑
    test_stocks = ['000001', '000002', '300001', '600001', '688001']
    
    print('测试 check_price_logic 方法:')
    breakthrough_results = []
    for stock_code in test_stocks:
        # 创建测试结果
        test_result = {
            'stock_code': stock_code,
            'v_right_date': date(2023, 8, 1),
            'v_right_price': 10.0
        }
        
        price_data = analyzer.check_price_logic(test_result)
        breakthrough_results.append(price_data)
        
        is_breakthrough = '是' if price_data.get('is_breakthrough') else '否'
        breakthrough_price = price_data.get('breakthrough_price', 0)
        breakthrough_date = price_data.get('breakthrough_date', 'N/A')
        
        print(f'  {stock_code}: 突破={is_breakthrough}, 价格={breakthrough_price:.2f}, 日期={breakthrough_date}')
    
    # 检查突破结果差异
    breakthrough_flags = [r.get('is_breakthrough', False) for r in breakthrough_results]
    breakthrough_prices = [r.get('breakthrough_price', 0) for r in breakthrough_results]
    
    unique_flags_count = len(set(breakthrough_flags))
    unique_prices_count = len(set(breakthrough_prices))
    
    print(f'\n突破标志: {unique_flags_count}/{len(test_stocks)} 个不同值')
    print(f'突破价格: {unique_prices_count}/{len(test_stocks)} 个不同值')
    
    if unique_flags_count > 1 or unique_prices_count > 1:
        print('✅ 突破逻辑修复成功 - 不同股票有不同值')
        return True
    else:
        print('❌ 突破逻辑修复失败 - 所有股票值相同')
        return False

def test_complete_analysis():
    """测试完整分析流程"""
    print('\n🔍 测试完整分析流程')
    print('=' * 60)
    
    analyzer = PatternAnalyzer()
    
    # 创建测试数据
    test_result = {
        'stock_code': '000001',
        'stock_name': '平安银行',
        'u_left_date': date(2023, 5, 1),
        'u_right_date': date(2023, 7, 1),
        'v_right_date': date(2023, 8, 1),
        'v_right_price': 12.5,
        'success_date': date(2023, 8, 15)
    }
    
    # 创建模拟周线数据
    weekly_data = []
    for i in range(50):
        weekly_data.append({
            'trade_date': date(2023, 1, 1 + i*7),
            'high_price': 10 + i * 0.1,
            'low_price': 9 + i * 0.1,
            'close_price': 9.5 + i * 0.1,
            'volume': 1000000 + i * 10000
        })
    
    print('执行 complete_analysis_result:')
    complete_result = analyzer.complete_analysis_result(test_result, weekly_data)
    
    print(f'  MA支撑点: {complete_result.get("ma_num", "N/A")}')
    print(f'  累计换手率: {complete_result.get("all_turnover_rate", 0):.6f}')
    print(f'  平均换手率: {complete_result.get("avg_turnover_rate", 0):.6f}')
    print(f'  是否突破: {"是" if complete_result.get("is_breakthrough") else "否"}')
    print(f'  突破价格: {complete_result.get("breakthrough_price", 0):.2f}')
    print(f'  突破日期: {complete_result.get("breakthrough_date", "N/A")}')
    
    # 测试多个股票的完整分析
    print('\n测试多个股票的完整分析:')
    test_stocks = ['000001', '000002', '300001']
    complete_results = []
    
    for stock_code in test_stocks:
        test_result_copy = test_result.copy()
        test_result_copy['stock_code'] = stock_code
        test_result_copy['stock_name'] = f'测试股票{stock_code}'
        
        complete_result = analyzer.complete_analysis_result(test_result_copy, weekly_data)
        complete_results.append(complete_result)
        
        print(f'  {stock_code}: MA={complete_result.get("ma_num")}, 累计换手={complete_result.get("all_turnover_rate", 0):.4f}')
    
    # 检查差异化
    ma_values = [r.get('ma_num') for r in complete_results]
    turnover_values = [r.get('all_turnover_rate', 0) for r in complete_results]
    
    ma_unique = len(set(ma_values))
    turnover_unique = len(set(turnover_values))
    
    print(f'\n完整分析差异化结果:')
    print(f'  MA支撑点: {ma_unique}/{len(test_stocks)} 个不同值')
    print(f'  累计换手率: {turnover_unique}/{len(test_stocks)} 个不同值')
    
    if ma_unique > 1 and turnover_unique > 1:
        print('✅ 完整分析修复成功')
        return True
    else:
        print('❌ 完整分析仍有问题')
        return False

def main():
    """主测试函数"""
    print('🧪 PatternAnalyzer修复效果测试')
    print('=' * 80)
    
    # 执行各项测试
    ma_success = test_ma_support_fixes()
    turnover_success = test_turnover_fixes()
    breakthrough_success = test_breakthrough_fixes()
    complete_success = test_complete_analysis()
    
    # 总结测试结果
    print('\n' + '=' * 80)
    print('📊 测试结果总结:')
    print('=' * 80)
    
    tests = [
        ('MA支撑点修复', ma_success),
        ('换手率修复', turnover_success),
        ('突破逻辑修复', breakthrough_success),
        ('完整分析修复', complete_success)
    ]
    
    success_count = sum(1 for _, success in tests if success)
    total_tests = len(tests)
    
    for test_name, success in tests:
        status = '✅' if success else '❌'
        print(f'  {status} {test_name}')
    
    success_rate = (success_count / total_tests) * 100
    print(f'\n🎯 总体修复成功率: {success_rate:.1f}% ({success_count}/{total_tests})')
    
    if success_rate >= 75:
        print('🎉 修复效果优秀！')
    elif success_rate >= 50:
        print('👍 修复效果良好，还有改进空间')
    else:
        print('⚠️ 修复效果不佳，需要进一步调试')

if __name__ == "__main__":
    main()
