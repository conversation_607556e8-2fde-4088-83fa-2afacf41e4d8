#!/usr/bin/env python3
"""
快速测试形态分析
"""

import sys
import os
import logging

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置简单日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(message)s',
    datefmt='%H:%M:%S'
)

logger = logging.getLogger(__name__)

try:
    from models.database import DatabaseManager, StockDataModel
    from analysis.pattern_analyzer import PatternAnalyzer
    from config.analysis_config import U_PATTERN_CONFIG, V_PATTERN_CONFIG, VOLUME_CONFIG
    
    logger.info("🔍 开始快速测试")
    
    # 初始化
    db = DatabaseManager()
    stock_model = StockDataModel(db)
    
    # 测试股票000004
    stock_code = '000004'
    stock_name = '*ST国华'
    
    logger.info(f"测试股票: {stock_code} ({stock_name})")
    
    # 获取数据
    weekly_data = stock_model.get_weekly_data(stock_code)
    avg_trade_data = stock_model.get_avg_trade_data(stock_code)
    
    logger.info(f"周线数据: {len(weekly_data)} 条")
    logger.info(f"平均交易量数据: {len(avg_trade_data)} 条")
    
    if len(weekly_data) < 20 or len(avg_trade_data) == 0:
        logger.error("数据不足")
        exit(1)
    
    # 创建分析器
    analyzer = PatternAnalyzer(U_PATTERN_CONFIG, V_PATTERN_CONFIG, VOLUME_CONFIG)
    
    logger.info("开始形态分析...")
    
    # 执行分析
    result = analyzer.analyze_stock_patterns_strict(stock_code, stock_name, weekly_data, avg_trade_data)
    
    if result:
        logger.info(f"✅ 发现形态: {result.get('pattern_type', '未知')}")
        logger.info(f"  U左: {result.get('u_left_date')} - {result.get('u_left_price')}")
        logger.info(f"  U右: {result.get('u_right_date')} - {result.get('u_right_price')}")
    else:
        logger.info("❌ 未发现形态")
    
    # 打印统计
    logger.info("=== 筛选统计 ===")
    stats = analyzer.filter_stats
    logger.info(f"总分析: {stats.get('total_analyzed', 0)}")
    logger.info(f"成功: {stats.get('success_count', 0)}")
    logger.info(f"U左失败: {stats.get('u_left_failed', 0)}")
    logger.info(f"U右失败: {stats.get('u_right_failed', 0)}")
    
    logger.info("✅ 测试完成")
    
except Exception as e:
    logger.error(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()
