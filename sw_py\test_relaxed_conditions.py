#!/usr/bin/env python3
"""
测试放宽条件后的筛选效果
"""

from models.database import DatabaseManager
from analysis.pattern_analyzer import PatternAnalyzer
from config.analysis_config import U_PATTERN_CONFIG, V_PATTERN_CONFIG, VOLUME_CONFIG
import logging

logging.basicConfig(level=logging.INFO)

def test_different_thresholds():
    """测试不同阈值的筛选效果"""
    print('=== 测试不同U型左侧高点阈值 ===')
    
    db = DatabaseManager()
    
    # 测试多只股票
    test_stocks = ['000001', '000002', '600000', '600036', '000858']
    thresholds = [0.03, 0.05, 0.08, 0.10, 0.15]  # 3%, 5%, 8%, 10%, 15%
    
    for threshold in thresholds:
        print(f'\n--- 测试阈值 {threshold:.0%} ---')
        total_valid_points = 0
        
        for stock_code in test_stocks:
            weekly_data = db.execute_query(
                'SELECT * FROM stock_weekly_data WHERE stock_code = %s ORDER BY trade_date LIMIT 50', 
                (stock_code,)
            )
            
            if len(weekly_data) < 20:
                continue
            
            valid_points = 0
            for i in range(10, min(len(weekly_data), 30)):
                current_high = float(weekly_data[i]['high_price'])
                
                # 检查前10周
                start_idx = max(0, i - 10)
                threshold_price = current_high * (1 + threshold)
                
                is_valid = True
                for j in range(start_idx, i):
                    if j != i and float(weekly_data[j]['high_price']) > threshold_price:
                        is_valid = False
                        break
                
                if is_valid:
                    valid_points += 1
            
            total_valid_points += valid_points
            if valid_points > 0:
                print(f'  {stock_code}: {valid_points} 个有效点')
        
        print(f'阈值 {threshold:.0%} 总计: {total_valid_points} 个有效U型左侧高点')

def test_oracle_data_compatibility():
    """测试Oracle数据兼容性"""
    print('\n=== 测试Oracle数据兼容性 ===')
    
    db = DatabaseManager()
    
    # 检查是否存在GP_JY_W_TMP表（Oracle临时表）
    try:
        oracle_data = db.execute_query('SELECT COUNT(*) as count FROM GP_JY_W_TMP')
        print(f'Oracle临时表GP_JY_W_TMP记录数: {oracle_data[0]["count"]}')
        
        # 检查数据样本
        sample_data = db.execute_query('SELECT * FROM GP_JY_W_TMP WHERE ROWNUM <= 5')
        print('Oracle临时表数据样本:')
        for data in sample_data:
            print(f'  {data}')
            
    except Exception as e:
        print(f'Oracle临时表不存在或无法访问: {e}')
        
        # 检查我们的数据表
        python_count = db.execute_query('SELECT COUNT(*) as count FROM stock_weekly_data')[0]['count']
        print(f'Python表stock_weekly_data记录数: {python_count}')

def test_price_unit_issue():
    """测试价格单位问题"""
    print('\n=== 测试价格单位问题 ===')
    
    db = DatabaseManager()
    
    # 获取一些价格数据
    price_data = db.execute_query('''
        SELECT stock_code, trade_date, high_price, low_price, close_price
        FROM stock_weekly_data 
        WHERE stock_code IN ('000001', '600000') 
        ORDER BY stock_code, trade_date 
        LIMIT 10
    ''')
    
    print('价格数据样本:')
    for data in price_data:
        print(f'  {data["stock_code"]} {data["trade_date"]}: 高{data["high_price"]} 低{data["low_price"]} 收{data["close_price"]}')
    
    # 检查价格是否可能是以分为单位
    if price_data:
        sample_high = float(price_data[0]['high_price'])
        print(f'\n价格分析:')
        print(f'  原始高价: {sample_high}')
        print(f'  除以100后: {sample_high/100} (如果原始是分)')
        print(f'  除以1000后: {sample_high/1000} (如果原始是厘)')

def test_with_relaxed_analyzer():
    """使用放宽条件的分析器测试"""
    print('\n=== 使用放宽条件测试 ===')
    
    # 创建放宽条件的配置
    relaxed_u_config = U_PATTERN_CONFIG.copy()
    relaxed_u_config['left_diff_rate'] = 0.10  # 放宽到10%
    
    db = DatabaseManager()
    analyzer = PatternAnalyzer(relaxed_u_config, V_PATTERN_CONFIG, VOLUME_CONFIG)
    
    # 测试几只股票
    test_stocks = ['000001', '000002', '600000']
    
    for stock_code in test_stocks:
        print(f'\n--- 测试股票 {stock_code} (放宽条件) ---')
        
        weekly_data = db.execute_query(
            'SELECT * FROM stock_weekly_data WHERE stock_code = %s ORDER BY trade_date', 
            (stock_code,)
        )
        
        if len(weekly_data) < 20:
            print(f'数据不足: {len(weekly_data)} 条')
            continue
        
        # 测试U型左侧高点
        valid_points = 0
        for i in range(10, min(len(weekly_data), 50)):
            current_high = float(weekly_data[i]['high_price'])
            if analyzer.check_u_left_strict(weekly_data, i, current_high):
                valid_points += 1
                if valid_points <= 3:  # 只显示前3个
                    print(f'  ✓ 第{i}周有效: {weekly_data[i]["trade_date"]} 价格{current_high}')
        
        print(f'  总计: {valid_points} 个有效U型左侧高点')

def main():
    """主函数"""
    print('🔍 开始测试放宽条件的筛选效果')
    
    try:
        test_different_thresholds()
        test_oracle_data_compatibility()
        test_price_unit_issue()
        test_with_relaxed_analyzer()
        
        print('\n✅ 测试完成')
        
    except Exception as e:
        print(f'❌ 测试失败: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
