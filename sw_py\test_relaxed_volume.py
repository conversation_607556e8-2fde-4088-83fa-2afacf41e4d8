#!/usr/bin/env python3
"""
测试放宽交易量条件的效果
"""

import sys
import os
import logging

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models.database import DatabaseManager, StockDataModel
from analysis.pattern_analyzer import PatternAnalyzer
from config.analysis_config import U_PATTERN_CONFIG, V_PATTERN_CONFIG, VOLUME_CONFIG

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_volume_conditions():
    """测试不同交易量条件的效果"""
    
    print('🧪 测试不同交易量条件的效果')
    
    db = DatabaseManager()
    stock_model = StockDataModel(db)
    
    # 测试不同的交易量倍数
    volume_multipliers = [1.0, 1.01, 1.02, 1.03, 1.05, 1.1]
    test_stocks = ['000001', '000002', '600000', '600036', '000858']
    
    print(f'\n📊 测试结果对比:')
    print(f'倍数\t成功股票数\t成功率')
    
    for multiplier in volume_multipliers:
        print(f'\n--- 测试交易量倍数 {multiplier} ---')
        
        # 创建修改后的配置
        test_u_config = U_PATTERN_CONFIG.copy()
        test_u_config['volume_amplify_rate'] = multiplier
        
        analyzer = PatternAnalyzer(test_u_config, V_PATTERN_CONFIG, VOLUME_CONFIG)
        
        success_count = 0
        success_details = []
        
        for stock_code in test_stocks:
            weekly_data = stock_model.get_weekly_data(stock_code)
            avg_trade_data = stock_model.get_avg_trade_data(stock_code)
            
            if len(weekly_data) < 20:
                continue
            
            result = analyzer.analyze_stock_patterns_strict(stock_code, f'股票{stock_code}', weekly_data, avg_trade_data)
            
            if result:
                success_count += 1
                success_details.append(f'{stock_code}: {result}')
        
        success_rate = success_count / len(test_stocks) * 100
        print(f'{multiplier}\t{success_count}/{len(test_stocks)}\t{success_rate:.1f}%')
        
        if success_details:
            print(f'  成功案例:')
            for detail in success_details:
                print(f'    {detail}')

def analyze_volume_statistics():
    """分析交易量统计数据"""
    
    print(f'\n📊 分析交易量统计数据:')
    
    db = DatabaseManager()
    
    # 分析交易量与5日均量的比例分布
    volume_ratio_stats = db.execute_query('''
        SELECT 
            CASE 
                WHEN w.jy_quantity / a.avg_qty >= 1.1 THEN '>=1.1倍'
                WHEN w.jy_quantity / a.avg_qty >= 1.05 THEN '1.05-1.1倍'
                WHEN w.jy_quantity / a.avg_qty >= 1.02 THEN '1.02-1.05倍'
                WHEN w.jy_quantity / a.avg_qty >= 1.0 THEN '1.0-1.02倍'
                ELSE '<1.0倍'
            END as ratio_range,
            COUNT(*) as count
        FROM stock_weekly_data w
        JOIN stock_avg_trade a ON w.stock_code = a.stock_code AND w.trade_date = a.trade_date
        WHERE a.avg_days = 5 
            AND w.stock_code IN ('000001', '000002', '600000', '600036', '000858')
            AND a.avg_qty > 0
        GROUP BY 
            CASE 
                WHEN w.jy_quantity / a.avg_qty >= 1.1 THEN '>=1.1倍'
                WHEN w.jy_quantity / a.avg_qty >= 1.05 THEN '1.05-1.1倍'
                WHEN w.jy_quantity / a.avg_qty >= 1.02 THEN '1.02-1.05倍'
                WHEN w.jy_quantity / a.avg_qty >= 1.0 THEN '1.0-1.02倍'
                ELSE '<1.0倍'
            END
        ORDER BY 
            CASE 
                WHEN ratio_range = '>=1.1倍' THEN 5
                WHEN ratio_range = '1.05-1.1倍' THEN 4
                WHEN ratio_range = '1.02-1.05倍' THEN 3
                WHEN ratio_range = '1.0-1.02倍' THEN 2
                ELSE 1
            END DESC
    ''')
    
    print(f'交易量与5日均量比例分布:')
    total_count = sum(row['count'] for row in volume_ratio_stats)
    
    for row in volume_ratio_stats:
        percentage = row['count'] / total_count * 100
        print(f'  {row["ratio_range"]}: {row["count"]}条 ({percentage:.1f}%)')
    
    # 计算累计通过率
    print(f'\n累计通过率:')
    cumulative = 0
    for row in volume_ratio_stats:
        cumulative += row['count']
        cumulative_rate = cumulative / total_count * 100
        print(f'  {row["ratio_range"]}及以上: {cumulative_rate:.1f}%')

def test_without_volume_check():
    """测试完全跳过交易量检查"""
    
    print(f'\n🧪 测试完全跳过交易量检查:')
    
    db = DatabaseManager()
    stock_model = StockDataModel(db)
    
    # 创建无交易量要求的配置
    test_u_config = U_PATTERN_CONFIG.copy()
    test_u_config['volume_amplify_rate'] = 0.0  # 设为0表示跳过交易量检查
    
    analyzer = PatternAnalyzer(test_u_config, V_PATTERN_CONFIG, VOLUME_CONFIG)
    
    test_stocks = ['000001', '000002', '600000', '600036', '000858']
    success_count = 0
    success_details = []
    
    for stock_code in test_stocks:
        weekly_data = stock_model.get_weekly_data(stock_code)
        avg_trade_data = stock_model.get_avg_trade_data(stock_code)
        
        if len(weekly_data) < 20:
            continue
        
        result = analyzer.analyze_stock_patterns_strict(stock_code, f'股票{stock_code}', weekly_data, avg_trade_data)
        
        if result:
            success_count += 1
            success_details.append(f'{stock_code}: {result}')
            print(f'  ✅ {stock_code}: {result}')
        else:
            # 显示失败统计
            stats = analyzer.filter_stats
            print(f'  ❌ {stock_code}: U左侧{stats.get("u_left_valid", 0)}, U右侧{stats.get("u_right_valid", 0)}, U底部{stats.get("u_bottom_valid", 0)}')
    
    success_rate = success_count / len(test_stocks) * 100
    print(f'\n无交易量要求成功率: {success_count}/{len(test_stocks)} ({success_rate:.1f}%)')

def check_oracle_volume_logic():
    """检查Oracle交易量逻辑"""
    
    print(f'\n🔍 检查Oracle交易量逻辑:')
    
    print(f'Oracle SQL逻辑:')
    print(f'  SELECT 1 FROM gp_avg_wtrade dt, GP_JY_W_TMP wt')
    print(f'  WHERE dt.gp_num = p_gpnum')
    print(f'    AND dt.gp_jy_date = wt.jy_date')
    print(f'    AND dt.avg_days = 5')
    print(f'    AND (dt.avg_qty * 1.05) <= wt.jy_quantity;')
    
    print(f'\n转换为Python逻辑:')
    print(f'  current_volume >= (avg_volume * 1.05)')
    
    print(f'\n可能的问题:')
    print(f'  1. Oracle中可能有其他隐含条件')
    print(f'  2. 数据精度或计算方式不同')
    print(f'  3. Oracle版本可能使用不同的交易量倍数')
    
    print(f'\n建议:')
    print(f'  1. 先测试1.02倍或1.01倍的交易量要求')
    print(f'  2. 或者完全跳过交易量检查，专注于价格形态')
    print(f'  3. 检查Oracle版本的实际执行结果')

def main():
    """主函数"""
    logger.info('🧪 开始测试放宽交易量条件')
    
    try:
        test_volume_conditions()
        analyze_volume_statistics()
        test_without_volume_check()
        check_oracle_volume_logic()
        
        logger.info('✅ 交易量条件测试完成')
        
    except Exception as e:
        logger.error(f'❌ 测试失败: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
