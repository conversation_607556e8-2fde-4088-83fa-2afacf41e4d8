#!/usr/bin/env python3
"""
简单测试脚本 - 调试股票分析问题
"""

import sys
import os
import logging

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置简单日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

logger = logging.getLogger(__name__)

try:
    from models.database import DatabaseManager, StockDataModel
    from analysis.pattern_analyzer import PatternAnalyzer
    from config.analysis_config import U_PATTERN_CONFIG, V_PATTERN_CONFIG, VOLUME_CONFIG
    
    logger.info("✅ 模块导入成功")
    
    # 测试数据库连接
    db_manager = DatabaseManager()
    stock_model = StockDataModel(db_manager)
    
    logger.info("✅ 数据库连接成功")
    
    # 获取股票列表（只取前3只）
    stock_list = stock_model.get_stock_list()[:3]
    logger.info(f"✅ 获取股票列表成功，测试股票数量: {len(stock_list)}")
    
    for stock in stock_list:
        logger.info(f"  - {stock['gp_num']}: {stock['gp_name']}")
    
    # 测试获取第一只股票的周线数据
    if stock_list:
        test_stock = stock_list[0]
        stock_code = test_stock['gp_num']
        
        logger.info(f"🔍 测试获取股票 {stock_code} 的周线数据...")
        
        # 获取周线数据
        weekly_data = stock_model.get_weekly_data(stock_code)
        logger.info(f"  周线数据条数: {len(weekly_data)}")
        
        if weekly_data:
            logger.info(f"  最早日期: {weekly_data[0]['trade_date']}")
            logger.info(f"  最晚日期: {weekly_data[-1]['trade_date']}")
            
            # 获取平均交易量数据
            avg_trade_data = stock_model.get_avg_trade_data(stock_code)
            logger.info(f"  平均交易量数据条数: {len(avg_trade_data)}")
            
            if len(weekly_data) >= 20 and len(avg_trade_data) > 0:
                # 测试形态分析
                logger.info(f"🔍 测试股票 {stock_code} 的形态分析...")
                
                pattern_analyzer = PatternAnalyzer(U_PATTERN_CONFIG, V_PATTERN_CONFIG, VOLUME_CONFIG)
                
                result = pattern_analyzer.analyze_stock_patterns_strict(
                    stock_code, test_stock['gp_name'], weekly_data, avg_trade_data
                )
                
                if result:
                    logger.info(f"✅ 发现形态: {result['pattern_type']}")
                    logger.info(f"  U左: {result.get('u_left_date')} - {result.get('u_left_price')}")
                    logger.info(f"  U右: {result.get('u_right_date')} - {result.get('u_right_price')}")
                    logger.info(f"  V右: {result.get('v_right_date')} - {result.get('v_right_price')}")
                else:
                    logger.info("❌ 未发现符合条件的形态")
                
                # 打印筛选统计
                pattern_analyzer.print_filter_statistics()
            else:
                logger.warning(f"⚠️ 股票 {stock_code} 数据不足，跳过形态分析")
        else:
            logger.warning(f"⚠️ 股票 {stock_code} 没有周线数据")
    
    logger.info("✅ 测试完成")
    
except Exception as e:
    logger.error(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()
