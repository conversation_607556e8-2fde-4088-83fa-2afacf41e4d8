#!/usr/bin/env python3
"""
测试特定股票的完整形态分析
"""

import sys
import os
import logging

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置详细日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

logger = logging.getLogger(__name__)

try:
    from models.database import DatabaseManager, StockDataModel
    from analysis.pattern_analyzer import PatternAnalyzer
    from config.analysis_config import U_PATTERN_CONFIG, V_PATTERN_CONFIG, VOLUME_CONFIG
    
    # 测试数据库连接
    db_manager = DatabaseManager()
    stock_model = StockDataModel(db_manager)
    
    # 测试通过U型左侧高点验证的股票
    test_stocks = ['000004', '000007']
    
    for stock_code in test_stocks:
        logger.info(f"\n{'='*60}")
        logger.info(f"🔍 测试股票 {stock_code} 的完整形态分析")
        logger.info(f"{'='*60}")
        
        # 获取股票信息
        stock_list = stock_model.get_stock_list()
        stock_info = next((s for s in stock_list if s['gp_num'] == stock_code), None)
        if not stock_info:
            logger.error(f"未找到股票 {stock_code}")
            continue
            
        stock_name = stock_info['gp_name']
        logger.info(f"股票名称: {stock_name}")
        
        # 获取数据
        weekly_data = stock_model.get_weekly_data(stock_code)
        avg_trade_data = stock_model.get_avg_trade_data(stock_code)
        
        logger.info(f"周线数据条数: {len(weekly_data)}")
        logger.info(f"平均交易量数据条数: {len(avg_trade_data)}")
        
        if len(weekly_data) < 20 or len(avg_trade_data) == 0:
            logger.warning(f"⚠️ 股票 {stock_code} 数据不足，跳过")
            continue
        
        # 创建形态分析器
        pattern_analyzer = PatternAnalyzer(U_PATTERN_CONFIG, V_PATTERN_CONFIG, VOLUME_CONFIG)
        
        # 执行完整的形态分析
        logger.info("开始执行完整的形态分析...")
        result = pattern_analyzer.analyze_stock_patterns_strict(
            stock_code, stock_name, weekly_data, avg_trade_data
        )
        
        if result:
            logger.info(f"✅ 发现形态: {result['pattern_type']}")
            logger.info(f"  U左: {result.get('u_left_date')} - {result.get('u_left_price')}")
            logger.info(f"  U右: {result.get('u_right_date')} - {result.get('u_right_price')}")
            logger.info(f"  U底: {result.get('u_low_date')} - {result.get('u_low_price')}")
            logger.info(f"  V右: {result.get('v_right_date')} - {result.get('v_right_price')}")
            logger.info(f"  V底: {result.get('v_low_date')} - {result.get('v_low_price')}")
        else:
            logger.info("❌ 未发现符合条件的形态")
        
        # 打印详细的筛选统计
        logger.info("\n--- 筛选统计详情 ---")
        pattern_analyzer.print_filter_statistics()
    
    logger.info(f"\n{'='*60}")
    logger.info("✅ 测试完成")
    
except Exception as e:
    logger.error(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()
