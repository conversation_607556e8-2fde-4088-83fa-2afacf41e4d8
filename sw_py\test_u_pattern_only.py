#!/usr/bin/env python3
"""
测试仅U型形态（不要求V型）
"""

import sys
import os
import logging

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models.database import DatabaseManager, StockDataModel
from analysis.pattern_analyzer import PatternAnalyzer
from config.analysis_config import U_PATTERN_CONFIG, V_PATTERN_CONFIG, VOLUME_CONFIG

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_u_pattern_only():
    """测试仅U型形态"""
    
    print('🔍 测试仅U型形态（不要求V型）')
    
    db = DatabaseManager()
    stock_model = StockDataModel(db)
    analyzer = PatternAnalyzer(U_PATTERN_CONFIG, V_PATTERN_CONFIG, VOLUME_CONFIG)
    
    test_stocks = ['000001', '000002', '600000', '600036', '000858']
    
    print(f'\n📊 U型形态测试结果:')
    
    total_u_patterns = 0
    
    for stock_code in test_stocks:
        print(f'\n--- 股票 {stock_code} ---')
        
        weekly_data = stock_model.get_weekly_data(stock_code)
        avg_trade_data = stock_model.get_avg_trade_data(stock_code)
        
        if len(weekly_data) < 20:
            print(f'  数据不足: {len(weekly_data)}条')
            continue
        
        print(f'  数据: 周线{len(weekly_data)}条, 平均交易量{len(avg_trade_data)}条')
        
        # 手动检查U型形态
        u_patterns_found = []
        
        # 从第11周开始检查
        for i in range(U_PATTERN_CONFIG['pre_month'], min(len(weekly_data), 50)):
            current_data = weekly_data[i]
            current_high = float(current_data['high_price'])
            
            # 1. 检查U型左侧高点
            if analyzer.check_u_left_strict(weekly_data, i, current_high):
                
                # 2. 查找U型右侧高点
                u_right_result = analyzer.get_u_right_strict(
                    weekly_data, avg_trade_data, i, current_high, stock_code
                )
                
                if u_right_result:
                    u_right_idx, u_right_date, u_right_price = u_right_result
                    
                    # 3. 检查U型底部
                    u_bottom_result = analyzer.check_u_bottom_strict(
                        weekly_data, i, u_right_idx, current_high
                    )
                    
                    if u_bottom_result:
                        u_lowest_price, u_lowest_date = u_bottom_result
                        
                        u_pattern = {
                            'left_date': current_data['trade_date'],
                            'left_price': current_high,
                            'right_date': u_right_date,
                            'right_price': u_right_price,
                            'bottom_date': u_lowest_date,
                            'bottom_price': u_lowest_price
                        }
                        
                        u_patterns_found.append(u_pattern)
                        print(f'  ✅ U型形态: 左({current_data["trade_date"]},{current_high:.2f}) 右({u_right_date},{u_right_price:.2f}) 底({u_lowest_date},{u_lowest_price:.2f})')
                        
                        # 只显示前3个
                        if len(u_patterns_found) >= 3:
                            break
        
        total_u_patterns += len(u_patterns_found)
        print(f'  U型形态数量: {len(u_patterns_found)}')
    
    print(f'\n📈 总体结果:')
    print(f'  总U型形态数量: {total_u_patterns}')
    print(f'  平均每股: {total_u_patterns/len(test_stocks):.1f}个')

def test_u_pattern_with_relaxed_volume():
    """测试放宽交易量要求的U型形态"""
    
    print(f'\n🧪 测试放宽交易量要求的U型形态:')
    
    db = DatabaseManager()
    stock_model = StockDataModel(db)
    
    # 创建放宽交易量要求的配置
    relaxed_u_config = U_PATTERN_CONFIG.copy()
    relaxed_volume_config = VOLUME_CONFIG.copy()
    relaxed_volume_config['volume_amplify_rate'] = 1.01  # 从1.05降到1.01
    
    analyzer = PatternAnalyzer(relaxed_u_config, V_PATTERN_CONFIG, relaxed_volume_config)
    
    test_stocks = ['000001', '000002', '600000']
    
    for stock_code in test_stocks:
        print(f'\n--- 股票 {stock_code} (交易量1.01倍) ---')
        
        weekly_data = stock_model.get_weekly_data(stock_code)
        avg_trade_data = stock_model.get_avg_trade_data(stock_code)
        
        u_patterns_found = 0
        
        for i in range(relaxed_u_config['pre_month'], min(len(weekly_data), 30)):
            current_data = weekly_data[i]
            current_high = float(current_data['high_price'])
            
            if analyzer.check_u_left_strict(weekly_data, i, current_high):
                u_right_result = analyzer.get_u_right_strict(
                    weekly_data, avg_trade_data, i, current_high, stock_code
                )
                
                if u_right_result:
                    u_right_idx, u_right_date, u_right_price = u_right_result
                    u_bottom_result = analyzer.check_u_bottom_strict(
                        weekly_data, i, u_right_idx, current_high
                    )
                    
                    if u_bottom_result:
                        u_patterns_found += 1
                        if u_patterns_found <= 2:  # 只显示前2个
                            u_lowest_price, u_lowest_date = u_bottom_result
                            print(f'  ✅ U型形态: 左({current_data["trade_date"]},{current_high:.2f}) 右({u_right_date},{u_right_price:.2f}) 底({u_lowest_date},{u_lowest_price:.2f})')
        
        print(f'  总U型形态: {u_patterns_found}个')

def test_without_volume_requirement():
    """测试完全无交易量要求的U型形态"""
    
    print(f'\n🧪 测试完全无交易量要求的U型形态:')
    
    db = DatabaseManager()
    stock_model = StockDataModel(db)
    
    # 创建无交易量要求的analyzer
    class NoVolumeAnalyzer(PatternAnalyzer):
        def check_volume_condition_strict_fixed(self, weekly_data, avg_trade_data, current_idx, stock_code):
            """跳过交易量检查，直接返回True"""
            return True
    
    analyzer = NoVolumeAnalyzer(U_PATTERN_CONFIG, V_PATTERN_CONFIG, VOLUME_CONFIG)
    
    test_stocks = ['000001', '000002', '600000']
    
    for stock_code in test_stocks:
        print(f'\n--- 股票 {stock_code} (无交易量要求) ---')
        
        weekly_data = stock_model.get_weekly_data(stock_code)
        avg_trade_data = stock_model.get_avg_trade_data(stock_code)
        
        u_patterns_found = 0
        
        for i in range(U_PATTERN_CONFIG['pre_month'], min(len(weekly_data), 30)):
            current_data = weekly_data[i]
            current_high = float(current_data['high_price'])
            
            if analyzer.check_u_left_strict(weekly_data, i, current_high):
                u_right_result = analyzer.get_u_right_strict(
                    weekly_data, avg_trade_data, i, current_high, stock_code
                )
                
                if u_right_result:
                    u_right_idx, u_right_date, u_right_price = u_right_result
                    u_bottom_result = analyzer.check_u_bottom_strict(
                        weekly_data, i, u_right_idx, current_high
                    )
                    
                    if u_bottom_result:
                        u_patterns_found += 1
                        if u_patterns_found <= 2:  # 只显示前2个
                            u_lowest_price, u_lowest_date = u_bottom_result
                            print(f'  ✅ U型形态: 左({current_data["trade_date"]},{current_high:.2f}) 右({u_right_date},{u_right_price:.2f}) 底({u_lowest_date},{u_lowest_price:.2f})')
        
        print(f'  总U型形态: {u_patterns_found}个')

def analyze_why_no_patterns():
    """分析为什么找不到形态"""
    
    print(f'\n🔍 分析为什么找不到形态:')
    
    db = DatabaseManager()
    stock_model = StockDataModel(db)
    analyzer = PatternAnalyzer(U_PATTERN_CONFIG, V_PATTERN_CONFIG, VOLUME_CONFIG)
    
    stock_code = '000001'
    weekly_data = stock_model.get_weekly_data(stock_code)
    avg_trade_data = stock_model.get_avg_trade_data(stock_code)
    
    print(f'股票 {stock_code} 详细分析:')
    
    # 统计各阶段的通过情况
    stats = {
        'u_left_passed': 0,
        'u_right_passed': 0,
        'u_bottom_passed': 0,
        'total_checked': 0
    }
    
    for i in range(U_PATTERN_CONFIG['pre_month'], min(len(weekly_data), 50)):
        current_data = weekly_data[i]
        current_high = float(current_data['high_price'])
        stats['total_checked'] += 1
        
        # 检查U型左侧
        if analyzer.check_u_left_strict(weekly_data, i, current_high):
            stats['u_left_passed'] += 1
            
            # 检查U型右侧
            u_right_result = analyzer.get_u_right_strict(
                weekly_data, avg_trade_data, i, current_high, stock_code
            )
            
            if u_right_result:
                stats['u_right_passed'] += 1
                u_right_idx, u_right_date, u_right_price = u_right_result
                
                # 检查U型底部
                u_bottom_result = analyzer.check_u_bottom_strict(
                    weekly_data, i, u_right_idx, current_high
                )
                
                if u_bottom_result:
                    stats['u_bottom_passed'] += 1
                    print(f'  ✅ 完整U型: 第{i}周 左({current_data["trade_date"]},{current_high:.2f})')
    
    print(f'\n统计结果:')
    print(f'  检查总数: {stats["total_checked"]}')
    print(f'  U左侧通过: {stats["u_left_passed"]} ({stats["u_left_passed"]/stats["total_checked"]:.1%})')
    print(f'  U右侧通过: {stats["u_right_passed"]} ({stats["u_right_passed"]/stats["total_checked"]:.1%})')
    print(f'  U底部通过: {stats["u_bottom_passed"]} ({stats["u_bottom_passed"]/stats["total_checked"]:.1%})')

def main():
    """主函数"""
    logger.info('🔍 开始测试仅U型形态')
    
    try:
        test_u_pattern_only()
        test_u_pattern_with_relaxed_volume()
        test_without_volume_requirement()
        analyze_why_no_patterns()
        
        logger.info('✅ U型形态测试完成')
        
    except Exception as e:
        logger.error(f'❌ 测试失败: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
