#!/usr/bin/env python3
"""
使用当前数据测试形态分析
"""

from models.database import DatabaseManager, StockDataModel
from analysis.pattern_analyzer import PatternAnalyzer
from config.analysis_config import U_PATTERN_CONFIG, V_PATTERN_CONFIG, VOLUME_CONFIG
import logging

logging.basicConfig(level=logging.INFO)

def test_with_modified_config():
    """使用修改后的配置测试"""
    print('=== 使用修改后的配置测试 ===')
    
    # 创建适合短期数据的配置
    modified_u_config = U_PATTERN_CONFIG.copy()
    modified_u_config['pre_month'] = 5  # 减少前段周期从10周到5周
    modified_u_config['left_diff_rate'] = 0.08  # 放宽左侧涨幅限制到8%
    modified_u_config['lr_diff_rate'] = 0.08  # 放宽左右价差到8%
    
    modified_v_config = V_PATTERN_CONFIG.copy()
    modified_v_config['low_period'] = 2  # 减少最小间隔到2周
    
    print(f'修改后的U型配置:')
    print(f'  前段周期: {modified_u_config["pre_month"]} (原10)')
    print(f'  左侧涨幅限制: {modified_u_config["left_diff_rate"]:.0%} (原3%)')
    print(f'  左右价差: {modified_u_config["lr_diff_rate"]:.0%} (原3%)')
    
    db = DatabaseManager()
    stock_model = StockDataModel(db)
    analyzer = PatternAnalyzer(modified_u_config, modified_v_config, VOLUME_CONFIG)
    
    # 测试几只股票
    test_stocks = ['000001', '000002', '600000', '600036', '000858']
    total_patterns = 0
    
    for stock_code in test_stocks:
        print(f'\n--- 测试股票 {stock_code} ---')
        
        stock_info = db.execute_query("SELECT name FROM stock_info_a_code_name WHERE code = %s", (stock_code,))
        stock_name = stock_info[0]['name'] if stock_info else stock_code
        
        weekly_data = stock_model.get_weekly_data(stock_code)
        avg_trade_data = stock_model.get_avg_trade_data(stock_code)
        
        print(f'数据量: 周线{len(weekly_data)}条, 平均交易量{len(avg_trade_data)}条')
        
        if len(weekly_data) < 15:
            print('数据不足，跳过')
            continue
        
        # 执行分析
        result = analyzer.analyze_stock_patterns_strict(stock_code, stock_name, weekly_data, avg_trade_data)
        
        if result:
            total_patterns += 1
            print(f'✅ 找到形态: {result}')
        else:
            print('❌ 未找到形态')
        
        # 显示统计
        stats = analyzer.filter_stats
        print(f'筛选统计: U左侧{stats.get("u_left_valid", 0)}, U右侧{stats.get("u_right_valid", 0)}, U底部{stats.get("u_bottom_valid", 0)}')
    
    print(f'\n总计找到 {total_patterns} 个形态')

def test_individual_conditions():
    """测试各个条件的通过率"""
    print('\n=== 测试各个条件的通过率 ===')
    
    db = DatabaseManager()
    
    # 使用放宽的配置
    relaxed_u_config = {
        'pre_month': 5,
        'left_diff_rate': 0.10,  # 10%
        'lr_diff_rate': 0.10,    # 10%
        'll_low_diff': 0.12,
        'll_high_diff': 0.5,
        'volume_amplify_rate': 1.05,
        'low_period': 5
    }
    
    analyzer = PatternAnalyzer(relaxed_u_config, V_PATTERN_CONFIG, VOLUME_CONFIG)
    
    test_stocks = ['000001', '000002', '600000']
    
    for stock_code in test_stocks:
        print(f'\n--- 分析股票 {stock_code} 各条件通过率 ---')
        
        weekly_data = db.execute_query(
            'SELECT * FROM stock_weekly_data WHERE stock_code = %s ORDER BY trade_date', 
            (stock_code,)
        )
        
        avg_trade_data = db.execute_query(
            'SELECT * FROM stock_avg_trade WHERE stock_code = %s AND avg_days = 5 ORDER BY trade_date', 
            (stock_code,)
        )
        
        if len(weekly_data) < 15:
            continue
        
        # 统计各条件通过情况
        u_left_count = 0
        u_right_count = 0
        u_bottom_count = 0
        
        for i in range(relaxed_u_config['pre_month'], min(len(weekly_data), 50)):
            current_high = float(weekly_data[i]['high_price'])
            
            # 测试U型左侧高点
            if analyzer.check_u_left_strict(weekly_data, i, current_high):
                u_left_count += 1
                
                # 测试U型右侧高点
                u_right_result = analyzer.get_u_right_strict(weekly_data, avg_trade_data, i, current_high, stock_code)
                if u_right_result:
                    u_right_count += 1
                    right_idx = u_right_result[0]
                    
                    # 测试U型底部
                    u_bottom_result = analyzer.check_u_bottom_strict(weekly_data, i, right_idx, current_high)
                    if u_bottom_result:
                        u_bottom_count += 1
        
        total_points = min(len(weekly_data), 50) - relaxed_u_config['pre_month']
        print(f'  总测试点: {total_points}')
        print(f'  U型左侧高点通过: {u_left_count} ({u_left_count/total_points:.1%})')
        print(f'  U型右侧高点通过: {u_right_count} ({u_right_count/total_points:.1%})')
        print(f'  U型底部通过: {u_bottom_count} ({u_bottom_count/total_points:.1%})')

def test_volume_condition():
    """专门测试交易量条件"""
    print('\n=== 测试交易量条件 ===')
    
    db = DatabaseManager()
    
    # 检查交易量数据匹配情况
    volume_match = db.execute_query('''
        SELECT 
            COUNT(DISTINCT w.stock_code) as weekly_stocks,
            COUNT(DISTINCT a.stock_code) as avg_stocks,
            COUNT(DISTINCT CASE WHEN a.stock_code IS NOT NULL THEN w.stock_code END) as matched_stocks
        FROM stock_weekly_data w
        LEFT JOIN stock_avg_trade a ON w.stock_code = a.stock_code AND w.trade_date = a.trade_date
        WHERE a.avg_days = 5
    ''')
    
    if volume_match:
        result = volume_match[0]
        print(f'周线数据股票: {result["weekly_stocks"]}')
        print(f'5日均量股票: {result["avg_stocks"]}')
        print(f'匹配股票: {result["matched_stocks"]}')
        match_rate = result["matched_stocks"] / result["weekly_stocks"] * 100
        print(f'匹配率: {match_rate:.1f}%')
    
    # 测试具体股票的交易量条件
    test_stock = '000001'
    print(f'\n--- 测试股票 {test_stock} 交易量条件 ---')
    
    # 获取最近的数据进行测试
    recent_data = db.execute_query('''
        SELECT w.trade_date, w.jy_quantity, a.avg_qty
        FROM stock_weekly_data w
        LEFT JOIN stock_avg_trade a ON w.stock_code = a.stock_code AND w.trade_date = a.trade_date AND a.avg_days = 5
        WHERE w.stock_code = %s
        ORDER BY w.trade_date DESC
        LIMIT 10
    ''', (test_stock,))
    
    print('最近10周交易量对比:')
    volume_valid_count = 0
    for data in recent_data:
        if data['avg_qty']:
            threshold = float(data['avg_qty']) * 1.05
            is_valid = data['jy_quantity'] >= threshold
            if is_valid:
                volume_valid_count += 1
            print(f'  {data["trade_date"]}: 实际{data["jy_quantity"]} vs 阈值{threshold:.0f} {"✓" if is_valid else "✗"}')
        else:
            print(f'  {data["trade_date"]}: 实际{data["jy_quantity"]} vs 无5日均量数据')
    
    print(f'交易量条件通过率: {volume_valid_count}/{len(recent_data)} ({volume_valid_count/len(recent_data):.1%})')

def main():
    """主函数"""
    print('🔍 使用当前数据测试形态分析')
    
    try:
        test_with_modified_config()
        test_individual_conditions()
        test_volume_condition()
        
        print('\n✅ 测试完成')
        
    except Exception as e:
        print(f'❌ 测试失败: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
