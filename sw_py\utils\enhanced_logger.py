#!/usr/bin/env python3
"""
增强的日志管理器
提供更好的日志输出格式和错误定位功能
"""

import logging
import os
import sys
import time
from datetime import datetime
from typing import Dict, Any, Optional
import traceback

class ColoredFormatter(logging.Formatter):
    """彩色日志格式化器"""
    
    COLORS = {
        'DEBUG': '\033[36m',      # 青色
        'INFO': '\033[32m',       # 绿色
        'WARNING': '\033[33m',    # 黄色
        'ERROR': '\033[31m',      # 红色
        'CRITICAL': '\033[35m',   # 紫色
        'RESET': '\033[0m'        # 重置
    }
    
    def format(self, record):
        # 添加颜色
        if hasattr(record, 'levelname'):
            color = self.COLORS.get(record.levelname, '')
            reset = self.COLORS['RESET']
            record.color = color
            record.reset = reset
        
        # 格式化消息
        formatted = super().format(record)
        return formatted

class ProgressLogger:
    """进度日志记录器"""
    
    def __init__(self, logger: logging.Logger, total: int, description: str = "处理中"):
        self.logger = logger
        self.total = total
        self.description = description
        self.current = 0
        self.start_time = time.time()
        self.last_log_time = 0
        self.log_interval = 5  # 每5秒记录一次进度
    
    def update(self, count: int = 1):
        """更新进度"""
        self.current += count
        current_time = time.time()
        
        # 控制日志频率
        if current_time - self.last_log_time >= self.log_interval or self.current >= self.total:
            percentage = (self.current / self.total) * 100
            elapsed = current_time - self.start_time
            
            if self.current > 0:
                eta = (elapsed / self.current) * (self.total - self.current)
                eta_str = f", 预计剩余: {eta:.1f}秒" if eta > 1 else ""
            else:
                eta_str = ""
            
            self.logger.info(
                f"📊 {self.description}: {self.current}/{self.total} "
                f"({percentage:.1f}%), 已用时: {elapsed:.1f}秒{eta_str}"
            )
            self.last_log_time = current_time
    
    def finish(self):
        """完成进度记录"""
        elapsed = time.time() - self.start_time
        self.logger.info(
            f"✅ {self.description}完成: {self.total} 项, 总耗时: {elapsed:.1f}秒"
        )

class EnhancedLogger:
    """增强的日志管理器"""
    
    def __init__(self, name: str, log_dir: str = "logs"):
        self.name = name
        self.log_dir = log_dir
        self.logger = logging.getLogger(name)
        self._setup_logger()
    
    def _setup_logger(self):
        """设置日志器"""
        # 创建日志目录
        if not os.path.exists(self.log_dir):
            os.makedirs(self.log_dir, exist_ok=True)
        
        # 清除现有处理器
        self.logger.handlers.clear()
        self.logger.setLevel(logging.DEBUG)
        
        # 文件处理器
        log_file = os.path.join(self.log_dir, f"{self.name}.log")
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)
        file_formatter = logging.Formatter(
            '%(asctime)s - [%(levelname)s] %(name)s:%(funcName)s:%(lineno)d - %(message)s'
        )
        file_handler.setFormatter(file_formatter)
        
        # 控制台处理器
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        console_formatter = ColoredFormatter(
            '%(asctime)s - %(color)s[%(levelname)s]%(reset)s %(name)s - %(message)s'
        )
        console_handler.setFormatter(console_formatter)
        
        # 添加处理器
        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)
    
    def debug(self, message: str, **kwargs):
        """调试日志"""
        self.logger.debug(self._format_message(message, **kwargs))
    
    def info(self, message: str, **kwargs):
        """信息日志"""
        self.logger.info(self._format_message(message, **kwargs))
    
    def warning(self, message: str, **kwargs):
        """警告日志"""
        self.logger.warning(self._format_message(message, **kwargs))
    
    def error(self, message: str, exception: Optional[Exception] = None, **kwargs):
        """错误日志"""
        formatted_message = self._format_message(message, **kwargs)
        
        if exception:
            formatted_message += f"\n异常详情: {str(exception)}"
            formatted_message += f"\n堆栈跟踪:\n{traceback.format_exc()}"
        
        self.logger.error(formatted_message)
    
    def critical(self, message: str, **kwargs):
        """严重错误日志"""
        self.logger.critical(self._format_message(message, **kwargs))
    
    def _format_message(self, message: str, **kwargs) -> str:
        """格式化消息"""
        if kwargs:
            # 添加上下文信息
            context_parts = []
            for key, value in kwargs.items():
                context_parts.append(f"{key}={value}")
            
            if context_parts:
                message += f" [{', '.join(context_parts)}]"
        
        return message
    
    def log_function_entry(self, func_name: str, **params):
        """记录函数入口"""
        param_str = ", ".join([f"{k}={v}" for k, v in params.items()])
        self.debug(f"🔵 进入函数 {func_name}({param_str})")
    
    def log_function_exit(self, func_name: str, result: Any = None, duration: float = None):
        """记录函数出口"""
        msg = f"🔴 退出函数 {func_name}"
        if result is not None:
            msg += f", 返回: {result}"
        if duration is not None:
            msg += f", 耗时: {duration:.3f}秒"
        self.debug(msg)
    
    def log_data_processing(self, operation: str, count: int, duration: float = None):
        """记录数据处理操作"""
        msg = f"📊 {operation}: 处理 {count:,} 条数据"
        if duration is not None:
            msg += f", 耗时: {duration:.2f}秒"
            if count > 0 and duration > 0:
                rate = count / duration
                msg += f", 速率: {rate:.0f} 条/秒"
        self.info(msg)
    
    def log_database_operation(self, operation: str, table: str, count: int = None, duration: float = None):
        """记录数据库操作"""
        msg = f"🗄️ {operation} {table}"
        if count is not None:
            msg += f": {count:,} 条记录"
        if duration is not None:
            msg += f", 耗时: {duration:.2f}秒"
        self.info(msg)
    
    def log_ma_support_calculation(self, stock_code: str, ma_num: int, method: str = "default"):
        """记录MA支撑点计算"""
        self.debug(f"🔢 MA支撑点计算: {stock_code} -> MA{ma_num} (方法: {method})")
    
    def log_pattern_analysis(self, stock_code: str, pattern_type: str, result: bool, details: Dict[str, Any] = None):
        """记录形态分析"""
        status = "✅ 成功" if result else "❌ 失败"
        msg = f"📈 形态分析 {stock_code} {pattern_type}: {status}"
        
        if details:
            detail_parts = []
            for key, value in details.items():
                detail_parts.append(f"{key}={value}")
            msg += f" [{', '.join(detail_parts)}]"
        
        self.info(msg)
    
    def log_incremental_processing(self, operation: str, data_type: str, count: int = None, time_range: str = None):
        """记录增量处理操作"""
        msg = f"🔄 增量处理 {operation} {data_type}"
        if count is not None:
            msg += f": {count:,} 条数据"
        if time_range:
            msg += f" (时间范围: {time_range})"
        self.info(msg)
    
    def create_progress_logger(self, total: int, description: str = "处理中") -> ProgressLogger:
        """创建进度日志记录器"""
        return ProgressLogger(self.logger, total, description)
    
    def log_performance_metrics(self, metrics: Dict[str, Any]):
        """记录性能指标"""
        self.info("📊 性能指标:")
        for key, value in metrics.items():
            if isinstance(value, float):
                self.info(f"  {key}: {value:.2f}")
            else:
                self.info(f"  {key}: {value}")

def get_enhanced_logger(name: str) -> EnhancedLogger:
    """获取增强日志器实例"""
    return EnhancedLogger(name)
