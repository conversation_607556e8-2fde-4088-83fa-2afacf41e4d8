#!/usr/bin/env python3
"""
增量数据处理管理器
负责管理增量数据处理逻辑，优化非首次执行的性能
"""

import os
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Any, Optional

from config.incremental_config import INCREMENTAL_CONFIG, IncrementalHelper


class IncrementalDataManager:
    """增量数据处理管理器"""

    # 类级别的日志控制，避免多个实例间的重复日志
    _global_logged_messages = set()
    _global_first_run_logged = False
    _global_date_range_logged = set()

    def __init__(self, db_manager, logger: logging.Logger = None):
        """
        初始化增量数据管理器
        
        Args:
            db_manager: 数据库管理器实例
            logger: 日志记录器
        """
        self.db_manager = db_manager
        self.logger = logger or logging.getLogger(__name__)
        self.config = INCREMENTAL_CONFIG
        self.helper = IncrementalHelper()
        
        # 运行统计
        self.stats = {
            'is_first_run': None,
            'time_savings': {},
            'data_volumes': {},
            'start_time': None,
            'end_time': None
        }

        # 日志输出控制，避免重复日志
        self._logged_messages = set()
        self._first_run_logged = False
        self._date_range_logged = set()
    
    def should_use_incremental(self) -> bool:
        """
        判断是否应该使用增量处理
        
        Returns:
            bool: True表示使用增量处理
        """
        # 检查全局开关
        if not self.config['enable_incremental']:
            self.logger.info("增量处理已禁用")
            return False
        
        # 检查强制全量重建标志
        if self.config['force_full_rebuild']:
            self.logger.info("强制全量重建，跳过增量处理")
            return False
        
        # 检查是否为首次运行
        is_first_run = self.helper.is_first_run(self.db_manager)
        self.stats['is_first_run'] = is_first_run

        # 只在第一次检测时输出日志，避免重复（使用类级别缓存）
        if not IncrementalDataManager._global_first_run_logged:
            if is_first_run:
                self.logger.info("检测到首次运行，将执行全量数据处理")
            else:
                self.logger.info("检测到非首次运行，将使用增量数据处理")
            IncrementalDataManager._global_first_run_logged = True

        return not is_first_run
    
    def get_incremental_date_range(self, data_type: str) -> Tuple[datetime, datetime]:
        """
        获取指定数据类型的增量处理日期范围
        
        Args:
            data_type: 数据类型
            
        Returns:
            tuple: (start_date, end_date)
        """
        strategy = self.helper.get_data_strategy(data_type)
        
        if not strategy.get('enabled', False):
            # 如果该数据类型不支持增量，返回全量范围
            return datetime(2023, 1, 1).date(), datetime.now().date()
        
        months = strategy.get('months', 3)
        overlap_weeks = strategy.get('overlap_weeks', 2)
        overlap_days = overlap_weeks * 7
        
        end_date = datetime.now().date()
        start_date = end_date - timedelta(days=months * 30 + overlap_days)

        # 只在第一次计算该数据类型的日期范围时输出日志，避免重复（使用类级别缓存）
        if data_type not in IncrementalDataManager._global_date_range_logged:
            self.logger.info(f"{data_type} 增量处理范围: {start_date} ~ {end_date} ({months}个月 + {overlap_days}天重叠)")
            IncrementalDataManager._global_date_range_logged.add(data_type)

        return start_date, end_date
    
    def cleanup_old_data(self, data_type: str, start_date: datetime) -> int:
        """
        清理指定日期范围内的旧数据
        
        Args:
            data_type: 数据类型
            start_date: 开始日期
            
        Returns:
            int: 清理的记录数
        """
        table_mapping = {
            'weekly_data': 'stock_weekly_data',
            'avg_trade': 'stock_avg_trade',
            'moving_average': 'stock_moving_average',
            'volatility': 'stock_volatility'
        }
        
        table_name = table_mapping.get(data_type)
        if not table_name:
            self.logger.warning(f"未知的数据类型: {data_type}")
            return 0
        
        try:
            # 先查询要删除的记录数
            count_sql = f"SELECT COUNT(*) as count FROM {table_name} WHERE trade_date >= %s"
            result = self.db_manager.execute_query(count_sql, (start_date,))
            old_count = result[0]['count'] if result else 0
            
            if old_count == 0:
                self.logger.info(f"{table_name}: 没有需要清理的数据")
                return 0
            
            # 执行删除
            delete_sql = f"DELETE FROM {table_name} WHERE trade_date >= %s"
            self.db_manager.execute_update(delete_sql, (start_date,))
            
            self.logger.info(f"{table_name}: 清理了 {old_count} 条旧数据 (>= {start_date})")
            self.stats['data_volumes'][f'{data_type}_cleaned'] = old_count
            
            return old_count
            
        except Exception as e:
            self.logger.error(f"清理 {table_name} 数据失败: {e}")
            return 0
    
    def get_incremental_stock_filter(self, data_type: str) -> Tuple[str, List]:
        """
        获取增量处理的SQL过滤条件
        
        Args:
            data_type: 数据类型
            
        Returns:
            tuple: (where_condition, params)
        """
        if not self.should_use_incremental():
            return '', []
        
        start_date, end_date = self.get_incremental_date_range(data_type)
        
        where_condition = "trade_date >= %s"
        params = [start_date]
        
        return where_condition, params
    
    def update_data_filter_config(self, config: Dict[str, Any], data_type: str) -> Dict[str, Any]:
        """
        更新数据过滤配置以支持增量处理
        
        Args:
            config: 原始配置
            data_type: 数据类型
            
        Returns:
            dict: 更新后的配置
        """
        if not self.should_use_incremental():
            return config
        
        # 复制配置避免修改原始配置
        updated_config = config.copy()
        
        # 获取增量日期范围
        start_date, end_date = self.get_incremental_date_range(data_type)
        
        # 更新开始日期
        updated_config['incremental_start_date'] = start_date.strftime('%Y-%m-%d')
        updated_config['use_incremental'] = True
        
        self.logger.info(f"数据过滤配置已更新为增量模式: {data_type} 从 {start_date} 开始")
        
        return updated_config
    
    def log_performance_stats(self):
        """记录性能统计信息"""
        if not self.config['monitoring']['log_time_savings']:
            return
        
        self.logger.info("=== 增量处理性能统计 ===")
        self.logger.info(f"首次运行: {self.stats['is_first_run']}")
        
        if self.stats['data_volumes']:
            self.logger.info("数据量统计:")
            for key, value in self.stats['data_volumes'].items():
                self.logger.info(f"  {key}: {value:,} 条")
        
        if self.stats['time_savings']:
            self.logger.info("时间节省统计:")
            for key, value in self.stats['time_savings'].items():
                self.logger.info(f"  {key}: {value:.2f} 秒")
    
    def mark_run_complete(self):
        """标记运行完成"""
        try:
            self.helper.create_run_marker()
            self.logger.info("运行标记已创建")
        except Exception as e:
            self.logger.warning(f"创建运行标记失败: {e}")
    
    def validate_incremental_data(self, data_type: str) -> bool:
        """
        验证增量数据的完整性
        
        Args:
            data_type: 数据类型
            
        Returns:
            bool: True表示数据完整
        """
        if not self.config['integrity_check']['enabled']:
            return True
        
        try:
            table_mapping = {
                'weekly_data': 'stock_weekly_data',
                'avg_trade': 'stock_avg_trade',
                'moving_average': 'stock_moving_average',
                'volatility': 'stock_volatility'
            }
            
            table_name = table_mapping.get(data_type)
            if not table_name:
                return True
            
            start_date, end_date = self.get_incremental_date_range(data_type)
            
            # 检查数据连续性
            if self.config['integrity_check']['check_continuity']:
                gap_sql = f"""
                SELECT COUNT(*) as gap_count
                FROM (
                    SELECT trade_date,
                           LAG(trade_date) OVER (ORDER BY trade_date) as prev_date
                    FROM {table_name}
                    WHERE trade_date BETWEEN %s AND %s
                    GROUP BY trade_date
                ) t
                WHERE DATEDIFF(trade_date, prev_date) > 7
                """
                
                result = self.db_manager.execute_query(gap_sql, (start_date, end_date))
                gap_count = result[0]['gap_count'] if result else 0
                
                if gap_count > 0:
                    self.logger.warning(f"{table_name}: 发现 {gap_count} 个数据缺口")
                    if self.config['integrity_check']['auto_fix_gaps']:
                        self.logger.info("自动修复数据缺口功能暂未实现")
                
            self.logger.info(f"{table_name}: 增量数据完整性验证通过")
            return True
            
        except Exception as e:
            self.logger.error(f"验证 {data_type} 数据完整性失败: {e}")
            return False
