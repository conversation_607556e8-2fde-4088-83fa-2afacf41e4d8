#!/usr/bin/env python3
"""
验证新生成的Excel文件修复效果
"""

import sys
import os
import pandas as pd
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def verify_excel_results():
    """验证Excel结果"""
    
    print('🔍 验证新生成的Excel文件修复效果')
    print('=' * 60)
    
    # 找到最新的Excel文件
    excel_file = 'exports/20250703_113719_stock_analysis_results.xlsx'
    
    if not os.path.exists(excel_file):
        print(f'❌ 文件不存在: {excel_file}')
        return
    
    print(f'📁 验证文件: {excel_file}')
    
    try:
        # 读取Excel文件
        df = pd.read_excel(excel_file)
        
        print(f'📊 总记录数: {len(df)}')
        print(f'📋 字段列表: {list(df.columns)}')
        
        # 检查关键字段
        key_fields = ['MA支撑点', '累计换手率', '平均换手率', '是否突破', '突破日收盘价']
        
        print('\n🔍 关键字段差异化检查:')
        print('=' * 60)
        
        for field in key_fields:
            if field in df.columns:
                unique_values = df[field].nunique()
                total_values = len(df[field].dropna())
                sample_values = df[field].dropna().head(10).tolist()
                
                print(f'{field}:')
                print(f'  不同值数量: {unique_values}/{total_values}')
                print(f'  前10个值: {sample_values}')
                
                if unique_values > 1:
                    print(f'  ✅ 修复成功 - 有 {unique_values} 个不同值')
                else:
                    print(f'  ❌ 修复失败 - 所有值相同')
                print()
            else:
                print(f'{field}: ❌ 字段不存在')
                print()
        
        # 显示前几行数据
        print('📋 前5行数据预览:')
        print('=' * 120)
        
        display_columns = ['股票代码', '股票名称', 'MA支撑点', '累计换手率', '平均换手率', '是否突破']
        available_columns = [col for col in display_columns if col in df.columns]
        
        if available_columns:
            preview_df = df[available_columns].head(5)
            print(preview_df.to_string(index=False))
        else:
            print('❌ 无法找到预期的字段')
        
        # 计算修复成功率
        print('\n🎯 修复效果评估:')
        print('=' * 60)
        
        success_count = 0
        total_fields = len(key_fields)
        
        for field in key_fields:
            if field in df.columns and df[field].nunique() > 1:
                success_count += 1
        
        success_rate = (success_count / total_fields) * 100
        print(f'修复成功率: {success_rate:.1f}% ({success_count}/{total_fields})')
        
        if success_rate >= 80:
            print('🎉 修复效果优秀！')
        elif success_rate >= 60:
            print('👍 修复效果良好')
        elif success_rate >= 40:
            print('⚠️ 修复效果一般')
        else:
            print('❌ 修复效果不佳')
        
        return success_rate
        
    except Exception as e:
        print(f'❌ 读取Excel文件失败: {e}')
        import traceback
        traceback.print_exc()
        return 0

def compare_with_old_file():
    """与旧文件对比"""
    
    print('\n🔄 与修复前文件对比')
    print('=' * 60)
    
    old_file = 'exports/20250703_110657_stock_analysis_results.xlsx'
    new_file = 'exports/20250703_113719_stock_analysis_results.xlsx'
    
    try:
        if os.path.exists(old_file):
            old_df = pd.read_excel(old_file)
            print(f'📁 修复前文件: {old_file} ({len(old_df)} 条记录)')
        else:
            print('❌ 找不到修复前的文件')
            return
        
        if os.path.exists(new_file):
            new_df = pd.read_excel(new_file)
            print(f'📁 修复后文件: {new_file} ({len(new_df)} 条记录)')
        else:
            print('❌ 找不到修复后的文件')
            return
        
        # 对比关键字段
        key_fields = ['MA支撑点', '累计换手率', '平均换手率']
        
        print('\n📊 字段差异化对比:')
        print('=' * 60)
        
        for field in key_fields:
            if field in old_df.columns and field in new_df.columns:
                old_unique = old_df[field].nunique()
                new_unique = new_df[field].nunique()
                
                print(f'{field}:')
                print(f'  修复前: {old_unique} 个不同值')
                print(f'  修复后: {new_unique} 个不同值')
                
                if new_unique > old_unique:
                    print(f'  ✅ 改进: +{new_unique - old_unique} 个不同值')
                elif new_unique == old_unique and new_unique > 1:
                    print(f'  ✅ 保持: 已有差异化')
                else:
                    print(f'  ❌ 无改进')
                print()
        
    except Exception as e:
        print(f'❌ 对比失败: {e}')

def main():
    """主函数"""
    print('🧪 Excel文件修复效果验证')
    print('=' * 80)
    
    success_rate = verify_excel_results()
    compare_with_old_file()
    
    print('\n' + '=' * 80)
    print('📋 验证总结:')
    print('=' * 80)
    
    if success_rate >= 80:
        print('🎉 修复非常成功！字段值已实现差异化')
    elif success_rate >= 60:
        print('👍 修复基本成功，大部分字段已差异化')
    elif success_rate >= 40:
        print('⚠️ 修复部分成功，仍有改进空间')
    else:
        print('❌ 修复效果不佳，需要进一步调试')
    
    print(f'最终修复成功率: {success_rate:.1f}%')

if __name__ == "__main__":
    main()
