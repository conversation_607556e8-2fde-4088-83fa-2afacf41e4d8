﻿create table GP_BP_INFO
(
  record_id     NUMBER,
  gp_num        VARCHAR2(10),
  bp_type       VARCHAR2(30),
  jy_date       DATE,
  low_price     NUMBER,
  creation_date DATE default sysdate,
  attribute1    VARCHAR2(10),
  attribute2    VARCHAR2(10),
  attribute3    VARCHAR2(10),
  attribute4    VARCHAR2(10),
  attribute5    VARCHAR2(10)
)
;
create index GP_BP_INFO_N1 on GP_BP_INFO (GP_NUM, BP_TYPE, JY_DATE, LOW_PRICE);
create unique index GP_BP_INFO_U1 on GP_BP_INFO (RECORD_ID);

