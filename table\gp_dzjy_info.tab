﻿create table GP_DZJY_INFO
(
  record_id     NUMBER,
  jy_date       DATE,
  gp_num        VARCHAR2(10),
  gp_name       VARCHAR2(20),
  cj_price      NUMBER,
  cj_qty        NUMBER,
  cj_amount     NUMBER,
  buy_yyb       VARCHAR2(200),
  sale_yyb      VARCHAR2(200),
  gp_type       VARCHAR2(10),
  creation_date DATE default sysdate
)
;
comment on column GP_DZJY_INFO.jy_date
  is '交易日期';
comment on column GP_DZJY_INFO.gp_num
  is '证券代码';
comment on column GP_DZJY_INFO.gp_name
  is '证券简称';
comment on column GP_DZJY_INFO.cj_price
  is '成交价格(元)';
comment on column GP_DZJY_INFO.cj_qty
  is '成交量(万股)';
comment on column GP_DZJY_INFO.cj_amount
  is '成交金额(万元)';
comment on column GP_DZJY_INFO.buy_yyb
  is '买方营业部';
comment on column GP_DZJY_INFO.sale_yyb
  is '卖方营业部';
comment on column GP_DZJY_INFO.gp_type
  is '证券类型';
create index GP_DZJY_INFO_N1 on GP_DZJY_INFO (JY_DATE, GP_NUM, GP_NAME, CJ_PRICE, CJ_QTY, CJ_AMOUNT, BUY_YYB, SALE_YYB, GP_TYPE);
create unique index GP_DZJY_INFO_U1 on GP_DZJY_INFO (RECORD_ID);

