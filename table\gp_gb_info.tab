﻿create table GP_GB_INFO
(
  record_id     NUMBER,
  gp_num        VARCHAR2(10),
  change_date   DATE,
  notice_date   DATE,
  change_reson  VARCHAR2(50),
  qty_sum       VARCHAR2(20),
  trade_qty     VARCHAR2(20),
  gg_qty        VARCHAR2(20),
  xs_qty        VARCHAR2(20),
  creation_date DATE default sysdate
)
;
comment on column GP_GB_INFO.gp_num
  is '股票';
comment on column GP_GB_INFO.change_date
  is '变更日期';
comment on column GP_GB_INFO.notice_date
  is '发布日期';
comment on column GP_GB_INFO.change_reson
  is '变更原因';
comment on column GP_GB_INFO.qty_sum
  is '总股本';
comment on column GP_GB_INFO.trade_qty
  is '流通A股';
comment on column GP_GB_INFO.gg_qty
  is '高管股';
comment on column GP_GB_INFO.xs_qty
  is '限售股';
create unique index GP_GB_INFO_U1 on GP_GB_INFO (RECORD_ID);
create unique index GP_GB_INFO_U2 on GP_GB_INFO (GP_NUM, CHANGE_DATE, NOTICE_DATE, CHANGE_RESON, QTY_SUM, TRADE_QTY, GG_QTY, XS_QTY);

