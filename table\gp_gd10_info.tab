﻿create table GP_GD10_INFO
(
  tradedate    VARCHAR2(18),
  record_id    VARCHAR2(32) default sys_guid() not null,
  gp_num       VARCHAR2(20),
  end_date     DATE,
  notice_date  DATE,
  gd_type      VARCHAR2(20),
  top_no       NUMBER,
  stock_holder VARCHAR2(500),
  hold_qty     NUMBER,
  hold_rate    NUMBER,
  create_date  DATE default sysdate,
  gd_nature    VARCHAR2(50)
)
;
comment on column GP_GD10_INFO.gp_num
  is '股票代码';
comment on column GP_GD10_INFO.gd_type
  is '股东类型';
comment on column GP_GD10_INFO.top_no
  is '排名';
comment on column GP_GD10_INFO.stock_holder
  is '股东名称';
comment on column GP_GD10_INFO.hold_qty
  is '持股数量(股)';
comment on column GP_GD10_INFO.hold_rate
  is '持股比例(%)';
create unique index GP_GD10_INFO_U1 on GP_GD10_INFO (GP_NUM, END_DATE, NOTICE_DATE, GD_TYPE, TOP_NO, STOCK_HOLDER, RECORD_ID);
alter table GP_GD10_INFO
  add constraint GP_GD10_INFO_PK primary key (RECORD_ID);

