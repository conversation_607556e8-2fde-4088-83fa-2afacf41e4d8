﻿create table GP_HK_D
(
  record_id     NUMBER,
  gp_num        VARCHAR2(50),
  jy_date       DATE,
  close_price   NUMBER,
  zd_amount     NUMBER,
  zd_rate       NUMBER,
  jy_quantity   NUMBER,
  jy_amount     NUMBER,
  open_price    NUMBER,
  high_price    NUMBER,
  low_price     NUMBER,
  zf_rate       NUMBER,
  creation_date DATE default sysdate,
  attribute1    VARCHAR2(50),
  attribute2    VARCHAR2(50),
  attribute3    VARCHAR2(50),
  attribute4    VARCHAR2(50),
  attribute5    VARCHAR2(50),
  attribute6    VARCHAR2(50),
  attribute7    VARCHAR2(50),
  attribute8    VARCHAR2(50),
  attribute9    VARCHAR2(50),
  attribute10   VARCHAR2(50)
)
;
comment on column GP_HK_D.jy_date
  is '交易日期';
comment on column GP_HK_D.close_price
  is '收盘价';
comment on column GP_HK_D.zd_amount
  is '涨跌额';
comment on column GP_HK_D.zd_rate
  is '涨跌幅';
comment on column GP_HK_D.jy_quantity
  is '成交量';
comment on column GP_HK_D.jy_amount
  is '成交额';
comment on column GP_HK_D.open_price
  is '开盘价';
comment on column GP_HK_D.high_price
  is '最高价';
comment on column GP_HK_D.low_price
  is '最低价';
comment on column GP_HK_D.zf_rate
  is '振幅';
create index GP_HK_D_N1 on GP_HK_D (RECORD_ID, GP_NUM, JY_DATE);
create unique index GP_HK_D_U1 on GP_HK_D (RECORD_ID);
create unique index GP_HK_D_U2 on GP_HK_D (GP_NUM, JY_DATE);

