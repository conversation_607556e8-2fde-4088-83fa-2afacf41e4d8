﻿create global temporary table GP_INFO_D_TMP
(
  row_num       NUMBER,
  record_id     NUMBER,
  gp_num        VARCHAR2(20),
  jy_date       DATE,
  open_price    NUMBER,
  high_price    NUMBER,
  low_price     NUMBER,
  close_price   NUMBER,
  volume_amount NUMBER,
  adj_close     NUMBER,
  jys_no        VARCHAR2(10),
  creation_date DATE default sysdate
)
on commit preserve rows;
create index GP_INFO_D_TMP_N1 on GP_INFO_D_TMP (JYS_NO);
create unique index GP_INFO_D_TMP_U2 on GP_INFO_D_TMP (GP_NUM, JY_DATE, JYS_NO);
create unique index GP_INFO_D_TMP_U3 on GP_INFO_D_TMP (ROW_NUM);
alter table GP_INFO_D_TMP
  add constraint GP_INFO_D_TMP_U1 unique (RECORD_ID);

