﻿create table GP_INFO_T
(
  record_id        NUMBER,
  gp_num           VARCHAR2(10) not null,
  gp_name          VARCHAR2(100),
  jys_no           VARCHAR2(10) not null,
  block_name       VARCHAR2(20),
  market_time      VARCHAR2(10),
  f_stage_industry VARCHAR2(100),
  s_stage_industry VARCHAR2(100),
  t_stage_industry VARCHAR2(100),
  concept          VARCHAR2(500),
  controller_pro   VARCHAR2(50),
  create_date      DATE default sysdate
)
;
comment on column GP_INFO_T.gp_num
  is '股票代码';
comment on column GP_INFO_T.gp_name
  is '股票名称';
comment on column GP_INFO_T.jys_no
  is '交易所';
comment on column GP_INFO_T.block_name
  is '板块';
comment on column GP_INFO_T.market_time
  is '上市日期';
comment on column GP_INFO_T.f_stage_industry
  is '申万一级行业';
comment on column GP_INFO_T.s_stage_industry
  is '申万二级行业';
comment on column GP_INFO_T.t_stage_industry
  is '申万三级行业';
comment on column GP_INFO_T.concept
  is '所涉概念';
comment on column GP_INFO_T.controller_pro
  is '控制人性质';
create index GP_INFO_T_N1 on GP_INFO_T (JYS_NO);
create unique index GP_INFO_T_U1 on GP_INFO_T (RECORD_ID);
create unique index GP_INFO_T_U2 on GP_INFO_T (GP_NUM);
create unique index GP_INFO_T_U3 on GP_INFO_T (GP_NAME, JYS_NO, GP_NUM);
alter table GP_INFO_T
  add constraint PK_GP_INFO_T primary key (GP_NUM, JYS_NO);

