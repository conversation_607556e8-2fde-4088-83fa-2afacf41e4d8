﻿create global temporary table GP_INFO_W_TMP
(
  row_num       NUMBER,
  record_id     NUMBER,
  gp_num        VARCHAR2(20),
  jy_date       DATE,
  open_price    NUMBER,
  high_price    NUMBER,
  low_price     NUMBER,
  close_price   NUMBER,
  volume_amount NUMBER,
  adj_close     NUMBER,
  jys_no        VARCHAR2(10),
  creation_date DATE
)
on commit preserve rows;
create index GP_INFO_W_TMP_N1 on GP_INFO_W_TMP (JYS_NO);
create index GP_INFO_W_TMP_N2 on GP_INFO_W_TMP (GP_NUM);
create index GP_INFO_W_TMP_N3 on GP_INFO_W_TMP (JY_DATE);
create unique index GP_INFO_W_TMP_N4 on GP_INFO_W_TMP (ROW_NUM);
create unique index GP_INFO_W_TMP_U1 on GP_INFO_W_TMP (RECORD_ID);
create unique index GP_INFO_W_TMP_U2 on GP_INFO_W_TMP (GP_NUM, JY_<PERSON>ATE, JYS_NO);

