﻿create table GP_JY_D
(
  record_id        NUMBER,
  gp_num           VARCHAR2(10),
  jy_date          DATE,
  open_price       NUMBER,
  high_price       NUMBER,
  close_price      NUMBER,
  low_price        NUMBER,
  jy_quantity      NUMBER,
  jy_amount        NUMBER,
  fq_factor        NUMBER,
  creation_date    DATE,
  last_update_date DATE,
  attribute1       VARCHAR2(100),
  attribute2       VARCHAR2(100),
  attribute3       VARCHAR2(100),
  attribute4       VARCHAR2(100),
  attribute5       VARCHAR2(100),
  attribute6       VARCHAR2(100),
  attribute7       VARCHAR2(100),
  attribute8       VARCHAR2(100),
  attribute9       VARCHAR2(100),
  attribute10      VARCHAR2(100)
)
;
create index GP_JY_D_N1 on GP_JY_D (GP_NUM);
create index GP_JY_D_N2 on GP_JY_D (JY_DATE);
create unique index GP_JY_D_U1 on GP_JY_D (RECORD_ID);
create unique index GP_JY_D_U2 on GP_JY_D (GP_NUM, JY_DATE);

