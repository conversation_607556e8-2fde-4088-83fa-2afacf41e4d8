﻿create table GP_LHB_DETAIL
(
  record_id     VARCHAR2(32) default sys_guid(),
  parent_id     VARCHAR2(32),
  tradedate     VARCHAR2(18),
  bs_type       VARCHAR2(10),
  symbol        VARCHAR2(10),
  reson_type    VARCHAR2(10),
  comcode       VARCHAR2(20),
  comname       VARCHAR2(100),
  buyamount     NUMBER,
  sellamount    NUMBER,
  netamount     NUMBER,
  creation_date DATE default sysdate
)
;
comment on column GP_LHB_DETAIL.parent_id
  is '龙虎榜记录号';
comment on column GP_LHB_DETAIL.tradedate
  is '业务日期';
comment on column GP_LHB_DETAIL.bs_type
  is '买卖标示';
comment on column GP_LHB_DETAIL.symbol
  is '股票代码';
comment on column GP_LHB_DETAIL.reson_type
  is '上榜原因编号';
comment on column GP_LHB_DETAIL.comcode
  is '交易营业所编号';
comment on column GP_LHB_DETAIL.comname
  is '交易营业所';
comment on column GP_LHB_DETAIL.buyamount
  is '买入金额(万元)';
comment on column GP_LHB_DETAIL.sellamount
  is '卖出金额(万元)';
comment on column GP_LHB_DETAIL.netamount
  is '净买入(万元)';
create index GP_LHB_DETAIL_N1 on GP_LHB_DETAIL (PARENT_ID);

