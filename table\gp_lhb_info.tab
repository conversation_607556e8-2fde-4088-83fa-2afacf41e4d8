﻿create table GP_LHB_INFO
(
  tradedate     VARCHAR2(18),
  branch_code   VARCHAR2(8) default '8888',
  record_id     VARCHAR2(32) default sys_guid(),
  lhb_reson     VARCHAR2(200),
  line_num      NUMBER,
  gp_num        VARCHAR2(10),
  gp_name       VARCHAR2(30),
  close_price   NUMBER,
  rate          NUMBER,
  trade_qty     NUMBER,
  trade_amount  NUMBER,
  creation_date DATE default sysdate,
  reson_type    VARCHAR2(10)
)
nologging;
comment on column GP_LHB_INFO.tradedate
  is '业务日期';
comment on column GP_LHB_INFO.lhb_reson
  is '上榜理由';
comment on column GP_LHB_INFO.line_num
  is '序号';
comment on column GP_LHB_INFO.gp_num
  is '股票代码';
comment on column GP_LHB_INFO.gp_name
  is '股票名称';
comment on column GP_LHB_INFO.close_price
  is '收盘价(元)';
comment on column GP_LHB_INFO.rate
  is '对应值(%)';
comment on column GP_LHB_INFO.trade_qty
  is '成交量(万股)';
comment on column GP_LHB_INFO.trade_amount
  is '成交额(万元)';
comment on column GP_LHB_INFO.creation_date
  is '创建日期';

