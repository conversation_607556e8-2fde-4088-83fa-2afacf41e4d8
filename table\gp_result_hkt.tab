﻿create table GP_RESULT_HKT
(
  record_id     NUMBER,
  batch_id      NUMBER,
  tactics_code  VARCHAR2(20),
  gp_num        VARCHAR2(10),
  gp_name       VARCHAR2(100),
  u_ldate       DATE,
  u_lhight      NUMBER,
  u_rdate       DATE,
  u_rhight      NUMBER,
  v_rdate       DATE,
  v_rhight      NUMBER,
  create_date   DATE,
  sucess_date   DATE,
  sucess_price  NUMBER,
  lower_date    DATE,
  lower_price   NUMBER,
  u_low1_price  NUMBER,
  u_low1_date   DATE,
  u_low2_price  NUMBER,
  u_low2_date   DATE,
  ma_num        NUMBER,
  u_lowest      NUMBER,
  u_lowest_date DATE,
  v_lowest      NUMBER,
  v_lowest_date DATE,
  allqty_rate   NUMBER,
  avgqty_rate   NUMBER
)
;
create index GP_RESULT_HKT_N1 on GP_RESULT_HKT (BATCH_ID);
create index GP_RESULT_HKT_N2 on GP_RESULT_HKT (TACTICS_CODE);
create index GP_RESULT_HKT_N3 on GP_RESULT_HKT (GP_NUM, GP_NAME);
create index GP_RESULT_HKT_N4 on GP_RESULT_HKT (U_LDATE, U_LHIGHT);
create index GP_RESULT_HKT_N5 on GP_RESULT_HKT (U_RDATE, U_RHIGHT);
create index GP_RESULT_HKT_N6 on GP_RESULT_HKT (V_RDATE, V_RHIGHT);
create unique index GP_RESULT_HKT_U1 on GP_RESULT_HKT (RECORD_ID);

