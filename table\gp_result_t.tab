﻿create table GP_RESULT_T
(
  record_id     NUMBER,
  batch_id      NUMBER,
  tactics_code  VARCHAR2(20),
  gp_num        VARCHAR2(10),
  gp_name       VARCHAR2(100),
  u_ldate       DATE,
  u_lhight      NUMBER,
  u_rdate       DATE,
  u_rhight      NUMBER,
  v_rdate       DATE,
  v_rhight      NUMBER,
  create_date   DATE default sysdate,
  sucess_date   DATE,
  sucess_price  NUMBER,
  lower_date    DATE,
  lower_price   NUMBER,
  u_low1_price  NUMBER,
  u_low1_date   DATE,
  u_low2_price  NUMBER,
  u_low2_date   DATE,
  ma_num        NUMBER,
  u_lowest      NUMBER,
  u_lowest_date DATE,
  v_lowest      NUMBER,
  v_lowest_date DATE,
  allqty_rate   NUMBER,
  avgqty_rate   NUMBER,
  attribute1    VARCHAR2(50),
  attribute2    VARCHAR2(50),
  attribute3    VARCHAR2(50),
  attribute4    VARCHAR2(50),
  attribute5    VARCHAR2(50)
)
nologging;
comment on column GP_RESULT_T.gp_num
  is '股票编号';
comment on column GP_RESULT_T.gp_name
  is '股票名称';
comment on column GP_RESULT_T.u_ldate
  is 'U型左侧高点周';
comment on column GP_RESULT_T.u_lhight
  is 'U型左侧高点价格';
comment on column GP_RESULT_T.u_rdate
  is 'U型右侧高点周';
comment on column GP_RESULT_T.u_rhight
  is 'U型右侧高点价格';
comment on column GP_RESULT_T.v_rdate
  is 'V型右侧高点周';
comment on column GP_RESULT_T.v_rhight
  is 'V型右侧高点价格';
comment on column GP_RESULT_T.sucess_date
  is '最高点周九十天';
comment on column GP_RESULT_T.sucess_price
  is '最高点价格九十天';
comment on column GP_RESULT_T.lower_date
  is '最低点周九十天';
comment on column GP_RESULT_T.lower_price
  is '内最低点价格九十天';
comment on column GP_RESULT_T.u_low1_price
  is '最低点价格三分之一';
comment on column GP_RESULT_T.u_low1_date
  is '最低点周三分之一';
comment on column GP_RESULT_T.u_low2_price
  is '最低点价格四分之一';
comment on column GP_RESULT_T.u_low2_date
  is '最低点周四分之一';
comment on column GP_RESULT_T.ma_num
  is 'MA支撑点';
comment on column GP_RESULT_T.u_lowest
  is 'U型最低点价格';
comment on column GP_RESULT_T.u_lowest_date
  is 'U型最低点周';
comment on column GP_RESULT_T.v_lowest
  is 'V型最低点价格';
comment on column GP_RESULT_T.v_lowest_date
  is 'V型最低点周';
comment on column GP_RESULT_T.allqty_rate
  is '累计换手率';
comment on column GP_RESULT_T.avgqty_rate
  is '平均换手率';
comment on column GP_RESULT_T.attribute1
  is '突破日收盘价';
comment on column GP_RESULT_T.attribute2
  is '是否突破';
comment on column GP_RESULT_T.attribute3
  is '最近突破日';
create index GP_RESULT_T_N1 on GP_RESULT_T (BATCH_ID);
create index GP_RESULT_T_N2 on GP_RESULT_T (TACTICS_CODE);
create index GP_RESULT_T_N3 on GP_RESULT_T (GP_NUM, GP_NAME);
create index GP_RESULT_T_N4 on GP_RESULT_T (U_LDATE, U_LHIGHT);
create index GP_RESULT_T_N5 on GP_RESULT_T (U_RDATE, U_RHIGHT);
create index GP_RESULT_T_N6 on GP_RESULT_T (V_RDATE, V_RHIGHT);
create unique index GP_RESULT_T_U1 on GP_RESULT_T (RECORD_ID);

