﻿create table GP_TREND_RESULT
(
  record_id     NUMBER not null,
  gp_num        VARCHAR2(10) not null,
  dates_type    NUMBER not null,
  down_a0       DATE,
  down_a2       DATE,
  point_a       NUMBER,
  point_b       NUMBER,
  top_a0        NUMBER,
  top_a2        NUMBER,
  tp_b0         DATE,
  up_a1         DATE,
  up_b2         DATE,
  point_c       NUMBER,
  point_d       NUMBER,
  low_a1        NUMBER,
  low_b2        NUMBER,
  date_b1       DATE,
  attribute1    VARCHAR2(100),
  attribute2    VARCHAR2(100),
  attribute3    VARCHAR2(100),
  attribute4    VARCHAR2(100),
  attribute5    VARCHAR2(100),
  attribute6    VARCHAR2(100),
  attribute7    VARCHAR2(100),
  attribute8    VARCHAR2(100),
  attribute9    VARCHAR2(100),
  attribute10   VARCHAR2(100),
  attribute11   VARCHAR2(100),
  attribute12   VARCHAR2(100),
  attribute13   VARCHAR2(100),
  attribute14   VARCHAR2(100),
  attribute15   VARCHAR2(100),
  creation_date DATE default sysdate
)
;
comment on column GP_TREND_RESULT.record_id
  is '序号';
comment on column GP_TREND_RESULT.gp_num
  is '股票代码';
comment on column GP_TREND_RESULT.dates_type
  is '时间框架(200天/400天/600天)';
comment on column GP_TREND_RESULT.down_a0
  is 'A0日期(下降点1)';
comment on column GP_TREND_RESULT.down_a2
  is 'A2日期(下降点2)';
comment on column GP_TREND_RESULT.point_a
  is '系数：A ';
comment on column GP_TREND_RESULT.point_b
  is '系数：B ';
comment on column GP_TREND_RESULT.top_a0
  is 'A0最高价';
comment on column GP_TREND_RESULT.top_a2
  is 'A2最高价';
comment on column GP_TREND_RESULT.tp_b0
  is 'B0日期(突破点) ';
comment on column GP_TREND_RESULT.up_a1
  is 'A1日期(上升点1)';
comment on column GP_TREND_RESULT.up_b2
  is 'B2日期(上升点2)';
comment on column GP_TREND_RESULT.point_c
  is '系数：C ';
comment on column GP_TREND_RESULT.point_d
  is '系数：D ';
comment on column GP_TREND_RESULT.low_a1
  is 'A1最低价';
comment on column GP_TREND_RESULT.low_b2
  is 'B2最低价';
comment on column GP_TREND_RESULT.date_b1
  is 'B1日期';
create unique index GP_TREND_RESULT_U1 on GP_TREND_RESULT (RECORD_ID);
create unique index GP_TREND_RESULT_U2 on GP_TREND_RESULT (GP_NUM, DATES_TYPE, ATTRIBUTE1);

