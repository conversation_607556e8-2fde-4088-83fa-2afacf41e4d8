﻿create table GP_US_W
(
  record_id     NUMBER,
  gp_num        VARCHAR2(10),
  jy_date       DATE,
  week_num      VARCHAR2(10),
  open_price    NUMBER,
  high_price    NUMBER,
  close_price   NUMBER,
  low_price     NUMBER,
  volume_amount NUMBER,
  adj_close     NUMBER,
  creation_date DATE,
  attribute1    DATE,
  attribute2    VARCHAR2(100),
  attribute3    VARCHAR2(100),
  attribute4    VARCHAR2(100),
  attribute5    VARCHAR2(100),
  attribute6    VARCHAR2(100),
  attribute7    VARCHAR2(100),
  attribute8    VARCHAR2(100),
  attribute9    VARCHAR2(100),
  attribute10   VARCHAR2(100)
)
;
create unique index GP_US_W_U1 on GP_US_W (RECORD_ID);
create unique index GP_US_W_U2 on GP_US_W (GP_NUM, JY_DATE);
create unique index GP_US_W_U3 on GP_US_W (GP_NUM, WEEK_NUM);

