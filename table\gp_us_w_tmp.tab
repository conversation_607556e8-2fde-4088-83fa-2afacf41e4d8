﻿create global temporary table GP_US_W_TMP
(
  row_num       NUMBER,
  record_id     NUMBER,
  gp_num        VARCHAR2(10),
  jy_date       DATE,
  week_num      VARCHAR2(10),
  open_price    NUMBER,
  high_price    NUMBER,
  close_price   NUMBER,
  low_price     NUMBER,
  volume_amount NUMBER,
  adj_close     NUMBER,
  creation_date DATE,
  attribute1    DATE,
  attribute2    VARCHAR2(100),
  attribute3    VARCHAR2(100),
  attribute4    VARCHAR2(100),
  attribute5    VARCHAR2(100),
  attribute6    VARCHAR2(100),
  attribute7    VARCHAR2(100),
  attribute8    VARCHAR2(100),
  attribute9    VARCHAR2(100),
  attribute10   VARCHAR2(100)
)
on commit preserve rows;
create unique index GP_US_W_TMP_U1 on GP_US_W_TMP (RECORD_ID);
create unique index GP_US_W_TMP_U2 on GP_US_W_TMP (JY_DATE, GP_NUM);

