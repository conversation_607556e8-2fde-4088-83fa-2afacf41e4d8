﻿create table GP_WEEK_INFO
(
  record_id   NUMBER,
  d_date      DATE not null,
  week_d      VARCHAR2(10),
  week_num    VARCHAR2(10),
  open_flag   VARCHAR2(2),
  create_date DATE,
  attribute1  VARCHAR2(100),
  attribute2  VARCHAR2(100),
  attribute3  VARCHAR2(100),
  attribute4  VARCHAR2(100),
  attribute5  VARCHAR2(100),
  attribute6  VARCHAR2(100),
  attribute7  VARCHAR2(100),
  attribute8  VARCHAR2(100),
  attribute9  VARCHAR2(100),
  attribute10 VARCHAR2(100)
)
;
comment on column GP_WEEK_INFO.week_d
  is '星期几';
comment on column GP_WEEK_INFO.week_num
  is '第几周';
create index GP_WEEK_INFO_N1 on GP_WEEK_INFO (D_DATE, WEEK_D, WEEK_NUM, OPEN_FLAG);
create unique index GP_WEEK_INFO_U1 on GP_WEEK_INFO (RECORD_ID);
create unique index GP_WEEK_INFO_U2 on GP_WEEK_INFO (D_DATE);
alter table GP_WEEK_INFO
  add constraint PK_GP_WEEK_INFO primary key (D_DATE);

