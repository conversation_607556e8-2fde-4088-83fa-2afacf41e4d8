﻿create table TB_GP_POOL
(
  gp_num        VARCHAR2(10),
  add_date      VARCHAR2(8),
  add_type      VARCHAR2(50),
  join_reasons  VARCHAR2(1000),
  created_by    VARCHAR2(20),
  creation_date VARCHAR2(30),
  unique_rowid  VARCHAR2(32) default sys_guid() not null
)
;
comment on column TB_GP_POOL.gp_num
  is '股票代码';
comment on column TB_GP_POOL.add_date
  is '加入日期';
comment on column TB_GP_POOL.add_type
  is '加入类型';
comment on column TB_GP_POOL.join_reasons
  is '加入理由';
comment on column TB_GP_POOL.created_by
  is '创建人';
comment on column TB_GP_POOL.creation_date
  is '创建日期';
alter table TB_GP_POOL
  add constraint TB_GP_POOL_PK primary key (UNIQUE_ROWID);

