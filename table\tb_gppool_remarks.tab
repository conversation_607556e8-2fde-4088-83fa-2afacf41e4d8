﻿create table TB_GPPOOL_REMARKS
(
  remark           <PERSON><PERSON><PERSON><PERSON>2(1800) not null,
  parent_id        VARCHAR2(32) not null,
  created_by       <PERSON><PERSON><PERSON><PERSON>2(20),
  creation_date    VARCHAR2(30) default TO_CHAR(sysdate,'YYYY-MM-DD HH24:MI:SS'),
  last_update_by   <PERSON><PERSON><PERSON><PERSON><PERSON>(20),
  last_update_date VA<PERSON>HAR2(30) default TO_CHAR(sysdate,'YYYY-MM-DD HH24:MI:SS'),
  uniquerowid      VARCHAR2(32) default sys_guid() not null
)
;
alter table TB_GPPOOL_REMARKS
  add constraint TB_GPPOOL_REMARKS_PK primary key (UNIQUEROWID);

