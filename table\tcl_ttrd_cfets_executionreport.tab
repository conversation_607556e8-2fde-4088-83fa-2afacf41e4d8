﻿create table TCL_TTRD_CFETS_EXECUTIONREPORT
(
  busi_date                   VARCHAR2(8),
  branch_code                 VARCHAR2(6),
  epid                        VARCHAR2(80),
  trddate                     VARCHAR2(80),
  trdtime                     VARCHAR2(80),
  execid                      VARCHAR2(80),
  trdtype                     VARCHAR2(80),
  marketindicator             VA<PERSON><PERSON>R<PERSON>(80),
  dirction                    VARCHAR2(80),
  party                       VARCHAR2(80),
  trader                      VARCHAR2(80),
  cash_acct_number            VARCHAR2(80),
  cash_acct_name              VA<PERSON>HAR2(80),
  settl_bank_name             VARCHAR2(80),
  settl_bank_sortcode         VARCHAR2(80),
  custodian_acct_name         VA<PERSON>HAR2(80),
  custodian_name              VA<PERSON><PERSON>R2(80),
  custodain_acct_number       VARCHAR2(80),
  counterparty                <PERSON><PERSON><PERSON><PERSON>2(80),
  cp_trader                   VARCHAR2(80),
  cp_cash_acct_number         VA<PERSON>HAR2(80),
  cp_cash_acct_name           VA<PERSON><PERSON>R2(80),
  cp_settl_bank_name          VA<PERSON><PERSON>R2(80),
  cp_settl_bank_sortcode      VA<PERSON>HAR2(80),
  cp_custodian_acct_name      VA<PERSON><PERSON>R2(80),
  cp_custodian_name           VARCHAR2(80),
  cp_custodain_acct_number    VARCHAR2(80),
  i_code                      VARCHAR2(80),
  a_type                      VARCHAR2(80),
  m_type                      VARCHAR2(80),
  i_name                      VARCHAR2(80),
  u_i_code                    VARCHAR2(80),
  u_a_type                    VARCHAR2(80),
  u_m_type                    VARCHAR2(80),
  u_i_name                    VARCHAR2(80),
  price                       VARCHAR2(80),
  dirty_price                 VARCHAR2(80),
  ytm                         VARCHAR2(80),
  u_clear_price               VARCHAR2(80),
  u_clear_price2              VARCHAR2(80),
  u_ai_amt                    VARCHAR2(80),
  u_ai_amt2                   VARCHAR2(80),
  u_dirty_price               VARCHAR2(80),
  u_dirty_price2              VARCHAR2(80),
  u_ytm                       VARCHAR2(80),
  u_ytm2                      VARCHAR2(80),
  trdfv                       VARCHAR2(80),
  trdcashamt                  VARCHAR2(80),
  trdfee                      VARCHAR2(80),
  setdate                     VARCHAR2(80),
  setdate2                    VARCHAR2(80),
  tradelimitdays              VARCHAR2(80),
  cashholdingdays             VARCHAR2(80),
  settlcurramt                VARCHAR2(80),
  settlcurramt2               VARCHAR2(80),
  deliverytype                VARCHAR2(80),
  deliverytype2               VARCHAR2(20),
  ai_totalamt                 VARCHAR2(20),
  exectype                    VARCHAR2(20),
  marginrequired              VARCHAR2(20),
  mg_replacement              VARCHAR2(80),
  mg_submitdate               VARCHAR2(20),
  mg_cashamt                  VARCHAR2(20),
  cp_mg_cashamt               VARCHAR2(20),
  mg_parvalue                 VARCHAR2(20),
  cp_mg_parvalue              VARCHAR2(20),
  mg_cash_acct_number         VARCHAR2(80),
  mg_cash_acct_name           VARCHAR2(80),
  mg_settl_bank_name          VARCHAR2(80),
  mg_settl_bank_sortcode      VARCHAR2(80),
  mg_custodian_acct_name      VARCHAR2(80),
  mg_custodian_name           VARCHAR2(80),
  mg_custodain_acct_number    VARCHAR2(80),
  cp_mg_replacement           VARCHAR2(20),
  cp_mg_submitdate            VARCHAR2(20),
  cp_mg_cash_acct_number      VARCHAR2(80),
  cp_mg_cash_acct_name        VARCHAR2(80),
  cp_mg_settl_bank_name       VARCHAR2(80),
  cp_mg_settl_bank_sortcode   VARCHAR2(80),
  cp_mg_custodian_acct_name   VARCHAR2(80),
  cp_mg_custodian_name        VARCHAR2(80),
  cp_mg_custodain_acct_number VARCHAR2(80),
  remark                      VARCHAR2(80),
  quoteid                     VARCHAR2(80),
  orderid                     VARCHAR2(80),
  bnd_trdtype                 VARCHAR2(20),
  termtomaturity              VARCHAR2(20),
  fee_amt                     VARCHAR2(80),
  disputesettlmethod          VARCHAR2(80),
  fra_discount                VARCHAR2(20),
  fra_interestfixdate         VARCHAR2(20),
  fra_couponpaymentdate       VARCHAR2(20),
  fra_businessdayconv         VARCHAR2(20),
  fra_daycount                VARCHAR2(20),
  irs_interestaccuraldaysadj  VARCHAR2(20),
  irs_couponpaymentdatereset  VARCHAR2(20),
  status                      VARCHAR2(20),
  transacttime                VARCHAR2(80),
  trademethod                 NUMBER,
  clearing_method             NUMBER,
  ai                          NUMBER,
  imp_time                    DATE
)
;
comment on column TCL_TTRD_CFETS_EXECUTIONREPORT.epid
  is ' 主键';
comment on column TCL_TTRD_CFETS_EXECUTIONREPORT.trddate
  is '委托日期 ';
comment on column TCL_TTRD_CFETS_EXECUTIONREPORT.trdtime
  is '委托时间 ';
comment on column TCL_TTRD_CFETS_EXECUTIONREPORT.execid
  is '交易序号 ';
comment on column TCL_TTRD_CFETS_EXECUTIONREPORT.trdtype
  is '交易类别 ';
comment on column TCL_TTRD_CFETS_EXECUTIONREPORT.marketindicator
  is '交易市场 1 信用拆借
2 利率互换
3 利率远期
4 现券
5 债券远期
6 资产支持类债券
8 债券借贷
9 质押式回购
10买断式回购';
comment on column TCL_TTRD_CFETS_EXECUTIONREPORT.dirction
  is '交易方向 1-买入 / 拆入 / 融入 / 逆回购/固定支付/参考利率1支付
4-卖出 / 拆出 / 融出 / 正回购/固定收取/参考利率1收取
';
comment on column TCL_TTRD_CFETS_EXECUTIONREPORT.party
  is '本方 ';
comment on column TCL_TTRD_CFETS_EXECUTIONREPORT.trader
  is '本方交易员 ';
comment on column TCL_TTRD_CFETS_EXECUTIONREPORT.cash_acct_number
  is '本方资金账号 ';
comment on column TCL_TTRD_CFETS_EXECUTIONREPORT.cash_acct_name
  is '本方资金账户户名 ';
comment on column TCL_TTRD_CFETS_EXECUTIONREPORT.settl_bank_name
  is '本方资金开户行 ';
comment on column TCL_TTRD_CFETS_EXECUTIONREPORT.settl_bank_sortcode
  is '本方资金开户行联行行号 ';
comment on column TCL_TTRD_CFETS_EXECUTIONREPORT.custodian_acct_name
  is '本方托管账户户名 ';
comment on column TCL_TTRD_CFETS_EXECUTIONREPORT.custodian_name
  is '本方托管机构 ';
comment on column TCL_TTRD_CFETS_EXECUTIONREPORT.custodain_acct_number
  is '本方托管账号 ';
comment on column TCL_TTRD_CFETS_EXECUTIONREPORT.counterparty
  is '对手方 ';
comment on column TCL_TTRD_CFETS_EXECUTIONREPORT.cp_trader
  is '对方交易员 ';
comment on column TCL_TTRD_CFETS_EXECUTIONREPORT.cp_cash_acct_number
  is '对方资金账号 ';
comment on column TCL_TTRD_CFETS_EXECUTIONREPORT.cp_cash_acct_name
  is '对方资金账户户名 ';
comment on column TCL_TTRD_CFETS_EXECUTIONREPORT.cp_settl_bank_name
  is '对方资金开户行 ';
comment on column TCL_TTRD_CFETS_EXECUTIONREPORT.cp_settl_bank_sortcode
  is '对方资金开户行联行行号 ';
comment on column TCL_TTRD_CFETS_EXECUTIONREPORT.cp_custodian_acct_name
  is '对方托管账户户名 ';
comment on column TCL_TTRD_CFETS_EXECUTIONREPORT.cp_custodian_name
  is '对方托管机构 ';
comment on column TCL_TTRD_CFETS_EXECUTIONREPORT.cp_custodain_acct_number
  is '对方托管账号 ';
comment on column TCL_TTRD_CFETS_EXECUTIONREPORT.i_code
  is '金融工具代码 ';
comment on column TCL_TTRD_CFETS_EXECUTIONREPORT.a_type
  is '资产类型 ';
comment on column TCL_TTRD_CFETS_EXECUTIONREPORT.m_type
  is '市场类型 ';
comment on column TCL_TTRD_CFETS_EXECUTIONREPORT.i_name
  is '金融工具名称 ';
comment on column TCL_TTRD_CFETS_EXECUTIONREPORT.u_i_code
  is '标的债券代码 ';
comment on column TCL_TTRD_CFETS_EXECUTIONREPORT.u_a_type
  is '标的资产类别 ';
comment on column TCL_TTRD_CFETS_EXECUTIONREPORT.u_m_type
  is '标的市场类别 ';
comment on column TCL_TTRD_CFETS_EXECUTIONREPORT.u_i_name
  is '标的债券名称 ';
comment on column TCL_TTRD_CFETS_EXECUTIONREPORT.price
  is '交易价格 ';
comment on column TCL_TTRD_CFETS_EXECUTIONREPORT.dirty_price
  is '交易全价 ';
comment on column TCL_TTRD_CFETS_EXECUTIONREPORT.ytm
  is '到期收益率 ';
comment on column TCL_TTRD_CFETS_EXECUTIONREPORT.u_clear_price
  is '标的首次净价 ';
comment on column TCL_TTRD_CFETS_EXECUTIONREPORT.u_clear_price2
  is '标的到期净价 ';
comment on column TCL_TTRD_CFETS_EXECUTIONREPORT.u_ai_amt
  is '标的首次应计利息 ';
comment on column TCL_TTRD_CFETS_EXECUTIONREPORT.u_ai_amt2
  is '标的到期应计利息 ';
comment on column TCL_TTRD_CFETS_EXECUTIONREPORT.u_dirty_price
  is '标的首次全价 ';
comment on column TCL_TTRD_CFETS_EXECUTIONREPORT.u_dirty_price2
  is '标的到期全价 ';
comment on column TCL_TTRD_CFETS_EXECUTIONREPORT.u_ytm
  is '标的首期收益率 ';
comment on column TCL_TTRD_CFETS_EXECUTIONREPORT.u_ytm2
  is '标的到期收益率 ';
comment on column TCL_TTRD_CFETS_EXECUTIONREPORT.trdfv
  is '交易面额 交易数量';
comment on column TCL_TTRD_CFETS_EXECUTIONREPORT.trdcashamt
  is '交易金额 交易金额';
comment on column TCL_TTRD_CFETS_EXECUTIONREPORT.trdfee
  is '交易费用 交易费用';
comment on column TCL_TTRD_CFETS_EXECUTIONREPORT.setdate
  is '首次结算日期 结算日期';
comment on column TCL_TTRD_CFETS_EXECUTIONREPORT.setdate2
  is '到期结算日期 结算日期';
comment on column TCL_TTRD_CFETS_EXECUTIONREPORT.tradelimitdays
  is '期限 ';
comment on column TCL_TTRD_CFETS_EXECUTIONREPORT.cashholdingdays
  is '实际占款天数 ';
comment on column TCL_TTRD_CFETS_EXECUTIONREPORT.settlcurramt
  is '首期结算金额 ';
comment on column TCL_TTRD_CFETS_EXECUTIONREPORT.settlcurramt2
  is '到期结算金额 ';
comment on column TCL_TTRD_CFETS_EXECUTIONREPORT.deliverytype
  is '首次结算方式 0 - DVP;4 - PUD;5 - DUP;6 - BVB;7-NONE';
comment on column TCL_TTRD_CFETS_EXECUTIONREPORT.deliverytype2
  is '到期结算方式 0 - DVP;4 - PUD;5 - DUP;6 - BVB;7-NONE';
comment on column TCL_TTRD_CFETS_EXECUTIONREPORT.ai_totalamt
  is '应计利息总额 ';
comment on column TCL_TTRD_CFETS_EXECUTIONREPORT.exectype
  is '成交状态 F-成交;H-撤销;5-更新;101-应急
';
comment on column TCL_TTRD_CFETS_EXECUTIONREPORT.marginrequired
  is '有无保证品 Y-有;N-无';
comment on column TCL_TTRD_CFETS_EXECUTIONREPORT.mg_replacement
  is '保证金/券变动标识 Y-可以；N-不可以';
comment on column TCL_TTRD_CFETS_EXECUTIONREPORT.mg_submitdate
  is '保证金/券提交日 ';
comment on column TCL_TTRD_CFETS_EXECUTIONREPORT.mg_cashamt
  is '本方保证金总额 ';
comment on column TCL_TTRD_CFETS_EXECUTIONREPORT.cp_mg_cashamt
  is '对方保证金总额 ';
comment on column TCL_TTRD_CFETS_EXECUTIONREPORT.mg_parvalue
  is '本方保证券总额 ';
comment on column TCL_TTRD_CFETS_EXECUTIONREPORT.cp_mg_parvalue
  is '对方保证券总额 ';
comment on column TCL_TTRD_CFETS_EXECUTIONREPORT.mg_cash_acct_number
  is '本方保证金账号 ';
comment on column TCL_TTRD_CFETS_EXECUTIONREPORT.mg_cash_acct_name
  is '本方保证金账户户名 ';
comment on column TCL_TTRD_CFETS_EXECUTIONREPORT.mg_settl_bank_name
  is '本方保证金开户行 ';
comment on column TCL_TTRD_CFETS_EXECUTIONREPORT.mg_settl_bank_sortcode
  is '本方保证金开户行联行行号 ';
comment on column TCL_TTRD_CFETS_EXECUTIONREPORT.mg_custodian_acct_name
  is '本方保证券托管账户户名 ';
comment on column TCL_TTRD_CFETS_EXECUTIONREPORT.mg_custodian_name
  is '本方保证券托管机构 ';
comment on column TCL_TTRD_CFETS_EXECUTIONREPORT.mg_custodain_acct_number
  is '本方保证券托管账号 ';
comment on column TCL_TTRD_CFETS_EXECUTIONREPORT.cp_mg_replacement
  is '对手方保证金/券变动标识 Y-可以；N-不可以';
comment on column TCL_TTRD_CFETS_EXECUTIONREPORT.cp_mg_submitdate
  is '对方保证金/券提交日 ';
comment on column TCL_TTRD_CFETS_EXECUTIONREPORT.cp_mg_cash_acct_number
  is '对方保证金账号 ';
comment on column TCL_TTRD_CFETS_EXECUTIONREPORT.cp_mg_cash_acct_name
  is '对方保证金账户户名 ';
comment on column TCL_TTRD_CFETS_EXECUTIONREPORT.cp_mg_settl_bank_name
  is '对方保证金开户行 ';
comment on column TCL_TTRD_CFETS_EXECUTIONREPORT.cp_mg_settl_bank_sortcode
  is '对方保证金开户行联行行号 ';
comment on column TCL_TTRD_CFETS_EXECUTIONREPORT.cp_mg_custodian_acct_name
  is '对方保证券托管账户户名 ';
comment on column TCL_TTRD_CFETS_EXECUTIONREPORT.cp_mg_custodian_name
  is '对方保证券托管机构 ';
comment on column TCL_TTRD_CFETS_EXECUTIONREPORT.cp_mg_custodain_acct_number
  is '对方保证券托管账号 ';
comment on column TCL_TTRD_CFETS_EXECUTIONREPORT.remark
  is '备注 ';
comment on column TCL_TTRD_CFETS_EXECUTIONREPORT.quoteid
  is '报价编号 ';
comment on column TCL_TTRD_CFETS_EXECUTIONREPORT.orderid
  is '订单编号 ';
comment on column TCL_TTRD_CFETS_EXECUTIONREPORT.bnd_trdtype
  is '现券交易类别 1-非做市；2-做市';
comment on column TCL_TTRD_CFETS_EXECUTIONREPORT.termtomaturity
  is '待偿期 ';
comment on column TCL_TTRD_CFETS_EXECUTIONREPORT.fee_amt
  is '借贷费用 ';
comment on column TCL_TTRD_CFETS_EXECUTIONREPORT.disputesettlmethod
  is '争议解决方式 ';
comment on column TCL_TTRD_CFETS_EXECUTIONREPORT.fra_discount
  is '远期利率协议贴现率 ';
comment on column TCL_TTRD_CFETS_EXECUTIONREPORT.fra_interestfixdate
  is '远期利率协议利率确定日 ';
comment on column TCL_TTRD_CFETS_EXECUTIONREPORT.fra_couponpaymentdate
  is '远期利率协议支付日 ';
comment on column TCL_TTRD_CFETS_EXECUTIONREPORT.fra_businessdayconv
  is '远期利率协议支付日调整 ';
comment on column TCL_TTRD_CFETS_EXECUTIONREPORT.fra_daycount
  is '远期利率协议计息基准 0-单利；1-复利';
comment on column TCL_TTRD_CFETS_EXECUTIONREPORT.irs_interestaccuraldaysadj
  is '利率互换计息调整 ';
comment on column TCL_TTRD_CFETS_EXECUTIONREPORT.irs_couponpaymentdatereset
  is '利率互换支付日调整 ';
comment on column TCL_TTRD_CFETS_EXECUTIONREPORT.status
  is '状态 1-匹配； 2-未匹配; 3-撤销';

