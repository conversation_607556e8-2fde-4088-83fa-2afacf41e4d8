# 趋势线策略分析系统

## 概述

本系统是Oracle `gp_Trend_pkg` 包的Python重构版本，实现了完整的趋势线策略分析功能。系统通过识别股票价格的下降和上升趋势线，检测突破信号，为投资决策提供技术分析支持。

## 主要功能

### 1. 趋势线分析
- **下降趋势线识别**：分析股票价格的下降趋势，计算趋势线斜率和截距
- **上升趋势线识别**：分析股票价格的上升趋势，找出最优上升趋势线
- **多时间框架支持**：支持200天、400天等不同时间框架的分析

### 2. 突破检测
- **价格突破确认**：检测价格突破趋势线的信号
- **交易量确认**：结合交易量变化确认突破有效性
- **连续确认机制**：要求连续3天确认突破信号

### 3. 双重计算方法
- **自然数计算**：基于价格绝对值的线性计算
- **自然对数计算**：基于价格对数值的指数计算
- **结果对比**：提供两种方法的计算结果供参考

### 4. 数据管理
- **自动建表**：自动创建MySQL结果表
- **数据同步**：与sw_py系统共享数据库基础设施
- **批量处理**：支持大批量股票数据的高效处理

## 系统架构

```
pkg/trend/
├── __init__.py              # 包初始化文件
├── config.py                # 配置参数
├── models.py                # 数据库模型
├── trend_analyzer.py        # 趋势分析核心算法
├── main.py                  # 主程序
├── run_trend_analysis.py    # 运行脚本
└── README.md               # 说明文档
```

## 核心算法

### 1. 下降趋势分析
1. 识别指定时间框架内的最低高点和最高高点
2. 计算下降趋势线的斜率和截距
3. 验证趋势线的有效性

### 2. 突破检测算法
```python
# 自然数突破检测
if close_price > trend_line_price and volume > avg_volume:
    breakthrough_confirmed = True

# 自然对数突破检测  
if close_price > exp(ln_trend_line_price) * 1.03:
    breakthrough_confirmed = True
```

### 3. 上升趋势分析
1. 在突破点后寻找上升趋势
2. 计算最小斜率的上升趋势线
3. 记录上升趋势的关键点位

## 配置说明

### 时间框架配置
```python
TREND_CONFIG = {
    'time_frames': [200, 400],  # 分析的时间框架（天）
    'trend_validation': {
        'check_days': 3,           # 趋势验证天数
        'price_tolerance': 1.03,   # 价格容忍度
    }
}
```

### 突破确认参数
```python
'breakout_confirmation': {
    'consecutive_days': 3,     # 连续确认天数
    'price_threshold': 1.03,   # 价格突破阈值
    'volume_threshold': 1.5,   # 交易量阈值
}
```

## 使用方法

### 1. 基本使用
```python
from pkg.trend.main import TrendAnalysisMain

# 创建分析器
analyzer = TrendAnalysisMain()

# 运行分析
analyzer.run_analysis()
```

### 2. 命令行使用
```bash
# 基本运行
python pkg/trend/run_trend_analysis.py

# 调试模式
python pkg/trend/run_trend_analysis.py --debug

# 测试单只股票
python pkg/trend/run_trend_analysis.py --test-stock 601002

# 启用详细日志
python pkg/trend/run_trend_analysis.py --detailed-log
```

### 3. 自定义配置
```python
from pkg.trend.config import DEBUG_CONFIG

# 启用调试模式
DEBUG_CONFIG['test_single_stock'] = '601002'
DEBUG_CONFIG['enable_detailed_logging'] = True
```

## 数据库表结构

### gp_trend_result 表
| 字段名 | 类型 | 说明 |
|--------|------|------|
| record_id | BIGINT | 主键ID |
| gp_num | VARCHAR(10) | 股票代码 |
| dates_type | INT | 时间框架 |
| down_a0 | DATE | 下降趋势第一高点日期 |
| down_a2 | DATE | 下降趋势第二高点日期 |
| point_a | DECIMAL(15,8) | 趋势线斜率系数A |
| point_b | DECIMAL(15,8) | 趋势线截距系数B |
| tp_b0 | DATE | 突破确认日期 |
| attribute1 | VARCHAR(100) | 计算类型（自然数/自然对数） |
| attribute2 | VARCHAR(100) | 突破标志（Y/N） |

## 性能优化

### 1. 多线程处理
- 支持多线程并行分析
- 可配置线程数量和批次大小
- 自动负载均衡

### 2. 数据库优化
- 使用临时表提高查询效率
- 批量插入减少数据库交互
- 索引优化提升查询性能

### 3. 内存管理
- 分批处理避免内存溢出
- 及时释放临时数据
- 可配置的缓存策略

## 日志和监控

### 1. 日志级别
- INFO：基本运行信息
- DEBUG：详细调试信息
- ERROR：错误信息
- WARNING：警告信息

### 2. 监控指标
- 处理股票数量
- 成功/失败统计
- 执行时间统计
- 内存使用情况

## 与Oracle版本的对应关系

| Oracle组件 | Python组件 | 说明 |
|------------|------------|------|
| Trend_main | main.py | 主程序入口 |
| Trend_Analy | TrendAnalyzer.analyze_single_stock | 单股分析 |
| check_tl | TrendLineValidator.check_trend_line | 趋势线验证 |
| GP_TREND_RESULT | gp_trend_result表 | 结果存储 |
| GP_JY_D_TMP | gp_jy_d_tmp临时表 | 数据处理 |

## 注意事项

1. **数据依赖**：需要sw_py系统的stock_daily_data表数据
2. **数据库权限**：需要MySQL数据库的读写权限
3. **内存要求**：建议至少4GB可用内存
4. **执行时间**：全量分析可能需要数小时，建议在非交易时间运行

## 故障排除

### 常见问题
1. **数据库连接失败**：检查数据库配置和网络连接
2. **内存不足**：减少线程数量或批次大小
3. **数据缺失**：确认stock_daily_data表有足够的历史数据
4. **计算结果异常**：检查配置参数是否正确

### 调试方法
```python
# 启用详细日志
DEBUG_CONFIG['enable_detailed_logging'] = True

# 测试单只股票
DEBUG_CONFIG['test_single_stock'] = '601002'

# 打印调试信息
DEBUG_CONFIG['print_debug_info'] = True
```
