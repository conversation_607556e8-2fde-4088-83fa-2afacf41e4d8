"""
趋势线策略分析包
对应Oracle gp_Trend_pkg的Python重构版本

主要功能：
1. 趋势线分析 - 识别股票价格的下降和上升趋势线
2. 突破检测 - 检测价格突破趋势线的信号
3. 多时间框架分析 - 支持200天、400天等不同时间框架
4. 自然数和自然对数双重计算 - 提供两种计算方法的结果
5. 邮件通知 - 分析完成后发送结果通知

使用方法：
    from pkg.trend.main import TrendAnalysisMain
    
    analyzer = TrendAnalysisMain()
    analyzer.run_analysis()

或者直接运行：
    python pkg/trend/run_trend_analysis.py
"""

from .main import TrendAnalysisMain
from .trend_analyzer import TrendAnalyzer, TrendLineValidator
from .models import TrendDatabaseManager
from .config import TREND_CONFIG, THREADING_CONFIG, DEBUG_CONFIG

__version__ = "1.0.0"
__author__ = "Trend Analysis Team"
__description__ = "趋势线策略分析系统 - Oracle gp_Trend_pkg Python重构版"

__all__ = [
    'TrendAnalysisMain',
    'TrendAnalyzer', 
    'TrendLineValidator',
    'TrendDatabaseManager',
    'TREND_CONFIG',
    'THREADING_CONFIG', 
    'DEBUG_CONFIG'
]
