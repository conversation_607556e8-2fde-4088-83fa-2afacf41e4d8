"""
趋势分析配置模块
"""

# 导入主要配置
from .email_config import EMAIL_CONFIG

# 从主配置文件导入其他配置
import sys
import os

# 添加trend根目录到路径
trend_root = os.path.dirname(os.path.dirname(__file__))
if trend_root not in sys.path:
    sys.path.insert(0, trend_root)

try:
    # 直接导入trend根目录下的config.py文件
    config_file_path = os.path.join(trend_root, 'config.py')
    if os.path.exists(config_file_path):
        # 使用exec读取配置文件
        config_globals = {}
        with open(config_file_path, 'r', encoding='utf-8') as f:
            exec(f.read(), config_globals)

        TREND_CONFIG = config_globals.get('TREND_CONFIG', {})
        THREADING_CONFIG = config_globals.get('THREADING_CONFIG', {})
        DEBUG_CONFIG = config_globals.get('DEBUG_CONFIG', {})
        MATH_CONFIG = config_globals.get('MATH_CONFIG', {})
        DATA_FILTER_CONFIG = config_globals.get('DATA_FILTER_CONFIG', {})
    else:
        raise ImportError(f"Config file not found: {config_file_path}")
except Exception as e:
    print(f"配置导入失败: {e}")
    # 如果无法导入，使用默认配置
    TREND_CONFIG = {}
    THREADING_CONFIG = {}
    DEBUG_CONFIG = {}
    MATH_CONFIG = {}
    DATA_FILTER_CONFIG = {}

__all__ = ['EMAIL_CONFIG', 'TREND_CONFIG', 'THREADING_CONFIG', 'DEBUG_CONFIG', 'MATH_CONFIG', 'DATA_FILTER_CONFIG']
