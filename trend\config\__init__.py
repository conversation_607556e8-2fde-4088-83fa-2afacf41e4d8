"""
趋势分析配置模块
"""

# 导入主要配置
from .email_config import EMAIL_CONFIG

# 从主配置文件导入其他配置
import sys
import os

# 添加trend根目录到路径
trend_root = os.path.dirname(os.path.dirname(__file__))
if trend_root not in sys.path:
    sys.path.insert(0, trend_root)

try:
    # 导入trend根目录下的config.py
    import config as trend_config
    TREND_CONFIG = getattr(trend_config, 'TREND_CONFIG', {})
    THREADING_CONFIG = getattr(trend_config, 'THREADING_CONFIG', {})
    DEBUG_CONFIG = getattr(trend_config, 'DEBUG_CONFIG', {})
    MATH_CONFIG = getattr(trend_config, 'MATH_CONFIG', {})
    DATA_FILTER_CONFIG = getattr(trend_config, 'DATA_FILTER_CONFIG', {})
except ImportError:
    # 如果无法导入，使用默认配置
    TREND_CONFIG = {}
    THREADING_CONFIG = {}
    DEBUG_CONFIG = {}
    MATH_CONFIG = {}
    DATA_FILTER_CONFIG = {}

__all__ = ['EMAIL_CONFIG', 'TREND_CONFIG', 'THREADING_CONFIG', 'DEBUG_CONFIG', 'MATH_CONFIG', 'DATA_FILTER_CONFIG']
