"""
趋势分析配置示例文件
展示如何自定义配置参数
"""

# 示例：自定义趋势分析配置
CUSTOM_TREND_CONFIG = {
    # 时间框架配置 - 可以添加更多时间框架
    'time_frames': [200, 400, 600],  # 增加600天框架
    
    # 趋势线验证参数
    'trend_validation': {
        'check_days': 5,           # 增加验证天数到5天
        'price_tolerance': 1.05,   # 提高价格容忍度到5%
    },
    
    # 平均交易量计算参数
    'avg_volume': {
        'calculation_days': 20,    # 减少到20天平均
        'volume_multiplier': 1.5,  # 提高交易量倍数
    },
    
    # 突破确认参数
    'breakout_confirmation': {
        'consecutive_days': 5,     # 增加连续确认天数
        'price_threshold': 1.05,   # 提高价格突破阈值
        'volume_threshold': 2.0,   # 提高交易量阈值
    },
    
    # 上升趋势分析参数
    'uptrend_analysis': {
        'min_gap_days': 30,        # 增加最小间隔天数
    },
    
    # 数据库配置
    'database': {
        'temp_table_name': 'gp_jy_d_tmp',
        'result_table_name': 'gp_trend_result_custom',  # 自定义结果表名
        'sequence_name': 'gp_trend_result_s',
    },
    
    # 邮件配置
    'email': {
        'default_subject': 'CUSTOM_TREND_ANALYSIS',
        'tactics_code': 'TACTICS_CUSTOM',
        'branch_code': '9999',
    }
}

# 示例：高性能配置（适用于大数据量）
HIGH_PERFORMANCE_CONFIG = {
    'max_workers': 8,              # 增加线程数
    'timeout': 3600,               # 增加超时时间到1小时
    'chunk_size': 100,             # 增加批次大小
    'batch_insert_size': 2000,     # 增加批量插入大小
}

# 示例：调试配置
DEBUG_EXAMPLE_CONFIG = {
    'enable_detailed_logging': True,
    'save_intermediate_data': True,
    'test_single_stock': '000001',     # 测试平安银行
    'print_debug_info': True,
}

# 示例：保守配置（适用于小内存环境）
CONSERVATIVE_CONFIG = {
    'max_workers': 2,              # 减少线程数
    'timeout': 900,                # 15分钟超时
    'chunk_size': 20,              # 减少批次大小
    'batch_insert_size': 500,      # 减少批量插入大小
}

# 示例：如何应用自定义配置
def apply_custom_config():
    """应用自定义配置的示例"""
    
    # 方法1：直接修改配置
    from config import TREND_CONFIG, THREADING_CONFIG, DEBUG_CONFIG
    
    # 更新趋势配置
    TREND_CONFIG.update(CUSTOM_TREND_CONFIG)
    
    # 更新线程配置
    THREADING_CONFIG.update(HIGH_PERFORMANCE_CONFIG)
    
    # 更新调试配置
    DEBUG_CONFIG.update(DEBUG_EXAMPLE_CONFIG)

# 示例：根据环境选择配置
def get_config_by_environment(env='production'):
    """根据环境获取配置"""
    
    if env == 'development':
        return {
            'trend': CUSTOM_TREND_CONFIG,
            'threading': CONSERVATIVE_CONFIG,
            'debug': DEBUG_EXAMPLE_CONFIG
        }
    elif env == 'testing':
        return {
            'trend': CUSTOM_TREND_CONFIG,
            'threading': CONSERVATIVE_CONFIG,
            'debug': {
                'enable_detailed_logging': True,
                'test_single_stock': '601002',
                'print_debug_info': False,
            }
        }
    elif env == 'production':
        return {
            'trend': CUSTOM_TREND_CONFIG,
            'threading': HIGH_PERFORMANCE_CONFIG,
            'debug': {
                'enable_detailed_logging': False,
                'save_intermediate_data': False,
                'test_single_stock': None,
                'print_debug_info': False,
            }
        }
    else:
        raise ValueError(f"未知环境: {env}")

# 示例：配置验证函数
def validate_config(config):
    """验证配置的有效性"""
    
    required_keys = [
        'time_frames',
        'trend_validation',
        'breakout_confirmation'
    ]
    
    for key in required_keys:
        if key not in config:
            raise ValueError(f"缺少必需的配置项: {key}")
    
    # 验证时间框架
    if not isinstance(config['time_frames'], list) or not config['time_frames']:
        raise ValueError("time_frames必须是非空列表")
    
    # 验证数值范围
    if config['trend_validation']['price_tolerance'] <= 1.0:
        raise ValueError("price_tolerance必须大于1.0")
    
    return True

# 使用示例
if __name__ == "__main__":
    # 示例1：应用自定义配置
    print("应用自定义配置...")
    apply_custom_config()
    
    # 示例2：根据环境获取配置
    print("获取开发环境配置...")
    dev_config = get_config_by_environment('development')
    print(f"开发环境时间框架: {dev_config['trend']['time_frames']}")
    
    # 示例3：验证配置
    print("验证配置...")
    try:
        validate_config(CUSTOM_TREND_CONFIG)
        print("✓ 配置验证通过")
    except ValueError as e:
        print(f"✗ 配置验证失败: {e}")
    
    print("配置示例演示完成")
