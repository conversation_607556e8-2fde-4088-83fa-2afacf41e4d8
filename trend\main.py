"""
趋势线策略分析主程序
对应Oracle gp_Trend_pkg.Trend_main存储过程
"""

import sys
import os
import logging
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List, Dict, Any, Optional
from datetime import datetime

# 添加路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'sw_py'))

try:
    from .config import TREND_CONFIG, THREADING_CONFIG, DEBUG_CONFIG
    from .models import TrendDatabaseManager
    from .trend_analyzer import TrendAnalyzer
except ImportError:
    from config import TREND_CONFIG, THREADING_CONFIG, DEBUG_CONFIG
    from models import TrendDatabaseManager
    from trend_analyzer import TrendAnalyzer

class TrendAnalysisMain:
    """趋势分析主程序"""
    
    def __init__(self):
        """初始化"""
        self.setup_logging()
        self.db_manager = TrendDatabaseManager()
        self.analyzer = TrendAnalyzer(self.db_manager)
        self.logger = logging.getLogger(__name__)
    
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.DEBUG if DEBUG_CONFIG['enable_detailed_logging'] else logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('trend_analysis.log'),
                logging.StreamHandler()
            ]
        )
    
    def run_analysis(self, subject: str = 'SZ_SH_TREND'):
        """
        运行趋势分析
        对应Oracle Trend_main存储过程
        """
        try:
            self.logger.info("开始趋势线策略分析")
            start_time = time.time()
            
            # 1. 初始化数据库表
            self._initialize_database()
            
            # 2. 清空结果表
            self.db_manager.truncate_result_table()
            
            # 3. 执行不同时间框架的分析
            time_frames = TREND_CONFIG['time_frames']
            for time_frame in time_frames:
                self.logger.info(f"开始分析时间框架: {time_frame}天")
                self._analyze_time_frame(time_frame)
            
            # 4. 数据同步到closing库（如果需要）
            # self._sync_to_closing_database()
            
            # 5. 发送邮件通知
            self._send_email_notification(subject)
            
            elapsed_time = time.time() - start_time
            self.logger.info(f"趋势分析完成，总耗时: {elapsed_time:.2f}秒")
            
        except Exception as e:
            self.logger.error(f"趋势分析失败: {e}")
            raise
    
    def _initialize_database(self):
        """初始化数据库表"""
        try:
            self.logger.info("初始化数据库表")
            self.db_manager.create_trend_result_table()
            self.db_manager.create_temp_table()
            self.logger.info("数据库表初始化完成")
        except Exception as e:
            self.logger.error(f"数据库初始化失败: {e}")
            raise
    
    def _analyze_time_frame(self, time_frame: int):
        """
        分析指定时间框架
        对应Oracle Trend_Analy存储过程调用
        """
        try:
            # 获取股票列表
            stock_list = self.db_manager.get_stock_list()
            if not stock_list:
                self.logger.warning("未找到股票数据")
                return
            
            self.logger.info(f"开始分析 {len(stock_list)} 只股票，时间框架: {time_frame}天")
            
            # 如果启用调试模式且指定了测试股票
            if DEBUG_CONFIG['test_single_stock']:
                stock_list = [{'gp_num': DEBUG_CONFIG['test_single_stock']}]
                self.logger.info(f"调试模式：仅分析股票 {DEBUG_CONFIG['test_single_stock']}")
            
            # 多线程分析
            self._analyze_stocks_multithread(stock_list, time_frame)
            
        except Exception as e:
            self.logger.error(f"时间框架 {time_frame} 分析失败: {e}")
            raise
    
    def _analyze_stocks_multithread(self, stock_list: List[Dict[str, Any]], time_frame: int):
        """多线程分析股票"""
        try:
            max_workers = THREADING_CONFIG['max_workers']
            chunk_size = THREADING_CONFIG['chunk_size']
            timeout = THREADING_CONFIG['timeout']
            
            # 分批处理
            stock_chunks = [stock_list[i:i + chunk_size] 
                           for i in range(0, len(stock_list), chunk_size)]
            
            successful_count = 0
            failed_count = 0
            
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                # 提交任务
                future_to_chunk = {
                    executor.submit(self._analyze_stock_chunk, chunk, time_frame, i): i
                    for i, chunk in enumerate(stock_chunks)
                }
                
                # 处理结果
                for future in as_completed(future_to_chunk, timeout=timeout):
                    chunk_id = future_to_chunk[future]
                    try:
                        chunk_result = future.result()
                        successful_count += chunk_result['successful']
                        failed_count += chunk_result['failed']
                        
                        self.logger.info(
                            f"批次 {chunk_id + 1}/{len(stock_chunks)} 完成，"
                            f"成功: {chunk_result['successful']}, "
                            f"失败: {chunk_result['failed']}"
                        )
                        
                    except Exception as e:
                        self.logger.error(f"批次 {chunk_id + 1} 处理失败: {e}")
                        failed_count += len(stock_chunks[chunk_id])
            
            self.logger.info(
                f"时间框架 {time_frame} 分析完成，"
                f"成功: {successful_count}, 失败: {failed_count}"
            )
            
        except Exception as e:
            self.logger.error(f"多线程分析失败: {e}")
            raise
    
    def _analyze_stock_chunk(self, stock_chunk: List[Dict[str, Any]], 
                           time_frame: int, chunk_id: int) -> Dict[str, int]:
        """分析股票批次"""
        successful = 0
        failed = 0
        
        for stock in stock_chunk:
            try:
                stock_code = stock['gp_num']
                
                # 分析单只股票
                result = self.analyzer.analyze_single_stock(stock_code, time_frame)
                
                if result:
                    # 插入自然数结果
                    natural_result = self._prepare_natural_result(result)
                    if self.db_manager.insert_trend_result(natural_result):
                        successful += 1
                    else:
                        failed += 1
                    
                    # 插入自然对数结果
                    log_result = self._prepare_logarithm_result(result)
                    if self.db_manager.insert_trend_result(log_result):
                        pass  # 已在上面计数
                    else:
                        self.logger.warning(f"股票 {stock_code} 自然对数结果插入失败")
                else:
                    failed += 1
                    
            except Exception as e:
                self.logger.error(f"分析股票 {stock.get('gp_num', 'unknown')} 失败: {e}")
                failed += 1
        
        return {'successful': successful, 'failed': failed}
    
    def _prepare_natural_result(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """准备自然数结果数据"""
        return {
            'gp_num': result['gp_num'],
            'dates_type': result['dates_type'],
            'down_a0': result.get('down_a0'),
            'down_a2': result.get('down_a2'),
            'point_a': result.get('point_a'),
            'point_b': result.get('point_b'),
            'top_a0': result.get('top_a0'),
            'top_a2': result.get('top_a2'),
            'tp_b0': result.get('tp_b0'),
            'up_a1': result.get('up_a1'),
            'up_b2': result.get('up_b2'),
            'point_c': result.get('point_c'),
            'point_d': result.get('point_d'),
            'low_a1': result.get('low_a1'),
            'low_b2': result.get('low_b2'),
            'date_b1': result.get('date_b1'),
            'attribute1': '自然数',
            'attribute2': result.get('attribute2', 'N')
        }
    
    def _prepare_logarithm_result(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """准备自然对数结果数据"""
        return {
            'gp_num': result['gp_num'],
            'dates_type': result['dates_type'],
            'down_a0': result.get('down_a0'),
            'down_a2': result.get('ln_down_a2'),
            'point_a': result.get('ln_point_a'),
            'point_b': result.get('ln_point_b'),
            'top_a0': result.get('top_a0'),
            'top_a2': result.get('top_a2'),
            'tp_b0': result.get('ln_tp_b0'),
            'up_a1': result.get('up_a1'),
            'up_b2': result.get('up_b2'),
            'point_c': result.get('ln_point_c'),
            'point_d': result.get('ln_point_d'),
            'low_a1': result.get('low_a1'),
            'low_b2': result.get('low_b2'),
            'date_b1': result.get('date_b1'),
            'attribute1': '自然对数',
            'attribute2': result.get('ln_attribute2', 'N')
        }
    
    def _send_email_notification(self, subject: str):
        """发送邮件通知"""
        try:
            # 构建查询SQL
            sql_query = """
            SELECT 
                gp_num as '股票代码',
                dates_type as '时间框架',
                down_a0 as 'A0下降趋势第一高点',
                down_a2 as 'A2下降趋势第二高点',
                point_a as '系数A',
                point_b as '系数B',
                top_a0 as 'A0最高价',
                top_a2 as 'A2最高价',
                tp_b0 as 'B0下降趋势确认',
                up_a1 as 'A1下降趋势拐点',
                up_b2 as 'B2上升趋势第二低点',
                point_c as '系数C',
                point_d as '系数D',
                low_a1 as 'A1最低价',
                low_b2 as 'B2最低价',
                date_b1 as 'B1上升趋势最高点',
                attribute1 as '类型',
                attribute2 as 'flag'
            FROM gp_trend_result
            """
            
            # 这里应该调用邮件发送模块
            # 由于依赖gp_mail_pkg，暂时记录日志
            self.logger.info(f"准备发送邮件通知，主题: {subject}")
            self.logger.info(f"查询SQL: {sql_query}")
            
            # TODO: 实现邮件发送功能
            # gp_mail_pkg.MAIN(P_JYS=subject, p_str=sql_query)
            
        except Exception as e:
            self.logger.error(f"邮件通知发送失败: {e}")

def main():
    """主函数"""
    try:
        analyzer = TrendAnalysisMain()
        analyzer.run_analysis()
    except Exception as e:
        logging.error(f"程序执行失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
