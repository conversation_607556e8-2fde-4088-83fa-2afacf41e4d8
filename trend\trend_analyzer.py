"""
趋势线策略分析器
对应Oracle gp_Trend_pkg的核心分析逻辑
"""

import sys
import os
import logging
import math
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, date
from dataclasses import dataclass

# 添加路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'sw_py'))

try:
    from .config import TREND_CONFIG, DEBUG_CONFIG, MATH_CONFIG
    from .models import TrendDatabaseManager
except ImportError:
    from config import TREND_CONFIG, DEBUG_CONFIG, MATH_CONFIG
    from models import TrendDatabaseManager

@dataclass
class TrendPoint:
    """趋势点数据结构"""
    date: date
    price: float
    row_num: int
    
@dataclass
class TrendCoefficients:
    """趋势系数数据结构"""
    point_a: float
    point_b: float
    point_c: Optional[float] = None
    point_d: Optional[float] = None

class TrendLineValidator:
    """趋势线验证器 - 对应Oracle check_tl函数"""
    
    def __init__(self, db_manager: TrendDatabaseManager):
        self.db = db_manager
        self.logger = logging.getLogger(__name__)
    
    def check_trend_line(self, row_num: int, price: float) -> bool:
        """
        检查趋势线有效性
        对应Oracle check_tl函数
        """
        try:
            if not self.db.db:
                # 模拟模式：随机返回验证结果
                import random
                return random.choice([True, False])

            sql = """
            SELECT 1 FROM gp_jy_d_tmp
            WHERE row_num BETWEEN %s AND %s
            AND close_price <= %s
            LIMIT 1
            """

            check_days = TREND_CONFIG['trend_validation']['check_days']
            price_threshold = price * TREND_CONFIG['trend_validation']['price_tolerance']

            result = self.db.db.execute_query(sql, (
                row_num + 1,
                row_num + check_days,
                price_threshold
            ))

            # 如果没有找到违反条件的记录，返回True
            return len(result) == 0

        except Exception as e:
            self.logger.error(f"趋势线验证失败: {e}")
            return False

class TrendAnalyzer:
    """趋势分析器 - 对应Oracle Trend_Analy存储过程"""
    
    def __init__(self, db_manager: TrendDatabaseManager):
        self.db = db_manager
        self.validator = TrendLineValidator(db_manager)
        self.logger = logging.getLogger(__name__)
    
    def analyze_single_stock(self, stock_code: str, time_frame: int) -> Dict[str, Any]:
        """
        分析单只股票的趋势
        对应Oracle Trend_Analy存储过程的主要逻辑
        """
        try:
            # 模拟模式检查
            if not self.db.db:
                return self._generate_mock_result(stock_code, time_frame)

            # 1. 加载股票数据到临时表
            self.db.load_stock_data_to_temp(stock_code)

            # 2. 获取总行数
            total_num = self.db.get_max_row_num()
            if total_num < time_frame + 1:
                self.logger.warning(f"股票 {stock_code} 数据不足，跳过分析")
                return {}

            # 3. 下降趋势分析
            down_trend_result = self._analyze_down_trend(stock_code, time_frame, total_num)
            if not down_trend_result:
                return {}

            # 4. 突破分析
            breakout_result = self._analyze_breakout(down_trend_result, total_num)

            # 5. 上升趋势分析
            up_trend_result = self._analyze_up_trend(down_trend_result, total_num)

            # 6. 合并结果
            result = {
                'gp_num': stock_code,
                'dates_type': time_frame,
                **down_trend_result,
                **breakout_result,
                **up_trend_result
            }

            return result

        except Exception as e:
            self.logger.error(f"分析股票 {stock_code} 失败: {e}")
            return {}

    def _generate_mock_result(self, stock_code: str, time_frame: int) -> Dict[str, Any]:
        """生成模拟分析结果"""
        from datetime import date, timedelta
        import random

        base_date = date.today() - timedelta(days=time_frame)

        return {
            'gp_num': stock_code,
            'dates_type': time_frame,
            'down_a0': base_date - timedelta(days=50),
            'down_a2': base_date - timedelta(days=20),
            'point_a': round(random.uniform(-0.1, -0.01), 8),
            'point_b': round(random.uniform(10, 50), 8),
            'top_a0': round(random.uniform(10, 30), 4),
            'top_a2': round(random.uniform(8, 25), 4),
            'tp_b0': base_date + timedelta(days=10),
            'up_a1': base_date - timedelta(days=30),
            'up_b2': base_date + timedelta(days=20),
            'point_c': round(random.uniform(0.01, 0.1), 8),
            'point_d': round(random.uniform(-50, -10), 8),
            'low_a1': round(random.uniform(5, 15), 4),
            'low_b2': round(random.uniform(6, 16), 4),
            'date_b1': base_date + timedelta(days=30),
            'attribute2': random.choice(['Y', 'N']),
            'ln_down_a2': base_date - timedelta(days=18),
            'ln_point_a': round(random.uniform(-0.05, -0.005), 8),
            'ln_point_b': round(random.uniform(2, 4), 8),
            'ln_tp_b0': base_date + timedelta(days=12),
            'ln_point_c': round(random.uniform(0.005, 0.05), 8),
            'ln_point_d': round(random.uniform(-4, -2), 8),
            'ln_attribute2': random.choice(['Y', 'N'])
        }
    
    def _analyze_down_trend(self, stock_code: str, time_frame: int, total_num: int) -> Dict[str, Any]:
        """
        分析下降趋势
        对应Oracle代码中的下降趋势分析部分
        """
        try:
            # 获取最低的high_price及对应jy_date (对应Oracle l_min_price1逻辑)
            sql_min_high = """
            SELECT MIN(high_price) as min_high_price
            FROM gp_jy_d_tmp 
            WHERE row_num > %s
            """
            result = self.db.db.execute_query(sql_min_high, (total_num - (time_frame + 1),))
            if not result:
                return {}
            
            min_high_price = result[0]['min_high_price']
            
            # 获取对应的日期和行号 (对应Oracle l_up_a1, l_min_num1, l_upmin_price)
            sql_min_point = """
            SELECT jy_date, row_num, low_price
            FROM gp_jy_d_tmp 
            WHERE row_num > %s 
            AND high_price = %s
            ORDER BY row_num
            LIMIT 1
            """
            result = self.db.db.execute_query(sql_min_point, (total_num - (time_frame + 1), min_high_price))
            if not result:
                return {}
            
            up_a1 = result[0]['jy_date']
            min_num1 = result[0]['row_num']
            upmin_price = result[0]['low_price']
            
            # 获取最高high_price及jy_date (对应Oracle l_top_a0逻辑)
            sql_max_high = """
            SELECT MAX(high_price) as max_high_price
            FROM gp_jy_d_tmp 
            WHERE row_num > %s 
            AND jy_date <= %s
            """
            result = self.db.db.execute_query(sql_max_high, (total_num - (time_frame + 1), up_a1))
            if not result:
                return {}
            
            top_a0 = result[0]['max_high_price']
            
            # 获取对应的日期和行号 (对应Oracle l_down_a0, l_max_num0, l_low_a1)
            sql_max_point = """
            SELECT jy_date, row_num, low_price
            FROM gp_jy_d_tmp 
            WHERE row_num > %s 
            AND jy_date <= %s 
            AND high_price = %s
            ORDER BY row_num
            LIMIT 1
            """
            result = self.db.db.execute_query(sql_max_point, (total_num - (time_frame + 1), up_a1, top_a0))
            if not result:
                return {}
            
            down_a0 = result[0]['jy_date']
            max_num0 = result[0]['row_num']
            low_a1 = result[0]['low_price']
            
            # 查找验证斜率最低点 (对应Oracle斜率计算逻辑)
            slope_result = self._calculate_slope(down_a0, up_a1, top_a0, max_num0)
            
            return {
                'down_a0': down_a0,
                'up_a1': up_a1,
                'top_a0': top_a0,
                'low_a1': low_a1,
                'min_num1': min_num1,
                'max_num0': max_num0,
                'upmin_price': upmin_price,
                **slope_result
            }
            
        except Exception as e:
            self.logger.error(f"下降趋势分析失败: {e}")
            return {}
    
    def _calculate_slope(self, down_a0: date, up_a1: date, top_a0: float, max_num0: int) -> Dict[str, Any]:
        """
        计算斜率
        对应Oracle代码中的斜率计算逻辑
        """
        try:
            # 查找验证斜率最低点的数据
            sql = """
            SELECT jy_date, row_num, high_price
            FROM gp_jy_d_tmp 
            WHERE jy_date BETWEEN DATE_ADD(%s, INTERVAL 20 DAY) AND %s
            ORDER BY jy_date
            """
            
            records = self.db.db.execute_query(sql, (down_a0, up_a1))
            if not records:
                return {}
            
            # 初始化变量 (对应Oracle变量初始化)
            aa1 = 0
            ln_aa1 = 0
            aa2 = 0
            ln_aa2 = 0
            ldate = None
            ln_ldate = None
            point_b = 0
            ln_point_b = 0
            top_a2 = 0
            
            for i, rec in enumerate(records):
                try:
                    if i == 0:
                        # 第一条记录 (对应Oracle i = 1逻辑)
                        aa1 = (top_a0 - rec['high_price']) / (max_num0 - rec['row_num'])
                        ln_aa1 = (math.log(top_a0) - math.log(rec['high_price'])) / (max_num0 - rec['row_num'])
                        
                        ldate = rec['jy_date']
                        ln_ldate = rec['jy_date']
                        
                        point_b = top_a0 - max_num0 * aa1
                        ln_point_b = math.log(top_a0) - max_num0 * aa1
                        
                    else:
                        # 后续记录 (对应Oracle else逻辑)
                        aa2 = (top_a0 - rec['high_price']) / (max_num0 - rec['row_num'])
                        ln_aa2 = (math.log(top_a0) - math.log(rec['high_price'])) / (max_num0 - rec['row_num'])
                        
                        # 自然数斜率比较
                        if aa1 < aa2:
                            aa1 = aa2
                            aa2 = 0
                            if aa1 != 0:
                                point_b = top_a0 - max_num0 * aa1
                            ldate = rec['jy_date']
                            top_a2 = rec['high_price']
                        
                        # 自然对数斜率比较
                        if ln_aa1 < ln_aa2:
                            ln_aa1 = ln_aa2
                            ln_ldate = rec['jy_date']
                            if ln_aa1 != 0:
                                ln_point_b = math.log(top_a0) - max_num0 * ln_aa1
                            top_a2 = rec['high_price']
                            ln_aa2 = 0
                            
                except Exception as e:
                    self.logger.error(f"斜率计算错误: {e}")
                    continue
            
            return {
                'down_a2': ldate,
                'point_a': aa1,
                'point_b': point_b,
                'top_a2': top_a2,
                'ln_down_a2': ln_ldate,
                'ln_point_a': ln_aa1,
                'ln_point_b': ln_point_b
            }
            
        except Exception as e:
            self.logger.error(f"斜率计算失败: {e}")
            return {}

    def _analyze_breakout(self, down_trend_result: Dict[str, Any], total_num: int) -> Dict[str, Any]:
        """
        分析突破
        对应Oracle代码中的突破分析部分
        """
        try:
            if not down_trend_result.get('point_a'):
                return {}

            point_a = down_trend_result['point_a']
            point_b = down_trend_result['point_b']
            min_num1 = down_trend_result['min_num1']

            # 自然数突破分析
            tp_b0, avgflag = self._check_breakout_natural(point_a, point_b, min_num1)

            # 自然对数突破分析
            ln_point_a = down_trend_result.get('ln_point_a')
            ln_point_b = down_trend_result.get('ln_point_b')
            ln_tp_b0, ln_avgflag = self._check_breakout_logarithm(ln_point_a, ln_point_b, min_num1)

            return {
                'tp_b0': tp_b0,
                'attribute2': avgflag,
                'ln_tp_b0': ln_tp_b0,
                'ln_attribute2': ln_avgflag
            }

        except Exception as e:
            self.logger.error(f"突破分析失败: {e}")
            return {}

    def _check_breakout_natural(self, point_a: float, point_b: float, min_num1: int) -> Tuple[Optional[date], str]:
        """
        检查自然数突破
        对应Oracle自然数突破检查逻辑
        """
        try:
            sql = """
            SELECT dt.*, (%(point_a)s * dt.row_num + %(point_b)s) as l_ypoint
            FROM gp_jy_d_tmp dt
            WHERE dt.row_num > %(min_num1)s
            ORDER BY dt.row_num
            """

            records = self.db.db.execute_query(sql, {
                'point_a': point_a,
                'point_b': point_b,
                'min_num1': min_num1
            })

            tp_i = 0
            avg_trade = 0

            for rec in records:
                if DEBUG_CONFIG['print_debug_info']:
                    self.logger.debug(f"{rec['gp_num']} {rec['jy_date']}")

                if tp_i == 0:
                    # 计算平均交易量
                    avg_trade = self.db.calculate_avg_volume(
                        rec['gp_num'],
                        rec['jy_date'],
                        TREND_CONFIG['avg_volume']['calculation_days']
                    )

                    if DEBUG_CONFIG['print_debug_info']:
                        self.logger.debug(f"平均交易量: {avg_trade}")

                    # 获取前一日收盘价和趋势价格
                    prev_sql = """
                    SELECT close_price, (%(point_a)s * row_num + %(point_b)s) as trend_price
                    FROM gp_jy_d_tmp
                    WHERE row_num = %(row_num)s
                    """
                    prev_result = self.db.db.execute_query(prev_sql, {
                        'point_a': point_a,
                        'point_b': point_b,
                        'row_num': rec['row_num'] - 1
                    })

                    if prev_result:
                        pre_close = prev_result[0]['close_price']
                        trend_price = prev_result[0]['trend_price']

                        # 检查交易量和价格条件
                        volume_multiplier = TREND_CONFIG['avg_volume']['volume_multiplier']
                        if rec['jy_quantity'] >= avg_trade * volume_multiplier:
                            if (rec['close_price'] > rec['l_ypoint'] and
                                pre_close < trend_price):
                                tp_i += 1
                            else:
                                tp_i = 0
                        else:
                            tp_i = 0
                else:
                    # 后续突破检查
                    price_threshold = TREND_CONFIG['breakout_confirmation']['price_threshold']
                    if rec['close_price'] > rec['l_ypoint'] * price_threshold:
                        tp_i += 1
                    else:
                        tp_i = 0

                # 检查是否达到突破确认天数
                if tp_i == TREND_CONFIG['breakout_confirmation']['consecutive_days']:
                    return rec['jy_date'], 'Y'

            return None, 'N'

        except Exception as e:
            self.logger.error(f"自然数突破检查失败: {e}")
            return None, 'N'

    def _check_breakout_logarithm(self, ln_point_a: Optional[float], ln_point_b: Optional[float],
                                  min_num1: int) -> Tuple[Optional[date], str]:
        """
        检查自然对数突破
        对应Oracle自然对数突破检查逻辑
        """
        try:
            if not ln_point_a or not ln_point_b:
                return None, 'N'

            natural_log_base = MATH_CONFIG['natural_log_base']
            precision = MATH_CONFIG['precision']

            sql = """
            SELECT dt.*,
                   ROUND(POWER(%(base)s,
                              (ROUND(%(ln_point_a)s * dt.row_num, %(precision)s) +
                               ROUND(%(ln_point_b)s, %(precision)s))),
                         %(precision)s) as ln_ypoint
            FROM gp_jy_d_tmp dt
            WHERE dt.row_num > %(min_num1)s
            ORDER BY dt.row_num
            """

            records = self.db.db.execute_query(sql, {
                'base': natural_log_base,
                'ln_point_a': ln_point_a,
                'ln_point_b': ln_point_b,
                'precision': precision,
                'min_num1': min_num1
            })

            tp_i = 0
            price_threshold = TREND_CONFIG['breakout_confirmation']['price_threshold']

            for rec in records:
                if rec['close_price'] > rec['ln_ypoint'] * price_threshold:
                    tp_i += 1
                else:
                    tp_i = 0

                if tp_i == TREND_CONFIG['breakout_confirmation']['consecutive_days']:
                    # 计算平均交易量（虽然Oracle中已注释，但保留逻辑）
                    return rec['jy_date'], 'Y'

            return None, 'N'

        except Exception as e:
            self.logger.error(f"自然对数突破检查失败: {e}")
            return None, 'N'

    def _analyze_up_trend(self, down_trend_result: Dict[str, Any], total_num: int) -> Dict[str, Any]:
        """
        分析上升趋势
        对应Oracle代码中的上升趋势分析部分
        """
        try:
            min_num1 = down_trend_result['min_num1']
            upmin_price = down_trend_result['upmin_price']

            # 获取上升最高价及对应日期
            sql_max_high = """
            SELECT MAX(high_price) as upmax_price
            FROM gp_jy_d_tmp
            WHERE row_num > %s
            """
            result = self.db.db.execute_query(sql_max_high, (min_num1,))
            if not result:
                return {}

            upmax_price = result[0]['upmax_price']

            # 获取对应的日期和行号
            sql_max_point = """
            SELECT jy_date, row_num
            FROM gp_jy_d_tmp
            WHERE row_num > %s
            AND high_price = %s
            ORDER BY row_num
            LIMIT 1
            """
            result = self.db.db.execute_query(sql_max_point, (min_num1, upmax_price))
            if not result:
                return {'date_b1': None, 'upmax_num': 0}

            date_b1 = result[0]['jy_date']
            upmax_num = result[0]['row_num']

            # 获取最小斜率上升趋势线
            up_b2 = None
            point_c = None
            ln_point_c = None
            low_b2 = None
            point_d = None
            ln_point_d = None

            min_gap_days = TREND_CONFIG['uptrend_analysis']['min_gap_days']
            if (min_num1 + min_gap_days) <= upmax_num:
                sql_uptrend = """
                SELECT jt.jy_date,
                       (%(upmin_price)s - jt.low_price) / (%(min_num1)s - jt.row_num) as l_rate,
                       (LN(%(upmin_price)s) - LN(jt.low_price)) / (%(min_num1)s - jt.row_num) as ln_rate,
                       jt.low_price,
                       (jt.low_price - ((%(upmin_price)s - jt.low_price) / (%(min_num1)s - jt.row_num)) * jt.row_num) as point_d_calc,
                       (LN(jt.low_price) - ((LN(%(upmin_price)s) - LN(jt.low_price)) / (%(min_num1)s - jt.row_num)) * jt.row_num) as ln_point_d_calc
                FROM gp_jy_d_tmp jt
                WHERE jt.row_num BETWEEN %(min_start)s AND %(upmax_num)s
                ORDER BY (%(upmin_price)s - jt.low_price) / (%(min_num1)s - jt.row_num)
                LIMIT 1
                """

                result = self.db.db.execute_query(sql_uptrend, {
                    'upmin_price': upmin_price,
                    'min_num1': min_num1,
                    'min_start': min_num1 + min_gap_days,
                    'upmax_num': upmax_num
                })

                if result:
                    record = result[0]
                    up_b2 = record['jy_date']
                    point_c = record['l_rate']
                    ln_point_c = record['ln_rate']
                    low_b2 = record['low_price']
                    point_d = record['point_d_calc']
                    ln_point_d = record['ln_point_d_calc']

            return {
                'up_b2': up_b2,
                'point_c': point_c,
                'ln_point_c': ln_point_c,
                'low_b2': low_b2,
                'point_d': point_d,
                'ln_point_d': ln_point_d,
                'date_b1': date_b1
            }

        except Exception as e:
            self.logger.error(f"上升趋势分析失败: {e}")
            return {}
