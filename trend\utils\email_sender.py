"""
趋势分析邮件发送工具
参考sw_py的邮件发送逻辑，适配trend模块
"""

import os
import smtplib
import logging
from datetime import datetime
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.mime.base import MIMEBase
from email import encoders
from typing import Dict, Any, List


class TrendEmailSender:
    """趋势分析邮件发送器"""
    
    def __init__(self, email_config: Dict[str, Any], logger: logging.Logger = None):
        """
        初始化邮件发送器
        
        Args:
            email_config: 邮件配置
            logger: 日志记录器
        """
        self.email_config = email_config
        self.logger = logger or logging.getLogger(__name__)
    
    def send_analysis_report(self, 
                           csv_filepath: str = None,
                           excel_filepath: str = None,
                           total_stocks: int = 0, 
                           found_patterns: int = 0,
                           time_frames: List[int] = None,
                           custom_subject: str = None) -> bool:
        """
        发送趋势分析报告邮件
        
        Args:
            csv_filepath: CSV文件路径
            excel_filepath: Excel文件路径
            total_stocks: 分析股票总数
            found_patterns: 发现的趋势信号数量
            time_frames: 时间框架列表
            custom_subject: 自定义邮件主题
            
        Returns:
            bool: 发送是否成功
        """
        try:
            # 检查是否启用邮件功能
            if not self.email_config.get('enable_email', False):
                self.logger.info("邮件发送功能已禁用")
                return False

            # 检查邮件配置
            if not self._validate_email_config():
                return False

            # 生成邮件内容
            subject, body = self._generate_email_content(
                total_stocks, found_patterns, time_frames, custom_subject
            )

            # 创建邮件对象
            msg = MIMEMultipart()
            msg['From'] = self.email_config['from_email'] or self.email_config['username']
            msg['To'] = ', '.join(self.email_config['to_emails'])
            msg['Subject'] = subject

            # 添加邮件正文
            msg.attach(MIMEText(body, 'plain', 'utf-8'))
            
            # 添加附件
            if csv_filepath and os.path.exists(csv_filepath):
                self._attach_file(msg, csv_filepath)
            
            if excel_filepath and os.path.exists(excel_filepath):
                self._attach_file(msg, excel_filepath)
            
            # 发送邮件
            return self._send_email(msg)
            
        except Exception as e:
            self.logger.error(f"发送趋势分析报告邮件失败: {str(e)}")
            return False
    
    def _validate_email_config(self) -> bool:
        """验证邮件配置"""
        if not self.email_config.get('username') or not self.email_config.get('password'):
            self.logger.warning("邮件配置不完整，跳过发送")
            return False

        if not self.email_config.get('to_emails'):
            self.logger.warning("未配置收件人，跳过发送")
            return False
        
        return True
    
    def _generate_email_content(self, 
                              total_stocks: int, 
                              found_patterns: int,
                              time_frames: List[int] = None,
                              custom_subject: str = None) -> tuple:
        """
        生成邮件内容
        
        Returns:
            tuple: (subject, body)
        """
        current_date = datetime.now().strftime('%Y-%m-%d')
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        # 处理时间框架
        if time_frames:
            framework_str = f"{'/'.join(map(str, time_frames))}日"
        else:
            framework_str = "多时间框架"
        
        # 生成邮件主题
        if custom_subject:
            subject = custom_subject
        else:
            subject = f"{current_date} 趋势线策略分析结果 - {framework_str}框架"
        
        # 生成邮件正文
        body = f"""各位好！

附件是{current_date}的趋势线策略分析数据，请查看。

本次分析概况：
- 分析时间框架：{framework_str}
- 分析股票总数：{total_stocks}
- 发现趋势信号：{found_patterns}个
- 分析完成时间：{current_time}

趋势线策略说明：
- 基于{framework_str}时间框架进行趋势线分析
- 识别下降趋势线突破信号
- 验证交易量配合情况
- 确认上升趋势延续性

更多精彩敬请期待！

此邮件由趋势分析系统自动发送。
"""
        
        return subject, body
    
    def _attach_file(self, msg: MIMEMultipart, filepath: str):
        """添加文件附件"""
        try:
            with open(filepath, "rb") as attachment:
                part = MIMEBase('application', 'octet-stream')
                part.set_payload(attachment.read())
            
            encoders.encode_base64(part)
            part.add_header(
                'Content-Disposition',
                f'attachment; filename= {os.path.basename(filepath)}'
            )
            msg.attach(part)
            
            self.logger.info(f"已添加附件: {os.path.basename(filepath)}")
            
        except Exception as e:
            self.logger.warning(f"添加附件失败 {filepath}: {e}")
    
    def _send_email(self, msg: MIMEMultipart) -> bool:
        """发送邮件"""
        try:
            # 连接SMTP服务器
            if self.email_config.get('use_ssl', True):
                server = smtplib.SMTP_SSL(
                    self.email_config['smtp_server'], 
                    self.email_config['smtp_port']
                )
            else:
                server = smtplib.SMTP(
                    self.email_config['smtp_server'], 
                    self.email_config['smtp_port']
                )
                server.starttls()
            
            # 登录并发送
            server.login(self.email_config['username'], self.email_config['password'])
            text = msg.as_string()
            server.sendmail(
                self.email_config['username'], 
                self.email_config['to_emails'], 
                text
            )
            server.quit()
            
            self.logger.info(f"邮件发送成功，收件人: {self.email_config['to_emails']}")
            return True
            
        except Exception as e:
            self.logger.error(f"邮件发送失败: {str(e)}")
            return False
